import type { Metada<PERSON> } from "next"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { BugReportForm } from "@/components/bug-report-form"
import { Badge } from "@/components/ui/badge"
import { MessageSquare, ExternalLink, Github } from "lucide-react"

export const metadata: Metadata = {
  title: "關於我們 | AILogora",
  description: "AILogora是一個專注於AI技術的中文社群平台，致力於促進知識分享和深度討論。",
}

export default function AboutPage() {
  return (
    <div className="container mx-auto py-6 space-y-8">
      {/* 頁面標題 */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">關於 AILogora</h1>
        <Badge variant="outline" className="bg-primary/10 text-primary">
          Beta
        </Badge>
      </div>

      {/* 平台理念 */}
      <section className="space-y-6">
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
              <div className="space-y-4">
                <h2 className="text-2xl font-bold">為什麼創建 AILogora</h2>
                <p className="text-muted-foreground">
                  我們創建 AILogora 是因為我們相信，在 AI
                  技術快速發展的今天，需要一個專注於中文內容的平台，讓實踐者能夠分享觀點、交流經驗。
                </p>
                <p className="text-muted-foreground">
                  在資訊爆炸的時代，我們發現結構化的知識分享和深度討論才是最有價值的。通過觀點卡片化的形式，我們希望讓複雜的
                  AI 知識變得更易於理解和傳播。
                </p>
                <p className="text-muted-foreground">
                  我們正在打造一個由社群驅動的平台，每一位參與者都是這個平台的共同創造者。無論是分享觀點、參與討論，還是提供反饋，您的每一次參與都在幫助這個社群成長。
                </p>
              </div>
              <div className="flex justify-center">
                <div className="relative w-full max-w-sm">
                  <Image
                    src="/diverse-team-collaboration.png"
                    alt="AILogora 願景"
                    width={300}
                    height={300}
                    className="rounded-lg shadow-md"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* 我們的願景 */}
      <section className="space-y-4">
        <h2 className="text-2xl font-bold">我們的願景</h2>
        <Card>
          <CardContent className="pt-6">
            <p className="text-muted-foreground mb-4">我們希望 AILogora 成為：</p>
            <ul className="space-y-3 text-muted-foreground">
              <li className="flex items-start gap-2">
                <span className="text-primary font-bold">•</span>
                <span>一個高質量的 AI 技術中文社群，讓知識分享變得更有結構和深度</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary font-bold">•</span>
                <span>一個鼓勵多元觀點碰撞的平台，通過集體智慧解決 AI 領域的複雜問題</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary font-bold">•</span>
                <span>一個注重實踐的社群，幫助開發者和研究者將理論轉化為實際應用</span>
              </li>
            </ul>
          </CardContent>
        </Card>
      </section>

      {/* 發展路線圖 */}
      <section className="space-y-4">
        <h2 className="text-2xl font-bold">我們的進展</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="border-l-4 border-l-primary">
            <CardHeader className="pb-2">
              <div className="flex items-center gap-2">
                <Badge variant="default" className="w-16 justify-center">
                  現在
                </Badge>
                <CardTitle className="text-lg">MVP 階段</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                我們正在開發核心功能，包括觀點卡發布、主題分類和基礎互動功能。這是我們的第一步，但絕不是最後一步。
              </p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-muted">
            <CardHeader className="pb-2">
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="w-16 justify-center">
                  下一步
                </Badge>
                <CardTitle className="text-lg">社群互動</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                我們計劃增加更豐富的互動功能，讓社群成員之間的交流更加順暢和有價值。
              </p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-muted">
            <CardHeader className="pb-2">
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="w-16 justify-center">
                  規劃中
                </Badge>
                <CardTitle className="text-lg">知識圖譜</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                構建 AI 知識圖譜，提供個性化內容推薦，幫助用戶發現更多相關內容。
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* 問題回報與聯絡 */}
      <section className="space-y-4">
        <h2 className="text-2xl font-bold">聯絡我們</h2>

        <Tabs defaultValue="contact" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="contact">聯絡方式</TabsTrigger>
            <TabsTrigger value="bug-report">回報問題</TabsTrigger>
          </TabsList>

          <TabsContent value="contact" className="pt-4">
            <Card>
              <CardContent className="pt-6 space-y-4">
                <p className="text-muted-foreground">
                  我們是一個小團隊，但我們重視每一位社群成員的聲音。有任何想法、建議或合作意向，都歡迎與我們聯絡。
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h3 className="font-medium flex items-center gap-2">
                      <MessageSquare className="h-4 w-4" />
                      電子郵件
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      <a href="mailto:<EMAIL>" className="hover:underline">
                        <EMAIL>
                      </a>
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium flex items-center gap-2">
                      <Github className="h-4 w-4" />
                      GitHub
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      <a
                        href="https://github.com/ailogora"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="hover:underline flex items-center gap-1"
                      >
                        github.com/ailogora
                        <ExternalLink className="h-3 w-3" />
                      </a>
                    </p>
                  </div>
                </div>

                <div className="pt-2">
                  <Button variant="outline" asChild>
                    <Link href="https://discord.gg/ailogora" target="_blank" rel="noopener noreferrer">
                      加入我們的 Discord 社群
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="bug-report" className="pt-4">
            <Card>
              <CardHeader>
                <CardTitle>回報問題或建議</CardTitle>
                <CardDescription>幫助我們改進平台，回報任何您遇到的問題或提出建議。</CardDescription>
              </CardHeader>
              <CardContent>
                <BugReportForm />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </section>
    </div>
  )
}
