'use server'

import { createServerClient } from '@/lib/supabase/server'
import { cookies } from 'next/headers'

export async function getUser() {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)
    try {
        const { safeGetUser } = await import('@/lib/api-utils')
        const { user, error } = await safeGetUser(supabase)
        if (error) {
            console.error('Error getting user:', error)
            return null
        }
        return user
    } catch (error: any) {
        console.error('Error getting user:', error)
        return null
    }
}

export async function signOut() {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)
    try {
        const { error } = await supabase.auth.signOut()
        if (error) {
            console.error('Error signing out:', error)
            throw error
        }
    } catch (error: any) {
        console.error('Error signing out:', error)
        throw error
    }
} 