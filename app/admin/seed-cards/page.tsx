"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { seedCards } from "@/lib/seed-cards"
import { Loader2 } from "lucide-react"

export default function SeedCardsPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<any>(null)

  async function handleSeedCards() {
    setIsLoading(true)
    try {
      const seedResult = await seedCards()
      setResult(seedResult)
    } catch (error) {
      console.error("導入卡片時出錯:", error)
      setResult({ success: false, error })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-10">
      <Card>
        <CardHeader>
          <CardTitle>導入模擬卡片數據</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p>點擊下方按鈕將導入模擬卡片數據到資料庫。這將創建示例卡片並關聯到現有的主題和子主題。</p>
            <Button onClick={handleSeedCards} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  正在導入...
                </>
              ) : (
                "導入卡片數據"
              )}
            </Button>

            {result && (
              <div className="mt-4 p-4 border rounded-md bg-gray-50">
                <h3 className="font-medium mb-2">{result.success ? "導入成功" : "導入失敗"}</h3>
                {result.success && <p>成功創建了 {result.created} 個卡片。</p>}
                {result.errors && result.errors.length > 0 && (
                  <div>
                    <p className="text-red-500">發生了 {result.errors.length} 個錯誤：</p>
                    <ul className="list-disc pl-5">
                      {result.errors.map((err: any, index: number) => (
                        <li key={index}>
                          {err.card}: {err.error.message || JSON.stringify(err.error)}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
