"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>oot<PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { seedTopics } from "@/lib/seed-topics"
import { getAllTopicsWithSubtopics } from "@/lib/topic-service"
import { Loader2 } from "lucide-react"

export default function SeedTopicsPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<{ success: boolean; message?: string; error?: any } | null>(null)
  const [topics, setTopics] = useState([])

  async function handleSeedTopics() {
    setIsLoading(true)
    try {
      const seedResult = await seedTopics()
      setResult(seedResult)

      // 如果成功導入，刷新主題列表
      if (seedResult.success) {
        const topicsResult = await getAllTopicsWithSubtopics()
        if (topicsResult.success && topicsResult.data) {
          setTopics(topicsResult.data)
        }
      }
    } catch (error) {
      console.error("導入主題時出錯:", error)
      setResult({ success: false, error })
    } finally {
      setIsLoading(false)
    }
  }

  async function handleLoadTopics() {
    setIsLoading(true)
    try {
      const topicsResult = await getAllTopicsWithSubtopics()
      if (topicsResult.success && topicsResult.data) {
        setTopics(topicsResult.data)
        setResult({ success: true, message: "成功載入主題資料" })
      } else {
        setResult({ success: false, error: topicsResult.error })
      }
    } catch (error) {
      console.error("載入主題時出錯:", error)
      setResult({ success: false, error })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">主題資料管理</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>導入主題資料</CardTitle>
            <CardDescription>將模擬主題資料導入到資料庫中</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              點擊下方按鈕將模擬主題資料導入到資料庫中。如果資料庫中已有主題資料，將不會重複導入。
            </p>
          </CardContent>
          <CardFooter>
            <Button onClick={handleSeedTopics} disabled={isLoading} className="w-full">
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  導入中...
                </>
              ) : (
                "導入主題資料"
              )}
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>載入主題資料</CardTitle>
            <CardDescription>從資料庫中載入主題資料</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              點擊下方按鈕從資料庫中載入主題資料，查看目前資料庫中的主題。
            </p>
          </CardContent>
          <CardFooter>
            <Button onClick={handleLoadTopics} disabled={isLoading} variant="outline" className="w-full">
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  載入中...
                </>
              ) : (
                "載入主題資料"
              )}
            </Button>
          </CardFooter>
        </Card>
      </div>

      {result && (
        <div
          className={`p-4 mb-6 rounded-md ${result.success ? "bg-green-50 text-green-700" : "bg-red-50 text-red-700"}`}
        >
          {result.success ? (
            <p>{result.message || "操作成功"}</p>
          ) : (
            <div>
              <p>操作失敗</p>
              {result.error && (
                <pre className="mt-2 text-xs overflow-auto">{JSON.stringify(result.error, null, 2)}</pre>
              )}
            </div>
          )}
        </div>
      )}

      {topics.length > 0 && (
        <div className="space-y-6">
          <h2 className="text-2xl font-bold">主題列表</h2>
          <div className="space-y-4">
            {topics.map((topic: any) => (
              <Card key={topic.id}>
                <CardHeader>
                  <CardTitle>{topic.name}</CardTitle>
                  <CardDescription>{topic.description}</CardDescription>
                </CardHeader>
                {topic.subtopics && topic.subtopics.length > 0 && (
                  <CardContent>
                    <h3 className="text-sm font-medium mb-2">子主題：</h3>
                    <ul className="space-y-1">
                      {topic.subtopics.map((subtopic: any) => (
                        <li key={subtopic.id} className="text-sm">
                          <span className="font-medium">{subtopic.name}</span>
                          {subtopic.description && (
                            <span className="text-muted-foreground"> - {subtopic.description}</span>
                          )}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                )}
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
