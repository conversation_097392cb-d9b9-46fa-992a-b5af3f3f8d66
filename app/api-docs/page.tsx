'use client'

import { useState, useEffect } from 'react'
import SwaggerWrapper from '@/components/SwaggerWrapper'
import 'swagger-ui-react/swagger-ui.css'

export default function ApiDocsPage() {
    const [spec, setSpec] = useState(null)
    const [isLoading, setIsLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    useEffect(() => {
        // 載入 API 規範
        fetch('/api/docs')
            .then(response => {
                if (!response.ok) {
                    throw new Error('無法載入 API 規範')
                }
                return response.json()
            })
            .then(data => {
                setSpec(data)
                setIsLoading(false)
            })
            .catch(err => {
                setError(err.message)
                setIsLoading(false)
            })
    }, [])

    if (isLoading) {
        return (
            <div className="min-h-screen bg-background flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-lg text-muted-foreground">載入 API 文檔中...</p>
                </div>
            </div>
        )
    }

    if (error) {
        return (
            <div className="min-h-screen bg-background flex items-center justify-center">
                <div className="text-center max-w-md mx-auto p-8">
                    <div className="text-red-500 text-6xl mb-4">⚠️</div>
                    <h1 className="text-2xl font-bold text-foreground mb-2">載入錯誤</h1>
                    <p className="text-muted-foreground mb-4">{error}</p>
                    <button
                        onClick={() => window.location.reload()}
                        className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors"
                    >
                        重新載入
                    </button>
                </div>
            </div>
        )
    }

    return (
        <div className="min-h-screen bg-background">
            <div className="container mx-auto py-8">
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-foreground mb-2">AILogora API 文檔</h1>
                    <p className="text-muted-foreground">
                        探索 AILogora 平台提供的所有 API 端點。您可以在這裡測試 API、查看請求和回應格式。
                    </p>
                </div>

                <div className="bg-card rounded-lg border p-4">
                    <SwaggerWrapper spec={spec} />
                </div>

                <div className="mt-8 bg-muted/50 rounded-lg p-6">
                    <h2 className="text-xl font-semibold mb-4">快速開始</h2>
                    <div className="grid md:grid-cols-2 gap-6">
                        <div>
                            <h3 className="font-medium mb-2">基本 URL</h3>
                            <code className="bg-muted px-2 py-1 rounded text-sm">
                                {process.env.NODE_ENV === 'development'
                                    ? 'http://localhost:3000/api'
                                    : 'https://your-domain.com/api'
                                }
                            </code>
                        </div>
                        <div>
                            <h3 className="font-medium mb-2">認證</h3>
                            <p className="text-sm text-muted-foreground">
                                大部分 API 需要 Bearer Token 認證
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
} 