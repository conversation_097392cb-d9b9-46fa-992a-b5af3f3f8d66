"use client"

import { useEffect, useState } from "react"
import { useAuth } from "@/contexts/auth-context"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function ApiTestPage() {
    const { isAuthenticated, user } = useAuth()
    const [testResults, setTestResults] = useState<any>({})
    const [loading, setLoading] = useState(false)

    const testApis = async () => {
        setLoading(true)
        const results: any = {}

        // 測試 auth/check
        try {
            const authResponse = await fetch('/api/auth/check')
            results.authCheck = {
                status: authResponse.status,
                data: await authResponse.json()
            }
        } catch (error) {
            results.authCheck = { error: (error as Error).message }
        }

        // 測試 reactions/check (需要參數)
        try {
            const reactionsResponse = await fetch('/api/reactions/check?itemType=thread&itemId=test-id&reactionType=like')
            results.reactionsCheck = {
                status: reactionsResponse.status,
                data: await reactionsResponse.json()
            }
        } catch (error) {
            results.reactionsCheck = { error: (error as Error).message }
        }

        // 測試 bookmarks/check (需要參數)
        try {
            const bookmarksResponse = await fetch('/api/bookmarks/check?itemType=thread&itemId=test-id')
            results.bookmarksCheck = {
                status: bookmarksResponse.status,
                data: await bookmarksResponse.json()
            }
        } catch (error) {
            results.bookmarksCheck = { error: (error as Error).message }
        }

        // 測試 bookmarks/count (不需要認證)
        try {
            const bookmarksCountResponse = await fetch('/api/bookmarks/count?itemType=thread&itemId=test-id')
            results.bookmarksCount = {
                status: bookmarksCountResponse.status,
                data: await bookmarksCountResponse.json()
            }
        } catch (error) {
            results.bookmarksCount = { error: (error as Error).message }
        }

        setTestResults(results)
        setLoading(false)
    }

    return (
        <div className="container mx-auto py-8 space-y-6">
            <h1 className="text-2xl font-bold">API 測試頁面</h1>
            
            <Card>
                <CardHeader>
                    <CardTitle>認證狀態</CardTitle>
                </CardHeader>
                <CardContent>
                    <p><strong>已認證:</strong> {isAuthenticated ? '是' : '否'}</p>
                    <p><strong>用戶 ID:</strong> {user?.id || '無'}</p>
                </CardContent>
            </Card>

            <Card>
                <CardHeader>
                    <CardTitle>API 測試</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <Button onClick={testApis} disabled={loading}>
                        {loading ? '測試中...' : '測試 API'}
                    </Button>

                    {Object.keys(testResults).length > 0 && (
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold">測試結果:</h3>
                            
                            {Object.entries(testResults).map(([apiName, result]: [string, any]) => (
                                <div key={apiName} className="border p-4 rounded">
                                    <h4 className="font-medium">{apiName}</h4>
                                    <pre className="text-sm bg-gray-100 p-2 rounded mt-2 overflow-auto">
                                        {JSON.stringify(result, null, 2)}
                                    </pre>
                                </div>
                            ))}
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    )
}
