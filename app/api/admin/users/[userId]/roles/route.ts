import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import { checkUserPermission, getUserRoles, getUserManagedTopics, getUserManagedSubtopics } from "@/lib/permission-service"

/**
 * @swagger
 * /api/admin/users/{userId}/roles:
 *   get:
 *     summary: 獲取特定用戶的角色信息
 *     tags: [Admin - Users]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: 用戶ID
 *     responses:
 *       200:
 *         description: 成功獲取用戶角色信息
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         name:
 *                           type: string
 *                         email:
 *                           type: string
 *                     roles:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           role_key:
 *                             type: string
 *                           role_name:
 *                             type: string
 *                           granted_at:
 *                             type: string
 *                           expires_at:
 *                             type: string
 *                     managedTopics:
 *                       type: array
 *                     managedSubtopics:
 *                       type: array
 *       401:
 *         description: 未授權
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 用戶不存在
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)
    const { userId } = params

    // 檢查用戶是否已登入 - 使用 safeGetUser 處理 AuthSessionMissingError
    const { safeGetUser } = await import("@/lib/api-utils")
    const { user, error: userError, isAuthError } = await safeGetUser(supabase)

    if (userError || isAuthError) {
      return NextResponse.json({ success: false, error: "認證失敗，請重新登入" }, { status: 401 })
    }

    if (!user) {
      return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
    }

    // 檢查管理員權限
    const permissionCheck = await checkUserPermission(user.id, 'view_admin_panel')
    if (!permissionCheck.success || !permissionCheck.hasPermission) {
      return NextResponse.json(
        { success: false, error: "權限不足" },
        { status: 403 }
      )
    }

    // 獲取目標用戶基本信息
    const { data: targetUser, error: targetUserError } = await supabase
      .from('profiles')
      .select('id, name, email, avatar, bio')
      .eq('id', userId)
      .single()

    if (targetUserError || !targetUser) {
      return NextResponse.json(
        { success: false, error: "用戶不存在" },
        { status: 404 }
      )
    }

    // 獲取用戶角色
    const rolesResult = await getUserRoles(userId)
    if (!rolesResult.success) {
      return NextResponse.json(
        { success: false, error: rolesResult.error },
        { status: 500 }
      )
    }

    // 獲取用戶管理的主題
    const topicsResult = await getUserManagedTopics(userId)
    if (!topicsResult.success) {
      return NextResponse.json(
        { success: false, error: topicsResult.error },
        { status: 500 }
      )
    }

    // 獲取用戶管理的子主題
    const subtopicsResult = await getUserManagedSubtopics(userId)
    if (!subtopicsResult.success) {
      return NextResponse.json(
        { success: false, error: subtopicsResult.error },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        user: targetUser,
        roles: rolesResult.roles,
        managedTopics: topicsResult.topics,
        managedSubtopics: subtopicsResult.subtopics
      }
    })

  } catch (error: any) {
    console.error('處理獲取用戶角色請求時出錯:', error)
    return NextResponse.json(
      { success: false, error: "內部伺服器錯誤" },
      { status: 500 }
    )
  }
}

/**
 * @swagger
 * /api/admin/users/{userId}/roles:
 *   post:
 *     summary: 為特定用戶指派角色
 *     tags: [Admin - Users]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: 用戶ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - roleKey
 *             properties:
 *               roleKey:
 *                 type: string
 *                 description: 角色鍵值
 *               expiresAt:
 *                 type: string
 *                 format: date-time
 *                 description: 過期時間（可選）
 *     responses:
 *       200:
 *         description: 成功指派角色
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未授權
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 用戶不存在
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)
    const { userId } = params

    // 檢查用戶身份驗證
    const { safeGetUser } = await import("@/lib/api-utils")
    const { user, error: authError, isAuthError } = await safeGetUser(supabase)
    
    if (authError || isAuthError || !user) {
      return NextResponse.json(
        { success: false, error: "未授權訪問" },
        { status: 401 }
      )
    }

    // 檢查管理員權限
    const permissionCheck = await checkUserPermission(user.id, 'manage_moderators')
    if (!permissionCheck.success || !permissionCheck.hasPermission) {
      return NextResponse.json(
        { success: false, error: "權限不足" },
        { status: 403 }
      )
    }

    // 解析請求體
    const body = await request.json()
    const { roleKey, expiresAt } = body

    // 驗證必要參數
    if (!roleKey) {
      return NextResponse.json(
        { success: false, error: "缺少必要參數：roleKey" },
        { status: 400 }
      )
    }

    // 驗證目標用戶是否存在
    const { data: targetUser, error: targetUserError2 } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', userId)
      .single()

    if (targetUserError2 || !targetUser) {
      return NextResponse.json(
        { success: false, error: "目標用戶不存在" },
        { status: 404 }
      )
    }

    // 驗證角色是否存在
    const { data: role, error: roleError } = await supabase
      .from('roles')
      .select('role_key')
      .eq('role_key', roleKey)
      .single()

    if (roleError || !role) {
      return NextResponse.json(
        { success: false, error: "無效的角色" },
        { status: 400 }
      )
    }

    // 使用數據庫函數指派角色
    const { data, error } = await supabase
      .rpc('assign_role_to_user', {
        target_user_id: userId,
        role_key: roleKey,
        granted_by_user_id: user.id,
        expires_at: expiresAt || null
      })

    if (error) {
      console.error('指派角色時出錯:', error)
      return NextResponse.json(
        { success: false, error: error.message || "指派角色失敗" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: "角色指派成功"
    })

  } catch (error: any) {
    console.error('處理指派角色請求時出錯:', error)
    return NextResponse.json(
      { success: false, error: "內部伺服器錯誤" },
      { status: 500 }
    )
  }
}

/**
 * @swagger
 * /api/admin/users/{userId}/roles:
 *   delete:
 *     summary: 移除特定用戶的角色
 *     tags: [Admin - Users]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: 用戶ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - roleKey
 *             properties:
 *               roleKey:
 *                 type: string
 *                 description: 角色鍵值
 *     responses:
 *       200:
 *         description: 成功移除角色
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未授權
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 用戶不存在
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)
    const { userId } = params

    // 檢查用戶身份驗證
    const { safeGetUser } = await import("@/lib/api-utils")
    const { user, error: authError, isAuthError } = await safeGetUser(supabase)
    
    if (authError || isAuthError || !user) {
      return NextResponse.json(
        { success: false, error: "未授權訪問" },
        { status: 401 }
      )
    }

    // 檢查管理員權限
    const permissionCheck = await checkUserPermission(user.id, 'manage_moderators')
    if (!permissionCheck.success || !permissionCheck.hasPermission) {
      return NextResponse.json(
        { success: false, error: "權限不足" },
        { status: 403 }
      )
    }

    // 解析請求體
    const body = await request.json()
    const { roleKey } = body

    // 驗證必要參數
    if (!roleKey) {
      return NextResponse.json(
        { success: false, error: "缺少必要參數：roleKey" },
        { status: 400 }
      )
    }

    // 使用數據庫函數移除角色
    const { data, error } = await supabase
      .rpc('remove_role_from_user', {
        target_user_id: userId,
        role_key: roleKey,
        removed_by_user_id: user.id
      })

    if (error) {
      console.error('移除角色時出錯:', error)
      return NextResponse.json(
        { success: false, error: error.message || "移除角色失敗" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: "角色移除成功"
    })

  } catch (error: any) {
    console.error('處理移除角色請求時出錯:', error)
    return NextResponse.json(
      { success: false, error: "內部伺服器錯誤" },
      { status: 500 }
    )
  }
}
