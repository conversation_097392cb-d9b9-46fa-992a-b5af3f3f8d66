import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import { safeGetUser } from "@/lib/api-utils"

/**
 * @swagger
 * /api/auth/check:
 *   get:
 *     tags: [Auth]
 *     summary: 檢查用戶認證狀態
 *     description: 檢查當前用戶是否已經登入，並返回用戶基本資訊
 *     responses:
 *       200:
 *         description: 成功檢查認證狀態
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 authenticated:
 *                   type: boolean
 *                   example: true
 *                   description: 用戶是否已認證
 *                 user:
 *                   type: object
 *                   nullable: true
 *                   properties:
 *                     id:
 *                       type: string
 *                       format: uuid
 *                       example: "123e4567-e89b-12d3-a456-************"
 *                     email:
 *                       type: string
 *                       format: email
 *                       example: "<EMAIL>"
 *       401:
 *         description: 認證失敗
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 authenticated:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Invalid token"
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 authenticated:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */
export async function GET() {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)

    const { user, error, isAuthError } = await safeGetUser(supabase)

    if (error && !isAuthError) {
      console.error("Error checking auth status:", error)
      return NextResponse.json(
        {
          authenticated: false,
          error: error.message,
        },
        { status: 401 },
      )
    }

    return NextResponse.json({
      authenticated: !!user,
      user: user ? {
        id: user.id,
        email: user.email
      } : null,
    })
  } catch (error) {
    console.error("Unexpected error in auth check:", error)
    return NextResponse.json(
      {
        authenticated: false,
        error: "Internal server error",
      },
      { status: 500 },
    )
  }
}
