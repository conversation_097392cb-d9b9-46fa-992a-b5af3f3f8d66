import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { cookies } from 'next/headers'

/**
 * @swagger
 * /api/auth/session:
 *   get:
 *     tags: [Auth]
 *     summary: 獲取用戶會話資訊
 *     description: 獲取當前用戶的會話狀態和詳細資訊
 *     responses:
 *       200:
 *         description: 成功獲取會話資訊
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 isAuthenticated:
 *                   type: boolean
 *                   example: true
 *                   description: 用戶是否已認證
 *                 user:
 *                   type: object
 *                   nullable: true
 *                   properties:
 *                     id:
 *                       type: string
 *                       format: uuid
 *                       example: "123e4567-e89b-12d3-a456-************"
 *                     email:
 *                       type: string
 *                       format: email
 *                       example: "<EMAIL>"
 *                 session:
 *                   type: boolean
 *                   example: true
 *                   description: 會話是否有效
 *                 error:
 *                   type: string
 *                   nullable: true
 *                   example: null
 *                   description: 錯誤訊息（如有）
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 isAuthenticated:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */
export async function GET(request: NextRequest) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        const { safeGetUser } = await import("@/lib/api-utils")
        const { user, error } = await safeGetUser(supabase)

        if (error) {
            console.error('User check error:', error)
            return NextResponse.json({
                isAuthenticated: false,
                error: error.message
            }, { status: 200 })
        }

        return NextResponse.json({
            isAuthenticated: !!user,
            user: user ? {
                id: user.id,
                email: user.email,
            } : null,
            session: !!user,
        })
    } catch (error) {
        console.error('Session API error:', error)
        return NextResponse.json({
            isAuthenticated: false,
            error: 'Internal server error'
        }, { status: 500 })
    }
} 