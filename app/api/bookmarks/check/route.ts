import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import { safeGetUser } from "@/lib/api-utils"

/**
 * @swagger
 * /api/bookmarks/check:
 *   get:
 *     tags: [Bookmarks]
 *     summary: 檢查收藏狀態
 *     description: 檢查指定項目是否已被用戶收藏
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: itemType
 *         required: true
 *         schema:
 *           type: string
 *           enum: [card, thread]
 *         description: 項目類型
 *         example: "card"
 *       - in: query
 *         name: itemId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 項目 ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       200:
 *         description: 成功檢查收藏狀態
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     isBookmarked:
 *                       type: boolean
 *                       example: true
 *                       description: 是否已收藏
 *       400:
 *         description: 請求參數錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: 未授權
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

export async function GET(request: Request) {
    try {
        const { searchParams } = new URL(request.url)
        const itemType = searchParams.get("itemType")
        const itemId = searchParams.get("itemId")

        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入（處理 AuthSessionMissingError）
        const { user, error: userError, isAuthError } = await safeGetUser(supabase)

        // 如果用戶未登入，返回未收藏狀態
        if (userError || isAuthError || !user) {
            return NextResponse.json({
                success: true,
                data: {
                    isBookmarked: false
                }
            })
        }

        // 驗證必要參數
        if (!itemType || !itemId) {
            return NextResponse.json({ success: false, error: "缺少必要參數" }, { status: 400 })
        }

        // 驗證 itemType
        if (itemType !== "card" && itemType !== "thread") {
            return NextResponse.json({ success: false, error: "itemType 必須是 card 或 thread" }, { status: 400 })
        }

        // 檢查是否已收藏
        const { data: bookmark, error: checkError } = await supabase
            .from("bookmarks")
            .select("id")
            .eq("profile_id", user.id)
            .eq("item_type", itemType)
            .eq("item_id", itemId)
            .maybeSingle()

        if (checkError) {
            console.error("檢查收藏狀態時出錯:", checkError)
            return NextResponse.json({ success: false, error: "檢查收藏狀態時出錯" }, { status: 500 })
        }

        return NextResponse.json({
            success: true,
            data: {
                isBookmarked: !!bookmark
            }
        })

    } catch (error) {
        console.error("處理檢查收藏狀態請求時出錯:", error)
        return NextResponse.json({ success: false, error: "內部伺服器錯誤" }, { status: 500 })
    }
} 