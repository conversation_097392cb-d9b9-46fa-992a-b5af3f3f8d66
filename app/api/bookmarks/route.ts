/**
 * @swagger
 * /api/bookmarks:
 *   get:
 *     tags: [Bookmarks]
 *     summary: 獲取收藏列表
 *     description: 獲取用戶的收藏內容列表，支援分頁和類型篩選
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         required: false
 *         schema:
 *           type: integer
 *           default: 1
 *           minimum: 1
 *         description: 頁碼
 *         example: 1
 *       - in: query
 *         name: limit
 *         required: false
 *         schema:
 *           type: integer
 *           default: 10
 *           minimum: 1
 *           maximum: 100
 *         description: 每頁數量
 *         example: 10
 *       - in: query
 *         name: itemType
 *         required: false
 *         schema:
 *           type: string
 *           enum: ["card", "thread", "all"]
 *         description: 收藏項目類型篩選
 *         example: "card"
 *     responses:
 *       200:
 *         description: 成功獲取收藏列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         format: uuid
 *                       itemType:
 *                         type: string
 *                         enum: ["card", "thread"]
 *                       title:
 *                         type: string
 *                       content:
 *                         type: string
 *                       author:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           name:
 *                             type: string
 *                           avatar:
 *                             type: string
 *                       topics:
 *                         type: array
 *                         items:
 *                           type: string
 *                       subtopics:
 *                         type: array
 *                         items:
 *                           type: string
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *       401:
 *         description: 未授權
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *   post:
 *     tags: [Bookmarks]
 *     summary: 添加收藏
 *     description: 將指定內容添加到用戶收藏列表
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: ["itemId", "itemType"]
 *             properties:
 *               itemId:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *                 description: 要收藏的項目 ID
 *               itemType:
 *                 type: string
 *                 enum: ["card", "thread"]
 *                 example: "card"
 *                 description: 收藏項目類型
 *     responses:
 *       200:
 *         description: 操作成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       400:
 *         description: 請求參數錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: 未授權
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       409:
 *         description: 已存在收藏
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *   delete:
 *     tags: [Bookmarks]
 *     summary: 取消收藏
 *     description: 從用戶收藏列表中移除指定內容
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: ["itemId", "itemType"]
 *             properties:
 *               itemId:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *                 description: 要取消收藏的項目 ID
 *               itemType:
 *                 type: string
 *                 enum: ["card", "thread"]
 *                 example: "card"
 *                 description: 收藏項目類型
 *     responses:
 *       200:
 *         description: 操作成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       400:
 *         description: 請求參數錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: 未授權
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

export async function GET(request: Request) {
    try {
        console.log('Bookmarks API: 開始處理請求')

        const { searchParams } = new URL(request.url)
        const page = parseInt(searchParams.get("page") || "1")
        const limit = parseInt(searchParams.get("limit") || "10")
        const itemType = searchParams.get("itemType") // "card" | "thread" | "all"

        console.log('Bookmarks API: 請求參數', { page, limit, itemType })

        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        console.log('Bookmarks API: Supabase 客戶端創建完成')

        // 檢查用戶是否已登入 - 使用 safeGetUser 處理 AuthSessionMissingError
        const { safeGetUser } = await import("@/lib/api-utils")
        const { user, error: userError, isAuthError } = await safeGetUser(supabase)

        console.log('Bookmarks API: 用戶檢查結果', {
            hasUser: !!user,
            userId: user?.id?.slice(0, 8),
            isAuthError,
            errorMessage: userError?.message
        })

        if (userError && !isAuthError) {
            console.error('Bookmarks API: 非認證錯誤', userError)
            return NextResponse.json({
                success: false,
                error: "認證檢查失敗，請稍後再試"
            }, { status: 500 })
        }

        if (isAuthError || !user) {
            console.warn('Bookmarks API: 用戶未認證')
            return NextResponse.json({
                success: false,
                error: "請先登入後再查看收藏"
            }, { status: 401 })
        }

        // 計算 offset
        const offset = (page - 1) * limit

        console.log('Bookmarks API: 開始查詢收藏記錄', { userId: user.id, offset, limit })

        // 先獲取收藏記錄
        let query = supabase
            .from("bookmarks")
            .select("*", { count: "exact" })
            .eq("profile_id", user.id)

        // 根據類型篩選
        if (itemType && itemType !== "all") {
            query = query.eq("item_type", itemType)
        }

        const { data: bookmarks, error, count } = await query
            .order("created_at", { ascending: false })
            .range(offset, offset + limit - 1)

        if (error) {
            console.error("Bookmarks API: 獲取收藏列表時出錯:", error)
            return NextResponse.json({
                success: false,
                error: "獲取收藏列表失敗，請稍後再試"
            }, { status: 500 })
        }

        console.log('Bookmarks API: 收藏記錄查詢完成', {
            bookmarksCount: bookmarks?.length || 0,
            totalCount: count
        })

        if (!bookmarks || bookmarks.length === 0) {
            console.log('Bookmarks API: 沒有找到收藏記錄')
            return NextResponse.json({
                success: true,
                data: [],
                pagination: {
                    total: count || 0,
                    page,
                    limit,
                    totalPages: Math.ceil((count || 0) / limit)
                }
            })
        }

        // 分別獲取卡片和討論串的詳細信息
        const cardIds = bookmarks.filter(b => b.item_type === "card").map(b => b.item_id)
        const threadIds = bookmarks.filter(b => b.item_type === "thread").map(b => b.item_id)

        console.log('Bookmarks API: 內容ID統計', {
            cardIds: cardIds.length,
            threadIds: threadIds.length
        })

        let cards: any[] = []
        let threads: any[] = []

        // 獲取卡片詳情
        if (cardIds.length > 0) {
            try {
                console.log('Bookmarks API: 開始獲取卡片詳情')
                const { data: cardsData, error: cardsError } = await supabase
                    .from("cards")
                    .select(`
                        id, title, content, semantic_type, contribution_type,
                        original_author, original_url, created_at, published_at,
                        profiles:author_id (id, name, avatar)
                    `)
                    .in("id", cardIds)

                if (cardsError) {
                    console.error('Bookmarks API: 獲取卡片詳情失敗', cardsError)
                } else if (cardsData) {
                    console.log('Bookmarks API: 成功獲取卡片詳情', { count: cardsData.length })

                    // 為每張卡片獲取主題和子主題
                    for (const card of cardsData) {
                        try {
                            // 獲取主題
                            const { data: cardTopics } = await supabase
                                .from("card_topics")
                                .select(`
                                    topics:topic_id (id, name)
                                `)
                                .eq("card_id", card.id)

                            // 獲取子主題
                            const { data: cardSubtopics } = await supabase
                                .from("card_subtopics")
                                .select(`
                                    subtopics:subtopic_id (id, name)
                                `)
                                .eq("card_id", card.id)

                                // 添加主題和子主題到卡片
                                ; (card as any).topics = cardTopics ? cardTopics.map((ct: any) => ct.topics.name) : []
                                ; (card as any).subtopics = cardSubtopics ? cardSubtopics.map((cs: any) => cs.subtopics.name) : []
                        } catch (topicError) {
                            console.warn('Bookmarks API: 獲取卡片主題失敗', { cardId: card.id, error: topicError })
                                ; (card as any).topics = []
                                ; (card as any).subtopics = []
                        }
                    }
                    cards = cardsData
                }
            } catch (error) {
                console.error('Bookmarks API: 處理卡片時出錯', error)
            }
        }

        // 獲取討論串詳情
        if (threadIds.length > 0) {
            try {
                console.log('Bookmarks API: 開始獲取討論串詳情')
                const { data: threadsData, error: threadsError } = await supabase
                    .from("threads")
                    .select(`
                        id, title, content, semantic_type,
                        created_at, published_at,
                        profiles:author_id (id, name, avatar)
                    `)
                    .in("id", threadIds)

                if (threadsError) {
                    console.error('Bookmarks API: 獲取討論串詳情失敗', threadsError)
                } else if (threadsData) {
                    console.log('Bookmarks API: 成功獲取討論串詳情', { count: threadsData.length })

                    // 為每個討論串獲取主題和子主題
                    for (const thread of threadsData) {
                        try {
                            // 獲取主題
                            const { data: threadTopics } = await supabase
                                .from("thread_topics")
                                .select(`
                                    topics:topic_id (id, name)
                                `)
                                .eq("thread_id", thread.id)

                            // 獲取子主題
                            const { data: threadSubtopics } = await supabase
                                .from("thread_subtopics")
                                .select(`
                                    subtopics:subtopic_id (id, name)
                                `)
                                .eq("thread_id", thread.id)

                                // 添加主題和子主題到討論串
                                ; (thread as any).topics = threadTopics ? threadTopics.map((tt: any) => tt.topics.name) : []
                                ; (thread as any).subtopics = threadSubtopics ? threadSubtopics.map((ts: any) => ts.subtopics.name) : []
                        } catch (topicError) {
                            console.warn('Bookmarks API: 獲取討論串主題失敗', { threadId: thread.id, error: topicError })
                                ; (thread as any).topics = []
                                ; (thread as any).subtopics = []
                        }
                    }
                    threads = threadsData
                }
            } catch (error) {
                console.error('Bookmarks API: 處理討論串時出錯', error)
            }
        }

        // 合併數據
        const processedData = bookmarks.map(bookmark => {
            let content = null

            if (bookmark.item_type === "card") {
                content = cards.find(card => card.id === bookmark.item_id)
            } else {
                content = threads.find(thread => thread.id === bookmark.item_id)
            }

            return {
                ...bookmark,
                content: content
            }
        }).filter(item => item.content !== null) // 過濾掉找不到內容的項目

        console.log('Bookmarks API: 數據處理完成', {
            originalCount: bookmarks.length,
            processedCount: processedData.length
        })

        return NextResponse.json({
            success: true,
            data: processedData,
            pagination: {
                total: count || 0,
                page,
                limit,
                totalPages: Math.ceil((count || 0) / limit)
            }
        })

    } catch (error) {
        console.error("Bookmarks API: 處理請求時出現未預期錯誤:", error)
        return NextResponse.json({
            success: false,
            error: "系統暫時無法處理請求，請稍後再試"
        }, { status: 500 })
    }
}

export async function POST(request: Request) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入 - 使用 safeGetUser 處理 AuthSessionMissingError
        const { safeGetUser } = await import("@/lib/api-utils")
        const { user, error: userError, isAuthError } = await safeGetUser(supabase)

        if (userError || isAuthError) {
            return NextResponse.json({ success: false, error: "認證失敗，請重新登入" }, { status: 401 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 解析請求體
        const { itemType, itemId } = await request.json()

        // 驗證必要參數
        if (!itemType || !itemId) {
            return NextResponse.json({ success: false, error: "缺少必要參數" }, { status: 400 })
        }

        // 驗證 itemType
        if (itemType !== "card" && itemType !== "thread") {
            return NextResponse.json({ success: false, error: "itemType 必須是 card 或 thread" }, { status: 400 })
        }

        // 檢查用戶是否已經收藏該項目
        const { data: existingBookmark, error: checkError } = await supabase
            .from("bookmarks")
            .select("id")
            .eq("profile_id", user.id)
            .eq("item_type", itemType)
            .eq("item_id", itemId)
            .maybeSingle()

        if (checkError) {
            console.error("檢查收藏時出錯:", checkError)
            return NextResponse.json({ success: false, error: "檢查收藏時出錯" }, { status: 500 })
        }

        // 如果已經收藏，則取消收藏
        if (existingBookmark) {
            const { error: deleteError } = await supabase
                .from("bookmarks")
                .delete()
                .eq("id", existingBookmark.id)

            if (deleteError) {
                console.error("取消收藏時出錯:", deleteError)
                return NextResponse.json({ success: false, error: "取消收藏時出錯" }, { status: 500 })
            }

            return NextResponse.json({
                success: true,
                data: { action: "removed" },
                message: "已取消收藏"
            })
        }

        // 如果沒有收藏，則添加收藏
        const { error: insertError } = await supabase
            .from("bookmarks")
            .insert({
                profile_id: user.id,
                item_type: itemType,
                item_id: itemId,
            })

        if (insertError) {
            console.error("添加收藏時出錯:", insertError)
            return NextResponse.json({ success: false, error: "添加收藏時出錯" }, { status: 500 })
        }

        return NextResponse.json({
            success: true,
            data: { action: "added" },
            message: "已加入收藏"
        })

    } catch (error) {
        console.error("處理收藏時出錯:", error)
        return NextResponse.json({ success: false, error: "內部伺服器錯誤" }, { status: 500 })
    }
}

export async function DELETE(request: Request) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入 - 使用 safeGetUser 處理 AuthSessionMissingError
        const { safeGetUser } = await import("@/lib/api-utils")
        const { user, error: userError, isAuthError } = await safeGetUser(supabase)

        if (userError || isAuthError) {
            return NextResponse.json({ success: false, error: "認證失敗，請重新登入" }, { status: 401 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        const body = await request.json()
        const { itemType, itemId } = body

        if (!itemType || !itemId) {
            return NextResponse.json({
                success: false,
                error: "缺少必要參數：項目類型和項目ID"
            }, { status: 400 })
        }

        // 刪除收藏
        const { error } = await supabase
            .from("bookmarks")
            .delete()
            .eq("profile_id", user.id)
            .eq("item_type", itemType)
            .eq("item_id", itemId)

        if (error) {
            console.error("刪除收藏時出錯:", error)
            return NextResponse.json({
                success: false,
                error: "刪除收藏失敗"
            }, { status: 500 })
        }

        return NextResponse.json({
            success: true,
            message: "取消收藏成功"
        })

    } catch (error) {
        console.error("處理刪除收藏請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
} 