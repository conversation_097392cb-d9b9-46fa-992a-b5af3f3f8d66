/**
 * @swagger
 * /api/cards:
 *   get:
 *     tags: [Cards]
 *     summary: 獲取觀點卡列表
 *     description: 獲取分頁的觀點卡列表，支援按來源和主題篩選
 *     parameters:
 *       - in: query
 *         name: page
 *         required: false
 *         schema:
 *           type: integer
 *           default: 1
 *           minimum: 1
 *         description: 頁碼
 *         example: 1
 *       - in: query
 *         name: limit
 *         required: false
 *         schema:
 *           type: integer
 *           default: 10
 *           minimum: 1
 *           maximum: 100
 *         description: 每頁數量
 *         example: 10
 *       - in: query
 *         name: source
 *         required: false
 *         schema:
 *           type: string
 *           enum: [editor, community, all]
 *         description: 卡片來源篩選
 *         example: community
 *       - in: query
 *         name: topicId
 *         required: false
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 主題 ID 篩選
 *         example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       200:
 *         description: 成功回應
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Card'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     limit:
 *                       type: integer
 *                       example: 10
 *                     total:
 *                       type: integer
 *                       example: 50
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

/**
 * @swagger
 * /api/cards:
 *   post:
 *     tags: [Cards]
 *     summary: 創建新的觀點卡
 *     description: 創建新的觀點卡並關聯相關主題
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [title, content, semanticType]
 *             properties:
 *               title:
 *                 type: string
 *                 example: "AI 技術的未來發展"
 *                 description: 觀點卡標題
 *               content:
 *                 type: string
 *                 example: "人工智能將在未來十年內改變我們的生活方式..."
 *                 description: 觀點卡內容
 *               semanticType:
 *                 type: string
 *                 enum: [claim, evidence, reasoning, question]
 *                 example: "claim"
 *                 description: 語義類型
 *               contributionType:
 *                 type: string
 *                 enum: [original, curated, translated]
 *                 example: "original"
 *                 description: 貢獻類型
 *               originalAuthor:
 *                 type: string
 *                 example: "張三"
 *                 description: 原作者（如果是轉載）
 *               originalUrl:
 *                 type: string
 *                 format: uri
 *                 example: "https://example.com/article"
 *                 description: 原文鏈接（如果是轉載）
 *               topicIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *                 example: ["123e4567-e89b-12d3-a456-************"]
 *                 description: 關聯的主題 ID 陣列
 *               subtopicIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *                 example: ["223e4567-e89b-12d3-a456-************"]
 *                 description: 關聯的子主題 ID 陣列
 *               status:
 *                 type: string
 *                 enum: [pending, published, rejected]
 *                 default: pending
 *                 example: "pending"
 *                 description: 卡片狀態
 *     responses:
 *       200:
 *         description: 成功創建觀點卡
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Card'
 *                 message:
 *                   type: string
 *                   example: "觀點卡創建成功"
 *       400:
 *         description: 請求參數錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: 未授權
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

/**
 * @swagger
 * /api/cards:
 *   put:
 *     tags: [Cards]
 *     summary: 更新觀點卡
 *     description: 更新指定的觀點卡及其關聯主題
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [id, title, content, semanticType]
 *             properties:
 *               id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *                 description: 觀點卡 ID
 *               title:
 *                 type: string
 *                 example: "AI 技術的未來發展（更新）"
 *                 description: 觀點卡標題
 *               content:
 *                 type: string
 *                 example: "人工智能將在未來十年內改變我們的生活方式...（更新內容）"
 *                 description: 觀點卡內容
 *               semanticType:
 *                 type: string
 *                 enum: [claim, evidence, reasoning, question]
 *                 example: "claim"
 *                 description: 語義類型
 *               contributionType:
 *                 type: string
 *                 enum: [original, curated, translated]
 *                 example: "original"
 *                 description: 貢獻類型
 *               originalAuthor:
 *                 type: string
 *                 example: "張三"
 *                 description: 原作者（如果是轉載）
 *               originalUrl:
 *                 type: string
 *                 format: uri
 *                 example: "https://example.com/article"
 *                 description: 原文鏈接（如果是轉載）
 *               topicIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *                 example: ["123e4567-e89b-12d3-a456-************"]
 *                 description: 關聯的主題 ID 陣列
 *               subtopicIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *                 example: ["223e4567-e89b-12d3-a456-************"]
 *                 description: 關聯的子主題 ID 陣列
 *               status:
 *                 type: string
 *                 enum: [pending, published, rejected]
 *                 example: "published"
 *                 description: 卡片狀態
 *     responses:
 *       200:
 *         description: 成功更新觀點卡
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Card'
 *                 message:
 *                   type: string
 *                   example: "觀點卡更新成功"
 *       400:
 *         description: 請求參數錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: 未授權
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: 卡片不存在或無權限編輯
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import type { CardCreateInput } from "@/lib/types"

export async function POST(request: Request) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入 - 使用 safeGetUser 處理 AuthSessionMissingError
        const { safeGetUser } = await import("@/lib/api-utils")
        const { user, error: userError, isAuthError } = await safeGetUser(supabase)

        if (userError || isAuthError) {
            return NextResponse.json({ success: false, error: "認證失敗，請重新登入" }, { status: 401 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 解析請求體
        const body = await request.json()
        const {
            title,
            content,
            semanticType,
            contributionType,
            originalAuthor,
            originalUrl,
            topicIds,
            subtopicIds,
            status = "pending", // 新增 status 參數，預設為 pending（待審核）
        } = body

        // 驗證必要參數
        if (!title || !content || !semanticType) {
            return NextResponse.json({
                success: false,
                error: "缺少必要參數：標題、內容和語義類型"
            }, { status: 400 })
        }

        // 1. 創建卡片
        const { data: card, error: cardError } = await supabase
            .from("cards")
            .insert({
                title,
                content,
                author_id: user.id,
                card_type: "internal", // 預設為內部類型
                semantic_type: semanticType,
                contribution_type: contributionType || "original",
                original_author: originalAuthor,
                original_url: originalUrl,
                status: status, // 新增 status 欄位
                published_at: status === "published" ? new Date().toISOString() : null, // 如果是已發布狀態，設定發布時間
            })
            .select()
            .single()

        if (cardError) {
            console.error("創建卡片時出錯:", cardError)
            return NextResponse.json({
                success: false,
                error: "創建卡片失敗"
            }, { status: 500 })
        }

        // 2. 添加主題關聯
        if (topicIds && topicIds.length > 0) {
            const topicRelations = topicIds.map((topicId: string) => ({
                card_id: card.id,
                topic_id: topicId,
            }))

            const { error: topicError } = await supabase
                .from("card_topics")
                .insert(topicRelations)

            if (topicError) {
                console.error("添加主題關聯時出錯:", topicError)
                // 這裡可以選擇回滾卡片創建或繼續
            }
        }

        // 3. 添加子主題關聯
        if (subtopicIds && subtopicIds.length > 0) {
            const subtopicRelations = subtopicIds.map((subtopicId: string) => ({
                card_id: card.id,
                subtopic_id: subtopicId,
            }))

            const { error: subtopicError } = await supabase
                .from("card_subtopics")
                .insert(subtopicRelations)

            if (subtopicError) {
                console.error("添加子主題關聯時出錯:", subtopicError)
                // 這裡可以選擇回滾卡片創建或繼續
            }
        }

        return NextResponse.json({
            success: true,
            data: card,
            message: "觀點卡創建成功"
        })

    } catch (error) {
        console.error("處理創建卡片請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
}

export async function GET(request: Request) {
    try {
        const { searchParams } = new URL(request.url)
        const page = parseInt(searchParams.get("page") || "1")
        const limit = parseInt(searchParams.get("limit") || "10")
        const source = searchParams.get("source") // "editor" | "community" | "all"
        const topicId = searchParams.get("topicId")

        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 計算 offset
        const offset = (page - 1) * limit

        let query = supabase
            .from("cards")
            .select(`
        *,
        profiles:author_id (id, name, avatar),
        card_topics (topics (*)),
        card_subtopics (subtopics (*))
      `, { count: "exact" })

        // 根據來源篩選
        if (source && source !== "all") {
            query = query.eq("contribution_type", source === "editor" ? "top_author" : "community")
        }

        // 根據主題篩選
        if (topicId) {
            query = query.filter("card_topics.topic_id", "eq", topicId)
        }

        const { data, error, count } = await query
            .order("created_at", { ascending: false })
            .range(offset, offset + limit - 1)

        if (error) {
            console.error("獲取卡片時出錯:", error)
            return NextResponse.json({
                success: false,
                error: "獲取卡片失敗"
            }, { status: 500 })
        }

        return NextResponse.json({
            success: true,
            data,
            pagination: {
                total: count || 0,
                page,
                limit,
                totalPages: Math.ceil((count || 0) / limit)
            }
        })

    } catch (error) {
        console.error("處理獲取卡片請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
}

export async function PUT(request: Request) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入 - 使用 safeGetUser 處理 AuthSessionMissingError
        const { safeGetUser } = await import("@/lib/api-utils")
        const { user, error: userError, isAuthError } = await safeGetUser(supabase)

        if (userError || isAuthError) {
            return NextResponse.json({ success: false, error: "認證失敗，請重新登入" }, { status: 401 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 解析請求體
        const body = await request.json()
        const {
            id,
            title,
            content,
            semanticType,
            contributionType,
            originalAuthor,
            originalUrl,
            topicIds,
            subtopicIds,
            status,
        } = body

        // 驗證必要參數
        if (!id || !title || !content || !semanticType) {
            return NextResponse.json({
                success: false,
                error: "缺少必要參數：ID、標題、內容和語義類型"
            }, { status: 400 })
        }

        // 檢查卡片是否存在且屬於當前用戶
        const { data: existingCard, error: checkError } = await supabase
            .from("cards")
            .select("id, author_id, status")
            .eq("id", id)
            .eq("author_id", user.id)
            .single()

        if (checkError || !existingCard) {
            return NextResponse.json({
                success: false,
                error: "卡片不存在或無權限編輯"
            }, { status: 404 })
        }

        // 更新卡片
        const updateData: any = {
            title,
            content,
            semantic_type: semanticType,
            contribution_type: contributionType || "original",
            original_author: originalAuthor,
            original_url: originalUrl,
            updated_at: new Date().toISOString(),
        }

        // 如果狀態有變化，更新狀態和發布時間
        if (status && status !== existingCard.status) {
            updateData.status = status
            if (status === "published") {
                updateData.published_at = new Date().toISOString()
            }
        }

        const { data: updatedCard, error: updateError } = await supabase
            .from("cards")
            .update(updateData)
            .eq("id", id)
            .select()
            .single()

        if (updateError) {
            console.error("更新卡片時出錯:", updateError)
            return NextResponse.json({
                success: false,
                error: "更新卡片失敗"
            }, { status: 500 })
        }

        // 更新主題關聯
        if (topicIds !== undefined) {
            // 先刪除現有關聯
            await supabase
                .from("card_topics")
                .delete()
                .eq("card_id", id)

            // 添加新關聯
            if (topicIds.length > 0) {
                const topicRelations = topicIds.map((topicId: string) => ({
                    card_id: id,
                    topic_id: topicId,
                }))

                const { error: topicError } = await supabase
                    .from("card_topics")
                    .insert(topicRelations)

                if (topicError) {
                    console.error("更新主題關聯時出錯:", topicError)
                }
            }
        }

        // 更新子主題關聯
        if (subtopicIds !== undefined) {
            // 先刪除現有關聯
            await supabase
                .from("card_subtopics")
                .delete()
                .eq("card_id", id)

            // 添加新關聯
            if (subtopicIds.length > 0) {
                const subtopicRelations = subtopicIds.map((subtopicId: string) => ({
                    card_id: id,
                    subtopic_id: subtopicId,
                }))

                const { error: subtopicError } = await supabase
                    .from("card_subtopics")
                    .insert(subtopicRelations)

                if (subtopicError) {
                    console.error("更新子主題關聯時出錯:", subtopicError)
                }
            }
        }

        return NextResponse.json({
            success: true,
            data: updatedCard,
            message: "觀點卡更新成功"
        })

    } catch (error) {
        console.error("處理更新卡片請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
} 