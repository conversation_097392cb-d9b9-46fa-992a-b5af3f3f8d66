import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

/**
 * @swagger
 * /api/collections/{collectionId}:
 *   get:
 *     tags: [Collections]
 *     summary: 獲取收藏集詳情
 *     description: 獲取指定收藏集的詳細資訊和項目列表
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: collectionId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 收藏集 ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *       - in: query
 *         name: page
 *         required: false
 *         schema:
 *           type: integer
 *           default: 1
 *           minimum: 1
 *         description: 頁碼
 *         example: 1
 *       - in: query
 *         name: limit
 *         required: false
 *         schema:
 *           type: integer
 *           default: 20
 *           minimum: 1
 *           maximum: 100
 *         description: 每頁數量
 *         example: 20
 *     responses:
 *       200:
 *         description: 成功獲取收藏集詳情
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     collection:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                           format: uuid
 *                         name:
 *                           type: string
 *                           example: "我的 AI 收藏"
 *                         description:
 *                           type: string
 *                         coverImage:
 *                           type: string
 *                         itemCount:
 *                           type: integer
 *                         isPublic:
 *                           type: boolean
 *                         createdAt:
 *                           type: string
 *                           format: date-time
 *                         updatedAt:
 *                           type: string
 *                           format: date-time
 *                     items:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             format: uuid
 *                           contentType:
 *                             type: string
 *                             enum: ["viewpoint", "discussion"]
 *                           title:
 *                             type: string
 *                           content:
 *                             type: string
 *                           author:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: string
 *                               name:
 *                                 type: string
 *                               avatar:
 *                                 type: string
 *                           topics:
 *                             type: array
 *                             items:
 *                               type: string
 *                           addedAt:
 *                             type: string
 *                             format: date-time
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 *       401:
 *         description: 未授權
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: 收藏集不存在或無權限訪問
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *   put:
 *     tags: [Collections]
 *     summary: 更新收藏集
 *     description: 更新指定收藏集的資訊
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: collectionId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 收藏集 ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 example: "更新後的收藏集名稱"
 *                 description: 收藏集名稱
 *               description:
 *                 type: string
 *                 example: "更新後的描述"
 *                 description: 收藏集描述
 *               coverImage:
 *                 type: string
 *                 example: "/new-cover.png"
 *                 description: 封面圖片路徑
 *               isPublic:
 *                 type: boolean
 *                 description: 是否公開收藏集
 *               categoryId:
 *                 type: string
 *                 format: uuid
 *                 description: 分類 ID
 *     responses:
 *       200:
 *         description: 成功更新收藏集
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       401:
 *         description: 未授權
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: 收藏集不存在或無權限訪問
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *   delete:
 *     tags: [Collections]
 *     summary: 刪除收藏集
 *     description: 刪除指定的收藏集
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: collectionId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 收藏集 ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       200:
 *         description: 成功刪除收藏集
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       401:
 *         description: 未授權
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: 收藏集不存在或無權限訪問
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

export async function GET(
    request: Request,
    { params }: { params: Promise<{ collectionId: string }> }
) {
    try {
        const { collectionId } = await params
        const { searchParams } = new URL(request.url)
        const page = parseInt(searchParams.get("page") || "1")
        const limit = parseInt(searchParams.get("limit") || "20")

        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入 - 使用 safeGetUser 處理 AuthSessionMissingError
        const { safeGetUser } = await import("@/lib/api-utils")
        const { user, error: userError, isAuthError } = await safeGetUser(supabase)

        if (userError || isAuthError) {
            return NextResponse.json({ success: false, error: "認證失敗，請重新登入" }, { status: 401 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 首先獲取收藏牆信息
        const { data: collection, error: collectionError } = await supabase
            .from("collections")
            .select("*")
            .eq("id", collectionId)
            .eq("user_id", user.id)
            .single()

        if (collectionError || !collection) {
            return NextResponse.json({
                success: false,
                error: "收藏牆不存在或無權限訪問"
            }, { status: 404 })
        }

        // 計算 offset
        const offset = (page - 1) * limit

        // 獲取收藏牆項目
        const { data: collectionItems, error: itemsError } = await supabase
            .from("collection_items")
            .select("id, item_type, item_id, created_at")
            .eq("collection_id", collectionId)
            .order("created_at", { ascending: false })
            .range(offset, offset + limit - 1)

        if (itemsError) {
            console.error("獲取收藏牆項目時出錯:", itemsError)
            return NextResponse.json({
                success: false,
                error: "獲取收藏牆項目失敗"
            }, { status: 500 })
        }

        // 獲取總計數
        const { count } = await supabase
            .from("collection_items")
            .select("*", { count: "exact", head: true })
            .eq("collection_id", collectionId)

        // 分別獲取 cards 和 threads 的詳細信息
        const cardIds = collectionItems?.filter(item => item.item_type === "card").map(item => item.item_id) || []
        const threadIds = collectionItems?.filter(item => item.item_type === "thread").map(item => item.item_id) || []

        // 並行獲取 cards 和 threads 數據
        const [cardsResult, threadsResult] = await Promise.all([
            cardIds.length > 0 ? supabase
                .from("cards")
                .select(`
                    id, title, content, semantic_type, contribution_type,
                    original_author, original_url, created_at, published_at,
                    profiles:author_id (id, name, avatar),
                    card_topics:card_topics (topics:topic_id (name)),
                    card_subtopics:card_subtopics (subtopics:subtopic_id (name))
                `)
                .in("id", cardIds) : Promise.resolve({ data: [], error: null }),
            threadIds.length > 0 ? supabase
                .from("threads")
                .select(`
                    id, title, content, semantic_type,
                    created_at, published_at,
                    profiles:author_id (id, name, avatar),
                    thread_topics:thread_topics (topics:topic_id (name)),
                    thread_subtopics:thread_subtopics (subtopics:subtopic_id (name))
                `)
                .in("id", threadIds) : Promise.resolve({ data: [], error: null })
        ])

        if (cardsResult.error || threadsResult.error) {
            console.error("獲取內容詳情時出錯:", cardsResult.error || threadsResult.error)
            return NextResponse.json({
                success: false,
                error: "獲取內容詳情失敗"
            }, { status: 500 })
        }

        // 創建內容映射
        const cardsMap = new Map(cardsResult.data?.map(card => [card.id, card]) || [])
        const threadsMap = new Map(threadsResult.data?.map(thread => [thread.id, thread]) || [])

        // 處理項目數據，統一格式
        const processedItems = await Promise.all(
            (collectionItems || []).map(async (item) => {
                const isCard = item.item_type === "card"
                const content = isCard ? cardsMap.get(item.item_id) : threadsMap.get(item.item_id)

                if (!content) return null

                // 獲取反應統計
                const { data: reactions } = await supabase
                    .from("reactions")
                    .select("reaction_type")
                    .eq("item_type", isCard ? "card" : "thread")
                    .eq("item_id", content.id)

                // 獲取評論統計
                const { data: comments } = await supabase
                    .from("comments")
                    .select("id")
                    .eq("root_item_type", isCard ? "card" : "thread")
                    .eq("root_item_id", content.id)

                // 統計反應數據
                const likes = reactions?.filter(r => r.reaction_type === "like").length || 0
                const dislikes = reactions?.filter(r => r.reaction_type === "dislike").length || 0
                const commentCount = comments?.length || 0

                return {
                    id: content.id,
                    contentType: isCard ? "viewpoint" : "thread",
                    semanticType: content.semantic_type,
                    title: content.title,
                    content: content.content,
                    author: {
                        id: (content as any).profiles?.id || "unknown",
                        name: (content as any).profiles?.name || "未知用戶",
                        avatar: (content as any).profiles?.avatar,
                    },
                    timestamp: new Date(content.created_at).toLocaleDateString("zh-TW"),
                    topics: isCard
                        ? (content as any).card_topics?.map((ct: any) => ct.topics?.name).filter(Boolean) || []
                        : (content as any).thread_topics?.map((tt: any) => tt.topics?.name).filter(Boolean) || [],
                    subtopics: isCard
                        ? (content as any).card_subtopics?.map((cs: any) => cs.subtopics?.name).filter(Boolean) || []
                        : undefined,
                    tags: !isCard
                        ? (content as any).thread_subtopics?.map((ts: any) => ts.subtopics?.name).filter(Boolean) || []
                        : undefined,
                    stats: {
                        likes,
                        dislikes: isCard ? dislikes : undefined,
                        comments: isCard ? commentCount : undefined,
                        replies: !isCard ? commentCount : undefined,
                    },
                    // 觀點卡特有屬性
                    ...(isCard && {
                        contribution_type: (content as any).contribution_type,
                        originalAuthor: (content as any).original_author,
                        originalSource: (content as any).original_url,
                    }),
                    addedAt: item.created_at,
                }
            })
        )

        // 過濾掉空項目
        const validItems = processedItems.filter(item => item !== null)

        // 格式化收藏牆信息
        const formattedCollection = {
            id: collection.id,
            name: collection.name,
            description: collection.description,
            coverImage: collection.cover_image,
            itemCount: count || 0,
            isPublic: collection.is_public,
            createdAt: collection.created_at,
            updatedAt: collection.updated_at,
        }

        return NextResponse.json({
            success: true,
            data: {
                collection: formattedCollection,
                items: validItems,
                pagination: {
                    total: count || 0,
                    page,
                    limit,
                    totalPages: Math.ceil((count || 0) / limit)
                }
            }
        })

    } catch (error) {
        console.error("處理獲取收藏牆詳情請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
}

export async function PUT(
    request: Request,
    { params }: { params: Promise<{ collectionId: string }> }
) {
    try {
        const { collectionId } = await params
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入 - 使用 safeGetUser 處理 AuthSessionMissingError
        const { safeGetUser } = await import("@/lib/api-utils")
        const { user, error: userError, isAuthError } = await safeGetUser(supabase)

        if (userError || isAuthError) {
            return NextResponse.json({ success: false, error: "認證失敗，請重新登入" }, { status: 401 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 解析請求體
        const { name, description, coverImage, isPublic, categoryId } = await request.json()

        // 檢查收藏牆是否存在且屬於當前用戶
        const { data: existingCollection, error: checkError } = await supabase
            .from("collections")
            .select("id, user_id")
            .eq("id", collectionId)
            .eq("user_id", user.id)
            .single()

        if (checkError || !existingCollection) {
            return NextResponse.json({
                success: false,
                error: "收藏牆不存在或無權限訪問"
            }, { status: 404 })
        }

        // 如果指定了 categoryId，驗證分類是否存在且屬於當前用戶
        if (categoryId && categoryId !== null) {
            const { data: category, error: categoryError } = await supabase
                .from("collection_categories")
                .select("id")
                .eq("id", categoryId)
                .eq("user_id", user.id)
                .single()

            if (categoryError || !category) {
                return NextResponse.json({
                    success: false,
                    error: "指定的分類不存在"
                }, { status: 400 })
            }
        }

        // 構建更新數據
        const updateData: any = {
            updated_at: new Date().toISOString()
        }

        if (name !== undefined) updateData.name = name
        if (description !== undefined) updateData.description = description
        if (coverImage !== undefined) updateData.cover_image = coverImage
        if (isPublic !== undefined) updateData.is_public = isPublic
        if (categoryId !== undefined) updateData.category_id = categoryId

        // 更新收藏牆
        const { data: updatedCollection, error: updateError } = await supabase
            .from("collections")
            .update(updateData)
            .eq("id", collectionId)
            .select()
            .single()

        if (updateError) {
            console.error("更新收藏牆時出錯:", updateError)
            return NextResponse.json({
                success: false,
                error: "更新收藏牆失敗"
            }, { status: 500 })
        }

        // 獲取包含分類資訊的收藏牆資料
        const { data: collectionWithCategory } = await supabase
            .from("collections")
            .select(`
                *,
                collection_categories!left(name),
                collection_items(count)
            `)
            .eq("id", collectionId)
            .single()

        // 格式化返回數據  
        const formattedCollection = {
            id: updatedCollection.id,
            name: updatedCollection.name,
            description: updatedCollection.description,
            coverImage: updatedCollection.cover_image,
            itemCount: collectionWithCategory?.collection_items?.[0]?.count || 0,
            isPublic: updatedCollection.is_public,
            categoryId: updatedCollection.category_id,
            categoryName: collectionWithCategory?.collection_categories?.name || null,
            createdAt: updatedCollection.created_at,
            updatedAt: updatedCollection.updated_at,
        }

        return NextResponse.json({
            success: true,
            data: formattedCollection,
            message: "收藏牆更新成功"
        })

    } catch (error) {
        console.error("處理更新收藏牆請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
}

export async function DELETE(
    request: Request,
    { params }: { params: Promise<{ collectionId: string }> }
) {
    try {
        const { collectionId } = await params
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入 - 使用 safeGetUser 處理 AuthSessionMissingError
        const { safeGetUser } = await import("@/lib/api-utils")
        const { user, error: userError, isAuthError } = await safeGetUser(supabase)

        if (userError || isAuthError) {
            return NextResponse.json({ success: false, error: "認證失敗，請重新登入" }, { status: 401 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 檢查收藏牆是否存在且屬於當前用戶
        const { data: existingCollection, error: checkError } = await supabase
            .from("collections")
            .select("id, user_id, name")
            .eq("id", collectionId)
            .eq("user_id", user.id)
            .single()

        if (checkError || !existingCollection) {
            return NextResponse.json({
                success: false,
                error: "收藏牆不存在或無權限訪問"
            }, { status: 404 })
        }

        // 先刪除收藏牆中的所有項目
        const { error: deleteItemsError } = await supabase
            .from("collection_items")
            .delete()
            .eq("collection_id", collectionId)

        if (deleteItemsError) {
            console.error("刪除收藏牆項目時出錯:", deleteItemsError)
            return NextResponse.json({
                success: false,
                error: "刪除收藏牆項目失敗"
            }, { status: 500 })
        }

        // 刪除收藏牆
        const { error: deleteCollectionError } = await supabase
            .from("collections")
            .delete()
            .eq("id", collectionId)

        if (deleteCollectionError) {
            console.error("刪除收藏牆時出錯:", deleteCollectionError)
            return NextResponse.json({
                success: false,
                error: "刪除收藏牆失敗"
            }, { status: 500 })
        }

        return NextResponse.json({
            success: true,
            message: `收藏牆「${existingCollection.name}」已成功刪除`
        })

    } catch (error) {
        console.error("處理刪除收藏牆請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
}