/**
 * @swagger
 * /api/collections:
 *   get:
 *     tags: [Collections]
 *     summary: 獲取收藏集列表
 *     description: 獲取用戶的收藏集列表，支援分頁和分類篩選
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         required: false
 *         schema:
 *           type: integer
 *           default: 1
 *           minimum: 1
 *         description: 頁碼
 *         example: 1
 *       - in: query
 *         name: limit
 *         required: false
 *         schema:
 *           type: integer
 *           default: 50
 *           minimum: 1
 *           maximum: 100
 *         description: 每頁數量
 *         example: 50
 *       - in: query
 *         name: categoryId
 *         required: false
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 分類 ID 篩選，使用 "uncategorized" 獲取未分類收藏集
 *         example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       200:
 *         description: 成功獲取收藏集列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         format: uuid
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *                       coverImage:
 *                         type: string
 *                       itemCount:
 *                         type: integer
 *                       isPublic:
 *                         type: boolean
 *                       categoryId:
 *                         type: string
 *                         format: uuid
 *                         nullable: true
 *                       categoryName:
 *                         type: string
 *                         nullable: true
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *       401:
 *         description: 未授權
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *   post:
 *     tags: [Collections]
 *     summary: 創建收藏集
 *     description: 創建新的收藏集
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: ["name"]
 *             properties:
 *               name:
 *                 type: string
 *                 example: "我的 AI 收藏"
 *                 description: 收藏集名稱
 *               description:
 *                 type: string
 *                 example: "關於人工智能的精選內容"
 *                 description: 收藏集描述
 *               coverImage:
 *                 type: string
 *                 example: "/abstract-geometric-shapes.png"
 *                 description: 封面圖片路徑
 *               isPublic:
 *                 type: boolean
 *                 default: true
 *                 description: 是否公開收藏集
 *               categoryId:
 *                 type: string
 *                 format: uuid
 *                 description: 分類 ID（可選）
 *     responses:
 *       200:
 *         description: 收藏集創建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       format: uuid
 *                     name:
 *                       type: string
 *                     description:
 *                       type: string
 *                     coverImage:
 *                       type: string
 *                     isPublic:
 *                       type: boolean
 *                     categoryId:
 *                       type: string
 *                       format: uuid
 *                       nullable: true
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                 message:
 *                   type: string
 *                   example: "收藏牆創建成功"
 *       400:
 *         description: 請求參數錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: 未授權
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

export async function GET(request: Request) {
    try {
        const { searchParams } = new URL(request.url)
        const page = parseInt(searchParams.get("page") || "1")
        const limit = parseInt(searchParams.get("limit") || "50")
        const categoryId = searchParams.get("categoryId") // 可選的分類篩選

        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入 - 使用 safeGetUser 處理 AuthSessionMissingError
        const { safeGetUser } = await import("@/lib/api-utils")
        const { user, error: userError, isAuthError } = await safeGetUser(supabase)

        if (userError || isAuthError) {
            return NextResponse.json({ success: false, error: "認證失敗，請重新登入" }, { status: 401 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 計算 offset
        const offset = (page - 1) * limit

        // 使用 JOIN 獲取收藏牆列表，包含分類資訊
        let query = supabase
            .from("collections")
            .select(`
                *,
                collection_categories!left(name),
                collection_items(count)
            `, { count: "exact" })
            .eq("user_id", user.id)

        // 如果指定了分類，則過濾
        if (categoryId) {
            if (categoryId === "uncategorized") {
                query = query.is("category_id", null)
            } else {
                query = query.eq("category_id", categoryId)
            }
        }

        const { data: collections, error, count } = await query
            .order("updated_at", { ascending: false })
            .range(offset, offset + limit - 1)

        if (error) {
            console.error("獲取收藏牆列表時出錯:", error)
            return NextResponse.json({
                success: false,
                error: "獲取收藏牆列表失敗"
            }, { status: 500 })
        }

        // 處理數據
        const processedCollections = collections?.map(collection => ({
            id: collection.id,
            name: collection.name,
            description: collection.description,
            coverImage: collection.cover_image,
            itemCount: collection.collection_items?.[0]?.count || 0,
            isPublic: collection.is_public,
            categoryId: collection.category_id,
            categoryName: collection.collection_categories?.name || null,
            createdAt: collection.created_at,
            updatedAt: collection.updated_at,
        })) || []

        return NextResponse.json({
            success: true,
            data: processedCollections,
            pagination: {
                total: count || 0,
                page,
                limit,
                totalPages: Math.ceil((count || 0) / limit)
            }
        })

    } catch (error) {
        console.error("處理獲取收藏牆列表請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
}

export async function POST(request: Request) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入 - 使用 safeGetUser 處理 AuthSessionMissingError
        const { safeGetUser } = await import("@/lib/api-utils")
        const { user, error: userError, isAuthError } = await safeGetUser(supabase)

        if (userError || isAuthError) {
            return NextResponse.json({ success: false, error: "認證失敗，請重新登入" }, { status: 401 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 解析請求體
        const { name, description, coverImage, isPublic = true, categoryId } = await request.json()

        // 驗證必要參數
        if (!name) {
            return NextResponse.json({ success: false, error: "缺少收藏牆名稱" }, { status: 400 })
        }

        // 如果指定了 categoryId，驗證分類是否存在且屬於當前用戶
        if (categoryId) {
            const { data: category, error: categoryError } = await supabase
                .from("collection_categories")
                .select("id")
                .eq("id", categoryId)
                .eq("user_id", user.id)
                .single()

            if (categoryError || !category) {
                return NextResponse.json({
                    success: false,
                    error: "指定的分類不存在"
                }, { status: 400 })
            }
        }

        // 創建收藏牆
        const { data: collection, error: insertError } = await supabase
            .from("collections")
            .insert({
                user_id: user.id,
                name,
                description: description || "",
                cover_image: coverImage || "/abstract-geometric-shapes.png",
                is_public: isPublic,
                category_id: categoryId || null,
            })
            .select()
            .single()

        if (insertError) {
            console.error("創建收藏牆時出錯:", insertError)
            return NextResponse.json({
                success: false,
                error: "創建收藏牆失敗"
            }, { status: 500 })
        }

        // 獲取包含分類資訊的收藏牆資料
        const { data: collectionWithCategory } = await supabase
            .from("collections")
            .select(`
                *,
                collection_categories!left(name)
            `)
            .eq("id", collection.id)
            .single()

        // 格式化返回數據
        const formattedCollection = {
            id: collection.id,
            name: collection.name,
            description: collection.description,
            coverImage: collection.cover_image,
            itemCount: 0, // 新創建的收藏牆沒有項目
            isPublic: collection.is_public,
            categoryId: collection.category_id,
            categoryName: collectionWithCategory?.collection_categories?.name || null,
            createdAt: collection.created_at,
            updatedAt: collection.updated_at,
        }

        return NextResponse.json({
            success: true,
            data: formattedCollection,
            message: "收藏牆創建成功"
        })

    } catch (error) {
        console.error("處理創建收藏牆請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
} 