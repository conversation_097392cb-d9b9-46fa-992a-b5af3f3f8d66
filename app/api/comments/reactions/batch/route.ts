import { type NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

/**
 * @swagger
 * /api/comments/reactions/batch:
 *   post:
 *     tags: [Comments]
 *     summary: 批量獲取評論反應
 *     description: 批量獲取多個評論的反應統計（讚/踩）和用戶的反應狀態
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [commentIds]
 *             properties:
 *               commentIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *                 example: ["123e4567-e89b-12d3-a456-************", "223e4567-e89b-12d3-a456-************"]
 *                 description: 評論 ID 陣列
 *     responses:
 *       200:
 *         description: 成功獲取評論反應資料
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     counts:
 *                       type: object
 *                       additionalProperties:
 *                         type: object
 *                         properties:
 *                           likes:
 *                             type: integer
 *                             example: 5
 *                             description: 讚的數量
 *                           dislikes:
 *                             type: integer
 *                             example: 1
 *                             description: 踩的數量
 *                       example:
 *                         "123e4567-e89b-12d3-a456-************":
 *                           likes: 5
 *                           dislikes: 1
 *                     userReactions:
 *                       type: object
 *                       additionalProperties:
 *                         type: object
 *                         properties:
 *                           liked:
 *                             type: boolean
 *                             example: true
 *                             description: 用戶是否已讚
 *                           disliked:
 *                             type: boolean
 *                             example: false
 *                             description: 用戶是否已踩
 *                       example:
 *                         "123e4567-e89b-12d3-a456-************":
 *                           liked: true
 *                           disliked: false
 *       400:
 *         description: 請求參數錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)
    const { commentIds } = await request.json()

    if (!commentIds || !Array.isArray(commentIds) || commentIds.length === 0) {
      return NextResponse.json({ success: false, error: "Invalid comment IDs" }, { status: 400 })
    }

    // 獲取當前用戶 - 使用 safeGetUser 處理 AuthSessionMissingError
    const { safeGetUser } = await import("@/lib/api-utils")
    const { user } = await safeGetUser(supabase)

    // 獲取所有評論的反應計數
    const { data: reactionCounts, error: countError } = await supabase
      .from("reactions")
      .select("item_id, reaction_type")
      .in("item_id", commentIds)
      .eq("item_type", "comment")

    if (countError) {
      console.error("Error fetching reaction counts:", countError)
      return NextResponse.json({ success: false, error: "Failed to fetch reaction counts" }, { status: 500 })
    }

    // 統計每個評論的反應數
    const counts: Record<string, { likes: number; dislikes: number }> = {}
    commentIds.forEach((id) => {
      counts[id] = { likes: 0, dislikes: 0 }
    })

    reactionCounts?.forEach((reaction) => {
      if (!counts[reaction.item_id]) {
        counts[reaction.item_id] = { likes: 0, dislikes: 0 }
      }
      if (reaction.reaction_type === "like") {
        counts[reaction.item_id].likes++
      } else if (reaction.reaction_type === "dislike") {
        counts[reaction.item_id].dislikes++
      }
    })

    // 如果用戶已登入，獲取用戶的反應狀態
    const userReactions: Record<string, { liked: boolean; disliked: boolean }> = {}
    if (user) {
      const { data: userReactionData, error: userError } = await supabase
        .from("reactions")
        .select("item_id, reaction_type")
        .eq("profile_id", user.id)
        .in("item_id", commentIds)
        .eq("item_type", "comment")

      if (!userError && userReactionData) {
        commentIds.forEach((id) => {
          userReactions[id] = { liked: false, disliked: false }
        })

        userReactionData.forEach((reaction) => {
          if (!userReactions[reaction.item_id]) {
            userReactions[reaction.item_id] = { liked: false, disliked: false }
          }
          if (reaction.reaction_type === "like") {
            userReactions[reaction.item_id].liked = true
          } else if (reaction.reaction_type === "dislike") {
            userReactions[reaction.item_id].disliked = true
          }
        })
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        counts,
        userReactions: user ? userReactions : {},
      },
    })
  } catch (error) {
    console.error("Error in batch reactions API:", error)
    return NextResponse.json({ success: false, error: "Internal server error" }, { status: 500 })
  }
}
