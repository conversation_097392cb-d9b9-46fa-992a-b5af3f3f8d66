import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

export async function GET(request: NextRequest) {
    try {
        console.log("=== Auth Status Debug ===")

        // 獲取所有 cookies
        const cookieStore = await cookies()
        const allCookies = cookieStore.getAll()

        console.log("All cookies:", allCookies.map(c => ({ name: c.name, hasValue: !!c.value })))

        // 查找認證相關的 cookies
        const authCookies = allCookies.filter(cookie =>
            cookie.name.includes('auth') ||
            cookie.name.includes('sb-') ||
            cookie.name.includes('supabase')
        )

        console.log("Auth-related cookies:", authCookies.map(c => ({
            name: c.name,
            valueLength: c.value?.length || 0,
            hasValue: !!c.value
        })))

        // 創建服務端 Supabase 客戶端
        const supabase = await createServerClient()

        // 檢查用戶認證狀態 - 使用 safeGetUser 處理 AuthSessionMissingError
        const { safeGetUser } = await import("@/lib/api-utils")
        const { user, error, isAuthError } = await safeGetUser(supabase)

        console.log("Server-side auth check:", {
            hasUser: !!user,
            userId: user?.id?.slice(0, 8),
            userEmail: user?.email,
            isAuthError,
            errorName: error?.name,
            errorMessage: error?.message
        })

        // 檢查 session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()

        console.log("Server-side session check:", {
            hasSession: !!session,
            sessionUserId: session?.user?.id?.slice(0, 8),
            expiresAt: session?.expires_at,
            errorName: sessionError?.name,
            errorMessage: sessionError?.message
        })

        return NextResponse.json({
            success: true,
            data: {
                serverSide: {
                    user: user ? {
                        id: user.id.slice(0, 8) + "...",
                        email: user.email,
                        lastSignInAt: user.last_sign_in_at
                    } : null,
                    session: session ? {
                        userId: session.user.id.slice(0, 8) + "...",
                        expiresAt: session.expires_at,
                        accessTokenLength: session.access_token?.length || 0,
                        refreshTokenLength: session.refresh_token?.length || 0
                    } : null,
                    error: error?.message || sessionError?.message || null
                },
                cookies: {
                    total: allCookies.length,
                    authRelated: authCookies.map(c => ({
                        name: c.name,
                        valueLength: c.value?.length || 0,
                        hasValue: !!c.value
                    }))
                },
                timestamp: new Date().toISOString()
            }
        })

    } catch (error: any) {
        console.error("Auth status debug error:", error)

        return NextResponse.json({
            success: false,
            error: error.message || "Debug check failed",
            timestamp: new Date().toISOString()
        }, { status: 500 })
    }
} 