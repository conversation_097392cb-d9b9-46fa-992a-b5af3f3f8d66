import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import { safeGetUser } from "@/lib/api-utils"

export async function GET() {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)

    // 獲取所有 cookies
    const allCookies = (await cookieStore).getAll()
    const authCookies = allCookies.filter(cookie => 
      cookie.name.includes('sb-') || cookie.name.includes('auth')
    )

    console.log('Debug session - All cookies:', allCookies.length)
    console.log('Debug session - Auth cookies:', authCookies.map(c => ({
      name: c.name,
      hasValue: !!c.value,
      valueLength: c.value.length
    })))

    // 使用 safeGetUser 檢查用戶
    const { user, error, isAuthError } = await safeGetUser(supabase)

    // 也嘗試直接獲取 session
    let directSession = null
    let directError = null
    try {
      const { data: { session }, error: sessionError } = await supabase.auth.getSession()
      directSession = session
      directError = sessionError
    } catch (err: any) {
      directError = err
    }

    return NextResponse.json({
      success: true,
      data: {
        safeGetUser: {
          hasUser: !!user,
          userId: user?.id,
          error: error?.message,
          isAuthError
        },
        directSession: {
          hasSession: !!directSession,
          userId: directSession?.user?.id,
          error: directError?.message
        },
        cookies: {
          total: allCookies.length,
          authCookies: authCookies.map(c => ({
            name: c.name,
            hasValue: !!c.value,
            valueLength: c.value.length
          }))
        }
      }
    })
  } catch (error: any) {
    console.error('Debug session error:', error)
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 })
  }
}
