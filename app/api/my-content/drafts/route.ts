import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

/**
 * @swagger
 * /api/my-content/drafts:
 *   get:
 *     tags: [My Content]
 *     summary: 獲取草稿內容
 *     description: 根據 ID 和類型獲取用戶的草稿內容（觀點卡或討論串）
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         required: true
 *         schema:
 *           type: string
 *           enum: [card, thread]
 *         description: 內容類型
 *         example: "card"
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 草稿 ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       200:
 *         description: 成功獲取草稿內容
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       format: uuid
 *                     title:
 *                       type: string
 *                       example: "草稿標題"
 *                     content:
 *                       type: string
 *                       example: "草稿內容"
 *                     semanticType:
 *                       type: string
 *                       example: "question"
 *                     contributionType:
 *                       type: string
 *                       example: "original"
 *                       description: 僅觀點卡有此屬性
 *                     originalAuthor:
 *                       type: string
 *                       description: 僅觀點卡有此屬性
 *                     originalUrl:
 *                       type: string
 *                       description: 僅觀點卡有此屬性
 *                     topicIds:
 *                       type: array
 *                       items:
 *                         type: string
 *                         format: uuid
 *                     subtopicIds:
 *                       type: array
 *                       items:
 *                         type: string
 *                         format: uuid
 *                     tags:
 *                       type: array
 *                       items:
 *                         type: string
 *                     status:
 *                       type: string
 *                       example: "draft"
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *       400:
 *         description: 請求參數錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: 未授權
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: 草稿不存在或無權限訪問
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

export async function GET(request: Request) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入 - 使用 safeGetUser 處理 AuthSessionMissingError
        const { safeGetUser } = await import("@/lib/api-utils")
        const { user, error: userError, isAuthError } = await safeGetUser(supabase)

        if (userError || isAuthError) {
            return NextResponse.json({ success: false, error: "認證失敗，請重新登入" }, { status: 401 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        const { searchParams } = new URL(request.url)
        const type = searchParams.get("type") // "card" | "thread"
        const id = searchParams.get("id")

        if (!type || !id) {
            return NextResponse.json({
                success: false,
                error: "缺少必要參數：type 和 id"
            }, { status: 400 })
        }

        const tableName = type === "card" ? "cards" : "threads"

        // 獲取草稿內容
        const { data: draft, error: fetchError } = await supabase
            .from(tableName)
            .select(`
        *,
        ${type === "card" ? `
          card_topics (topics (*)),
          card_subtopics (subtopics (*))
        ` : `
          thread_topics (topics (*)),
          thread_subtopics (subtopics (*))
        `}
      `)
            .eq("id", id)
            .eq("author_id", user.id)
            .eq("status", "draft")
            .single()

        if (fetchError) {
            return NextResponse.json({
                success: false,
                error: "草稿不存在或無權限訪問"
            }, { status: 404 })
        }

        // 格式化返回數據
        const formattedDraft = {
            id: draft.id,
            title: draft.title,
            content: draft.content,
            semanticType: draft.semantic_type,
            contributionType: type === "card" ? draft.contribution_type : undefined,
            originalAuthor: type === "card" ? draft.original_author : undefined,
            originalUrl: type === "card" ? draft.original_url : undefined,
            topicIds: type === "card"
                ? draft.card_topics?.map((ct: any) => ct.topics.id) || []
                : draft.thread_topics?.map((tt: any) => tt.topics.id) || [],
            subtopicIds: type === "card"
                ? draft.card_subtopics?.map((cs: any) => cs.subtopics.id) || []
                : draft.thread_subtopics?.map((ts: any) => ts.subtopics.id) || [],
            tags: type === "card"
                ? draft.card_subtopics?.map((cs: any) => cs.subtopics.name) || []
                : draft.thread_subtopics?.map((ts: any) => ts.subtopics.name) || [],
            status: draft.status,
            createdAt: draft.created_at,
            updatedAt: draft.updated_at,
        }

        return NextResponse.json({
            success: true,
            data: formattedDraft
        })

    } catch (error) {
        console.error("處理獲取草稿請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
} 