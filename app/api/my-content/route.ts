/**
 * @swagger
 * /api/my-content:
 *   get:
 *     tags: [My Content]
 *     summary: 獲取我的內容
 *     description: 獲取用戶創建的觀點卡和討論串，支援分頁、類型篩選、狀態篩選
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         required: false
 *         schema:
 *           type: string
 *           enum: [cards, threads, all]
 *           default: all
 *         description: 內容類型篩選
 *         example: "all"
 *       - in: query
 *         name: status
 *         required: false
 *         schema:
 *           type: string
 *           enum: [draft, pending, published, all]
 *           default: all
 *         description: 內容狀態篩選
 *         example: "all"
 *       - in: query
 *         name: page
 *         required: false
 *         schema:
 *           type: integer
 *           default: 1
 *           minimum: 1
 *         description: 頁碼
 *         example: 1
 *       - in: query
 *         name: limit
 *         required: false
 *         schema:
 *           type: integer
 *           default: 10
 *           minimum: 1
 *           maximum: 50
 *         description: 每頁數量
 *         example: 10
 *     responses:
 *       200:
 *         description: 成功獲取我的內容列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         format: uuid
 *                       contentType:
 *                         type: string
 *                         enum: [viewpoint, discussion]
 *                         example: "viewpoint"
 *                       semanticType:
 *                         type: string
 *                         example: "question"
 *                       title:
 *                         type: string
 *                         example: "我的觀點卡標題"
 *                       content:
 *                         type: string
 *                         example: "觀點卡的詳細內容"
 *                       topics:
 *                         type: array
 *                         items:
 *                           type: string
 *                         example: ["人工智能", "機器學習"]
 *                       subtopics:
 *                         type: array
 *                         items:
 *                           type: string
 *                         example: ["深度學習"]
 *                         description: 觀點卡專有
 *                       tags:
 *                         type: array
 *                         items:
 *                           type: string
 *                         description: 討論串專有
 *                       author:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           name:
 *                             type: string
 *                           avatar:
 *                             type: string
 *                       status:
 *                         type: string
 *                         enum: [draft, pending, published]
 *                       stats:
 *                         type: object
 *                         properties:
 *                           likes:
 *                             type: integer
 *                           dislikes:
 *                             type: integer
 *                             description: 僅觀點卡有此屬性
 *                           comments:
 *                             type: integer
 *                             description: 僅觀點卡有此屬性
 *                           replies:
 *                             type: integer
 *                             description: 僅討論串有此屬性
 *                           views:
 *                             type: integer
 *                             description: 僅討論串有此屬性
 *                       submittedAt:
 *                         type: string
 *                         format: date-time
 *                       publishedAt:
 *                         type: string
 *                         format: date-time
 *                         nullable: true
 *                       lastSaved:
 *                         type: string
 *                         format: date-time
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       example: 25
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     limit:
 *                       type: integer
 *                       example: 10
 *                     totalPages:
 *                       type: integer
 *                       example: 3
 *                 summary:
 *                   type: object
 *                   properties:
 *                     totalCards:
 *                       type: integer
 *                       example: 15
 *                       description: 觀點卡總數
 *                     totalThreads:
 *                       type: integer
 *                       example: 10
 *                       description: 討論串總數
 *                     cardsCount:
 *                       type: object
 *                       properties:
 *                         draft:
 *                           type: integer
 *                         pending:
 *                           type: integer
 *                         published:
 *                           type: integer
 *                     threadsCount:
 *                       type: object
 *                       properties:
 *                         draft:
 *                           type: integer
 *                         published:
 *                           type: integer
 *       401:
 *         description: 未授權
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *   delete:
 *     tags: [My Content]
 *     summary: 刪除我的內容
 *     description: 刪除用戶創建的觀點卡或討論串（僅限草稿狀態）
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         required: true
 *         schema:
 *           type: string
 *           enum: [card, thread]
 *         description: 內容類型
 *         example: "card"
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 內容 ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       200:
 *         description: 成功刪除內容
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "觀點卡已成功刪除"
 *       400:
 *         description: 請求參數錯誤或只能刪除草稿
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: 未授權
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: 無權限刪除此內容
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: 內容不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import { getBatchStats, enrichWithBatchStats, type BatchItem } from "@/lib/batch-stats-service"

export async function GET(request: Request) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入 - 使用 safeGetUser 處理 AuthSessionMissingError
        const { safeGetUser } = await import("@/lib/api-utils")
        const { user, error: userError, isAuthError } = await safeGetUser(supabase)

        if (userError || isAuthError) {
            return NextResponse.json({ success: false, error: "認證失敗，請重新登入" }, { status: 401 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        const { searchParams } = new URL(request.url)
        const type = searchParams.get("type") // "cards" | "threads" | "all"
        const status = searchParams.get("status") // "draft" | "pending" | "published" | "all"
        const page = parseInt(searchParams.get("page") || "1")
        const limit = parseInt(searchParams.get("limit") || "10")

        const offset = (page - 1) * limit

        let cardsData: any[] = []
        let threadsData: any[] = []
        let totalCards = 0
        let totalThreads = 0

        // 獲取觀點卡
        if (type === "cards" || type === "all") {
            let cardsQuery = supabase
                .from("cards")
                .select(`
          *,
          profiles:author_id (id, name, avatar),
          card_topics (topics (*)),
          card_subtopics (subtopics (*))
        `, { count: "exact" })
                .eq("author_id", user.id)

            // 狀態篩選
            if (status && status !== "all") {
                cardsQuery = cardsQuery.eq("status", status)
            }

            const { data: cards, error: cardsError, count: cardsCount } = await cardsQuery
                .order("updated_at", { ascending: false })
                .range(offset, offset + limit - 1)

            if (cardsError) {
                console.error("獲取觀點卡時出錯:", cardsError)
            } else if (cards && cards.length > 0) {
                // 使用 BatchStatsService 獲取統計數據
                const cardsWithStats = await enrichWithBatchStats(cards, "card", user.id)

                // 格式化觀點卡數據
                cardsData = cardsWithStats.map(card => ({
                    id: card.id,
                    contentType: "viewpoint",
                    semanticType: card.semantic_type,
                    title: card.title,
                    content: card.content,
                    topics: card.card_topics?.map((ct: any) => ct.topics.name) || [],
                    subtopics: card.card_subtopics?.map((cs: any) => cs.subtopics.name) || [],
                    author: {
                        id: card.profiles?.id || card.author_id,
                        name: card.profiles?.name || "未知用戶",
                        avatar: card.profiles?.avatar,
                    },
                    contribution_type: card.contribution_type,
                    originalAuthor: card.original_author,
                    originalSource: card.original_url,
                    timestamp: new Date(card.updated_at || card.created_at).toLocaleDateString("zh-TW"),
                    status: card.status,
                    stats: card.stats, // 包含完整的統計數據和用戶狀態
                    userStates: card.userStates,
                    submittedAt: card.created_at,
                    publishedAt: card.published_at,
                    lastSaved: card.updated_at,
                    variant: "grid",
                    features: { truncate: true },
                }))

                totalCards = cardsCount || 0
            }
        }

        // 獲取討論串
        if (type === "threads" || type === "all") {
            let threadsQuery = supabase
                .from("threads")
                .select(`
          *,
          profiles:author_id (id, name, avatar),
          thread_topics (topics (*)),
          thread_subtopics (subtopics (*))
        `, { count: "exact" })
                .eq("author_id", user.id)

            // 狀態篩選
            if (status && status !== "all") {
                threadsQuery = threadsQuery.eq("status", status)
            }

            const { data: threads, error: threadsError, count: threadsCount } = await threadsQuery
                .order("updated_at", { ascending: false })
                .range(offset, offset + limit - 1)

            if (threadsError) {
                console.error("獲取討論串時出錯:", threadsError)
            } else if (threads && threads.length > 0) {
                // 使用 BatchStatsService 獲取統計數據
                const threadsWithStats = await enrichWithBatchStats(threads, "thread", user.id)

                // 格式化討論串數據
                threadsData = threadsWithStats.map(thread => ({
                    id: thread.id,
                    contentType: "thread",
                    semanticType: thread.semantic_type,
                    title: thread.title,
                    content: thread.content,
                    topics: thread.thread_topics?.map((tt: any) => tt.topics.name) || [],
                    tags: thread.thread_subtopics?.map((ts: any) => ts.subtopics.name) || [],
                    author: {
                        id: thread.profiles?.id || thread.author_id,
                        name: thread.profiles?.name || "未知用戶",
                        avatar: thread.profiles?.avatar,
                    },
                    timestamp: new Date(thread.updated_at || thread.created_at).toLocaleDateString("zh-TW"),
                    status: thread.status,
                    stats: thread.stats, // 包含完整的統計數據和用戶狀態
                    userStates: thread.userStates,
                    submittedAt: thread.created_at,
                    publishedAt: thread.published_at,
                    lastSaved: thread.updated_at,
                    variant: "grid",
                    features: { truncate: true },
                }))

                totalThreads = threadsCount || 0
            }
        }

        // 根據類型返回數據
        let responseData = []
        let total = 0

        if (type === "cards") {
            responseData = cardsData
            total = totalCards
        } else if (type === "threads") {
            responseData = threadsData
            total = totalThreads
        } else {
            // type === "all"，合併兩種類型的數據
            responseData = [...cardsData, ...threadsData].sort((a, b) =>
                new Date(b.lastSaved || b.submittedAt).getTime() - new Date(a.lastSaved || a.submittedAt).getTime()
            )
            total = totalCards + totalThreads
        }

        return NextResponse.json({
            success: true,
            data: responseData,
            pagination: {
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit),
            },
            summary: {
                totalCards,
                totalThreads,
                cardsCount: {
                    draft: 0, // 這些會在前端或另一個專門的統計API中計算
                    pending: 0,
                    published: 0,
                },
                threadsCount: {
                    draft: 0,
                    published: 0,
                },
            },
        })

    } catch (error) {
        console.error("處理獲取用戶內容請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
}

export async function DELETE(request: Request) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入 - 使用 safeGetUser 處理 AuthSessionMissingError
        const { safeGetUser } = await import("@/lib/api-utils")
        const { user, error: userError, isAuthError } = await safeGetUser(supabase)

        if (userError || isAuthError) {
            return NextResponse.json({ success: false, error: "認證失敗，請重新登入" }, { status: 401 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        const { searchParams } = new URL(request.url)
        const type = searchParams.get("type") // "card" | "thread"
        const id = searchParams.get("id")

        if (!type || !id) {
            return NextResponse.json({
                success: false,
                error: "缺少必要參數：type 和 id"
            }, { status: 400 })
        }

        const tableName = type === "card" ? "cards" : "threads"

        // 確認該內容屬於當前用戶
        const { data: content, error: fetchError } = await supabase
            .from(tableName)
            .select("author_id, status")
            .eq("id", id)
            .single()

        if (fetchError) {
            return NextResponse.json({
                success: false,
                error: "內容不存在"
            }, { status: 404 })
        }

        if (content.author_id !== user.id) {
            return NextResponse.json({
                success: false,
                error: "無權限刪除此內容"
            }, { status: 403 })
        }

        // 只允許刪除草稿狀態的內容
        if (content.status !== "draft") {
            return NextResponse.json({
                success: false,
                error: "只能刪除草稿狀態的內容"
            }, { status: 400 })
        }

        // 刪除內容
        const { error: deleteError } = await supabase
            .from(tableName)
            .delete()
            .eq("id", id)
            .eq("author_id", user.id)

        if (deleteError) {
            console.error("刪除內容時出錯:", deleteError)
            return NextResponse.json({
                success: false,
                error: "刪除失敗"
            }, { status: 500 })
        }

        return NextResponse.json({
            success: true,
            message: `${type === "card" ? "觀點卡" : "討論串"}已成功刪除`
        })

    } catch (error) {
        console.error("處理刪除內容請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
} 