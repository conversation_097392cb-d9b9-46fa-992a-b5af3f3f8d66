import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import { getMyPostsContent } from "@/lib/my-posts-service"

export async function GET(request: Request) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入 - 使用 safeGetUser 處理 AuthSessionMissingError
        const { safeGetUser } = await import("@/lib/api-utils")
        const { user, error: userError, isAuthError } = await safeGetUser(supabase)

        if (userError || isAuthError) {
            return NextResponse.json({ success: false, error: "認證失敗，請重新登入" }, { status: 401 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 使用優化後的服務獲取數據
        const result = await getMyPostsContent(user.id)

        if (!result.success) {
            return NextResponse.json({
                success: false,
                error: result.error || "獲取數據失敗"
            }, { status: 500 })
        }

        return NextResponse.json({
            success: true,
            data: result.data
        })

    } catch (error) {
        console.error("處理獲取我的發文請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
} 