import { type NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import { safeGetUser } from "@/lib/api-utils"

/**
 * @swagger
 * /api/reactions/check:
 *   get:
 *     tags: [Reactions]
 *     summary: 檢查反應狀態
 *     description: 檢查用戶是否已對指定項目進行特定類型的反應
 *     parameters:
 *       - in: query
 *         name: itemType
 *         required: true
 *         schema:
 *           type: string
 *           enum: [card, thread, comment]
 *         description: 項目類型
 *         example: "card"
 *       - in: query
 *         name: itemId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 項目 ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *       - in: query
 *         name: reactionType
 *         required: true
 *         schema:
 *           type: string
 *           enum: [like, dislike, laugh, love, angry]
 *         description: 反應類型
 *         example: "like"
 *     responses:
 *       200:
 *         description: 成功檢查反應狀態
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     hasReacted:
 *                       type: boolean
 *                       example: true
 *                       description: 用戶是否已反應
 *                 warning:
 *                   type: string
 *                   description: 當 itemId 格式無效時的警告訊息
 *       400:
 *         description: 請求參數錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

// 檢查字符串是否為有效的 UUID
function isValidUUID(uuid: string) {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(uuid)
}

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const itemType = searchParams.get("itemType")
    const itemId = searchParams.get("itemId")
    const reactionType = searchParams.get("reactionType")

    console.log("Checking reaction:", { itemType, itemId, reactionType })

    if (!itemType || !itemId || !reactionType) {
      return NextResponse.json({ success: false, error: "缺少必要參數" }, { status: 400 })
    }

    // 檢查 itemId 是否為有效的 UUID
    if (!isValidUUID(itemId)) {
      console.log(`Invalid UUID format: ${itemId}, returning false`)
      // 如果不是有效的 UUID，直接返回未反應狀態
      return NextResponse.json({
        success: true,
        data: { hasReacted: false },
        warning: "Invalid UUID format, assuming no reaction"
      })
    }

    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)

    // 獲取當前用戶（處理 AuthSessionMissingError）
    const { user, error: userError, isAuthError } = await safeGetUser(supabase)

    // 任何用戶相關的錯誤都返回未反應狀態，而不是錯誤
    if (userError || isAuthError || !user) {
      console.debug("用戶未登入或認證錯誤，返回未反應狀態")
      return NextResponse.json({ success: true, data: { hasReacted: false } })
    }

    const userId = user.id

    // 檢查用戶是否已經對該項目進行了反應
    const { data, error } = await supabase
      .from("reactions")
      .select("id")
      .eq("item_type", itemType)
      .eq("item_id", itemId)
      .eq("reaction_type", reactionType)
      .eq("profile_id", userId)
      .maybeSingle()

    if (error) {
      console.error("檢查反應時出錯:", error)
      return NextResponse.json({ success: false, error: error.message }, { status: 500 })
    }

    return NextResponse.json({ success: true, data: { hasReacted: !!data } })
  } catch (error) {
    console.error("處理檢查反應時出錯:", error)
    return NextResponse.json({ success: false, error: "內部伺服器錯誤" }, { status: 500 })
  }
}
