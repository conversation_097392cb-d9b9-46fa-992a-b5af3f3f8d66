import { type NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

/**
 * @swagger
 * /api/reactions/count:
 *   get:
 *     tags: [Reactions]
 *     summary: 獲取反應統計
 *     description: 獲取指定項目的各類型反應統計數量
 *     parameters:
 *       - in: query
 *         name: itemType
 *         required: true
 *         schema:
 *           type: string
 *           enum: [card, thread, comment]
 *         description: 項目類型
 *         example: "card"
 *       - in: query
 *         name: itemId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 項目 ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       200:
 *         description: 成功獲取反應統計
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   additionalProperties:
 *                     type: integer
 *                   example:
 *                     like: 25
 *                     dislike: 3
 *                     love: 8
 *                     laugh: 12
 *                   description: 各類型反應的統計數量
 *                 warning:
 *                   type: string
 *                   description: 當 itemId 格式無效時的警告訊息
 *       400:
 *         description: 請求參數錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

// 檢查字符串是否為有效的 UUID
function isValidUUID(uuid: string) {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(uuid)
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const itemType = searchParams.get("itemType")
    const itemId = searchParams.get("itemId")

    console.log("Fetching reaction counts:", { itemType, itemId })

    if (!itemType || !itemId) {
      return NextResponse.json({ success: false, error: "缺少必要參數" }, { status: 400 })
    }

    // 檢查 itemId 是否為有效的 UUID
    if (!isValidUUID(itemId)) {
      console.log(`Invalid UUID format: ${itemId}, returning zero counts`)
      // 如果不是有效的 UUID，返回零計數
      return NextResponse.json({
        success: true,
        data: { like: 0, dislike: 0 },
        warning: "Invalid UUID format, returning zero counts",
      })
    }

    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)

    // 獲取反應計數
    const { data, error } = await supabase
      .from("reactions")
      .select("reaction_type")
      .eq("item_type", itemType)
      .eq("item_id", itemId)

    if (error) {
      console.error("獲取反應計數時出錯:", error)
      return NextResponse.json({ success: false, error: error.message }, { status: 500 })
    }

    // 計算每種反應類型的數量
    const counts: Record<string, number> = {}
    data.forEach((reaction) => {
      const type = reaction.reaction_type
      counts[type] = (counts[type] || 0) + 1
    })

    console.log("Reaction counts result:", counts)

    return NextResponse.json({ success: true, data: counts })
  } catch (error) {
    console.error("處理反應計數時出錯:", error)
    return NextResponse.json({ success: false, error: "內部伺服器錯誤" }, { status: 500 })
  }
}
