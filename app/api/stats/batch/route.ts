import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import { safeGetUser } from "@/lib/api-utils"

interface BatchStatsRequest {
    items: Array<{
        id: string
        type: "card" | "thread"
    }>
}

interface BatchStatsResponse {
    success: boolean
    data: {
        stats: Record<string, {
            likes: number
            dislikes: number
            comments: number
            bookmarks: number
        }>
        userStates: Record<string, {
            liked: boolean
            disliked: boolean
            bookmarked: boolean
        }>
    }
}

/**
 * @swagger
 * /api/stats/batch:
 *   post:
 *     tags: [Stats]
 *     summary: 批量獲取統計數據
 *     description: 一次性獲取多個項目的統計數據和用戶狀態
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               items:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       format: uuid
 *                     type:
 *                       type: string
 *                       enum: [card, thread]
 *     responses:
 *       200:
 *         description: 成功獲取批量統計數據
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     stats:
 *                       type: object
 *                       additionalProperties:
 *                         type: object
 *                         properties:
 *                           likes:
 *                             type: integer
 *                           dislikes:
 *                             type: integer
 *                           comments:
 *                             type: integer
 *                           bookmarks:
 *                             type: integer
 *                     userStates:
 *                       type: object
 *                       additionalProperties:
 *                         type: object
 *                         properties:
 *                           liked:
 *                             type: boolean
 *                           disliked:
 *                             type: boolean
 *                           bookmarked:
 *                             type: boolean
 */
export async function POST(request: NextRequest) {
    try {
        const { items }: BatchStatsRequest = await request.json()

        if (!items || !Array.isArray(items) || items.length === 0) {
            return NextResponse.json({ success: false, error: "項目列表不能為空" }, { status: 400 })
        }

        // 限制批量大小，避免查詢過大
        if (items.length > 50) {
            return NextResponse.json({ success: false, error: "批量項目數量不能超過50個" }, { status: 400 })
        }

        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 獲取用戶信息
        const { user } = await safeGetUser(supabase)

        // 提取所有項目ID
        const itemIds = items.map(item => item.id)
        const cardIds = items.filter(item => item.type === "card").map(item => item.id)
        const threadIds = items.filter(item => item.type === "thread").map(item => item.id)

        const stats: Record<string, { likes: number; dislikes: number; comments: number; bookmarks: number }> = {}
        const userStates: Record<string, { liked: boolean; disliked: boolean; bookmarked: boolean }> = {}

        // 初始化統計數據
        items.forEach(item => {
            stats[item.id] = { likes: 0, dislikes: 0, comments: 0, bookmarks: 0 }
            userStates[item.id] = { liked: false, disliked: false, bookmarked: false }
        })

        // 批量獲取反應統計
        if (itemIds.length > 0) {
            const { data: reactions, error: reactionsError } = await supabase
                .from("reactions")
                .select("item_id, item_type, reaction_type, profile_id")
                .in("item_id", itemIds)
                .in("item_type", items.map(item => item.type))

            if (reactionsError) {
                console.error("獲取反應統計錯誤:", reactionsError)
                return NextResponse.json({ success: false, error: "獲取反應統計失敗" }, { status: 500 })
            }

            // 計算反應統計
            reactions?.forEach(reaction => {
                if (stats[reaction.item_id]) {
                    if (reaction.reaction_type === "like") {
                        stats[reaction.item_id].likes++
                    } else if (reaction.reaction_type === "dislike") {
                        stats[reaction.item_id].dislikes++
                    }

                    // 檢查用戶狀態
                    if (user && reaction.profile_id === user.id) {
                        if (reaction.reaction_type === "like") {
                            userStates[reaction.item_id].liked = true
                        } else if (reaction.reaction_type === "dislike") {
                            userStates[reaction.item_id].disliked = true
                        }
                    }
                }
            })
        }

        // 批量獲取收藏統計
        if (itemIds.length > 0) {
            const { data: bookmarks, error: bookmarksError } = await supabase
                .from("bookmarks")
                .select("item_id, item_type, profile_id")
                .in("item_id", itemIds)
                .in("item_type", items.map(item => item.type))

            if (bookmarksError) {
                console.error("獲取收藏統計錯誤:", bookmarksError)
                return NextResponse.json({ success: false, error: "獲取收藏統計失敗" }, { status: 500 })
            }

            // 計算收藏統計
            bookmarks?.forEach(bookmark => {
                if (stats[bookmark.item_id]) {
                    stats[bookmark.item_id].bookmarks++

                    // 檢查用戶狀態
                    if (user && bookmark.profile_id === user.id) {
                        userStates[bookmark.item_id].bookmarked = true
                    }
                }
            })
        }

        // 批量獲取評論統計 - 卡片
        if (cardIds.length > 0) {
            const { data: cardComments, error: cardCommentsError } = await supabase
                .from("comments")
                .select("root_item_id")
                .eq("root_item_type", "card")
                .in("root_item_id", cardIds)

            if (cardCommentsError) {
                console.error("獲取卡片評論統計錯誤:", cardCommentsError)
            } else {
                // 計算每個卡片的評論數
                const commentCounts: Record<string, number> = {}
                cardComments?.forEach(comment => {
                    commentCounts[comment.root_item_id] = (commentCounts[comment.root_item_id] || 0) + 1
                })

                Object.entries(commentCounts).forEach(([cardId, count]) => {
                    if (stats[cardId]) {
                        stats[cardId].comments = count
                    }
                })
            }
        }

        // 批量獲取評論統計 - 討論串
        if (threadIds.length > 0) {
            const { data: threadComments, error: threadCommentsError } = await supabase
                .from("comments")
                .select("root_item_id")
                .eq("root_item_type", "thread")
                .in("root_item_id", threadIds)

            if (threadCommentsError) {
                console.error("獲取討論串評論統計錯誤:", threadCommentsError)
            } else {
                // 計算每個討論串的評論數
                const commentCounts: Record<string, number> = {}
                threadComments?.forEach(comment => {
                    commentCounts[comment.root_item_id] = (commentCounts[comment.root_item_id] || 0) + 1
                })

                Object.entries(commentCounts).forEach(([threadId, count]) => {
                    if (stats[threadId]) {
                        stats[threadId].comments = count
                    }
                })
            }
        }

        return NextResponse.json({
            success: true,
            data: {
                stats,
                userStates: user ? userStates : {}
            }
        })

    } catch (error) {
        console.error("批量統計API錯誤:", error)
        return NextResponse.json({ success: false, error: "內部服務器錯誤" }, { status: 500 })
    }
} 