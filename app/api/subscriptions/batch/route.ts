import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import { checkSubscriptionStatus, getSubscriptionStats } from "@/lib/subscription-service"

/**
 * @swagger
 * /api/subscriptions/batch:
 *   post:
 *     tags: [Subscriptions]
 *     summary: 批量獲取訂閱狀態和統計
 *     description: 一次性獲取多個項目的訂閱狀態和統計信息
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               items:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     itemType:
 *                       type: string
 *                       enum: [topic, subtopic]
 *                       example: topic
 *                     itemId:
 *                       type: string
 *                       format: uuid
 *                       example: "123e4567-e89b-12d3-a456-************"
 *                   required:
 *                     - itemType
 *                     - itemId
 *             required:
 *               - items
 *     responses:
 *       200:
 *         description: 成功獲取批量訂閱數據
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     statuses:
 *                       type: object
 *                       additionalProperties:
 *                         type: object
 *                         properties:
 *                           subscribed:
 *                             type: boolean
 *                           subscription:
 *                             type: object
 *                     stats:
 *                       type: object
 *                       additionalProperties:
 *                         type: object
 *                         properties:
 *                           subscriber_count:
 *                             type: integer
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未授權
 *       500:
 *         description: 伺服器錯誤
 */

export async function POST(request: NextRequest) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶身份驗證 - 使用 safeGetUser 處理 AuthSessionMissingError
        const { safeGetUser } = await import("@/lib/api-utils")
        const { user, error: authError, isAuthError } = await safeGetUser(supabase)
        if (authError || isAuthError || !user) {
            return NextResponse.json(
                { success: false, error: "未授權訪問" },
                { status: 401 }
            )
        }

        const body = await request.json()
        const { items } = body

        // 驗證請求參數
        if (!Array.isArray(items) || items.length === 0) {
            return NextResponse.json(
                { success: false, error: "items 必須是非空數組" },
                { status: 400 }
            )
        }

        // 驗證每個項目的格式
        for (const item of items) {
            if (!item.itemType || !item.itemId) {
                return NextResponse.json(
                    { success: false, error: "每個項目必須包含 itemType 和 itemId" },
                    { status: 400 }
                )
            }

            if (!['topic', 'subtopic'].includes(item.itemType)) {
                return NextResponse.json(
                    { success: false, error: "itemType 必須是 'topic' 或 'subtopic'" },
                    { status: 400 }
                )
            }
        }

        // 批量處理訂閱狀態和統計
        const statusPromises = items.map(item =>
            checkSubscriptionStatus(user.id, item.itemType as 'topic' | 'subtopic', item.itemId)
                .then(result => ({ itemId: item.itemId, result }))
        )

        const statsPromises = items.map(item =>
            getSubscriptionStats(item.itemType as 'topic' | 'subtopic', item.itemId)
                .then(result => ({ itemId: item.itemId, result }))
        )

        const [statusResults, statsResults] = await Promise.all([
            Promise.all(statusPromises),
            Promise.all(statsPromises)
        ])

        // 整理結果
        const statuses: Record<string, any> = {}
        const stats: Record<string, any> = {}

        statusResults.forEach(({ itemId, result }) => {
            if (result.success) {
                statuses[itemId] = result.data
            } else {
                statuses[itemId] = { subscribed: false }
                console.error(`Failed to get subscription status for ${itemId}:`, result.error)
            }
        })

        statsResults.forEach(({ itemId, result }) => {
            if (result.success) {
                stats[itemId] = result.data
            } else {
                stats[itemId] = { subscriber_count: 0 }
                console.error(`Failed to get subscription stats for ${itemId}:`, result.error)
            }
        })

        return NextResponse.json({
            success: true,
            data: {
                statuses,
                stats
            }
        })
    } catch (error) {
        console.error("批量訂閱 API 錯誤:", error)
        return NextResponse.json(
            { success: false, error: "內部伺服器錯誤" },
            { status: 500 }
        )
    }
} 