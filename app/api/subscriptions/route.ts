import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import {
    getUserSubscriptions,
    toggleSubscription,
    subscribeToItem,
    unsubscribeFromItem,
    validateSubscriptionItem
} from "@/lib/subscription-service"

/**
 * @swagger
 * /api/subscriptions:
 *   get:
 *     tags: [Subscriptions]
 *     summary: 獲取用戶訂閱列表
 *     description: 獲取當前用戶的所有訂閱
 *     parameters:
 *       - in: query
 *         name: itemType
 *         required: false
 *         schema:
 *           type: string
 *           enum: [topic, subtopic]
 *         description: 過濾特定類型的訂閱
 *     responses:
 *       200:
 *         description: 成功獲取訂閱列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Subscription'
 *       401:
 *         description: 未授權
 *       500:
 *         description: 伺服器錯誤
 *   post:
 *     tags: [Subscriptions]
 *     summary: 訂閱或取消訂閱項目
 *     description: 切換對主題或子主題的訂閱狀態
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               itemType:
 *                 type: string
 *                 enum: [topic, subtopic]
 *                 example: topic
 *               itemId:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               action:
 *                 type: string
 *                 enum: [subscribe, unsubscribe, toggle]
 *                 example: toggle
 *     responses:
 *       200:
 *         description: 操作成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     subscribed:
 *                       type: boolean
 *                       example: true
 *                     subscription:
 *                       $ref: '#/components/schemas/Subscription'
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未授權
 *       500:
 *         description: 伺服器錯誤
 */

export async function GET(request: NextRequest) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶身份驗證 - 使用 safeGetUser 處理 AuthSessionMissingError
        const { safeGetUser } = await import("@/lib/api-utils")
        const { user, error: authError, isAuthError } = await safeGetUser(supabase)
        if (authError || isAuthError || !user) {
            return NextResponse.json(
                { success: false, error: "未授權訪問" },
                { status: 401 }
            )
        }

        const searchParams = request.nextUrl.searchParams
        const itemType = searchParams.get("itemType") as 'topic' | 'subtopic' | null

        // 使用服務層獲取訂閱列表
        const result = await getUserSubscriptions(user.id, itemType || undefined)

        if (!result.success) {
            return NextResponse.json(
                { success: false, error: result.error },
                { status: 500 }
            )
        }

        return NextResponse.json({
            success: true,
            data: result.data,
        })
    } catch (error) {
        console.error("訂閱 API 錯誤:", error)
        return NextResponse.json(
            { success: false, error: "內部伺服器錯誤" },
            { status: 500 }
        )
    }
}

export async function POST(request: NextRequest) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶身份驗證 - 使用 safeGetUser 處理 AuthSessionMissingError
        const { safeGetUser } = await import("@/lib/api-utils")
        const { user, error: authError, isAuthError } = await safeGetUser(supabase)
        if (authError || isAuthError || !user) {
            return NextResponse.json(
                { success: false, error: "未授權訪問" },
                { status: 401 }
            )
        }

        // 解析請求體
        const body = await request.json()
        const { itemType, itemId, action = 'toggle' } = body

        // 驗證請求參數
        if (!itemType || !itemId) {
            return NextResponse.json(
                { success: false, error: "缺少必要參數 itemType 或 itemId" },
                { status: 400 }
            )
        }

        if (!['topic', 'subtopic'].includes(itemType)) {
            return NextResponse.json(
                { success: false, error: "itemType 必須是 'topic' 或 'subtopic'" },
                { status: 400 }
            )
        }

        if (!['subscribe', 'unsubscribe', 'toggle'].includes(action)) {
            return NextResponse.json(
                { success: false, error: "action 必須是 'subscribe', 'unsubscribe' 或 'toggle'" },
                { status: 400 }
            )
        }

        // 驗證項目是否存在
        const validationResult = await validateSubscriptionItem(itemType as 'topic' | 'subtopic', itemId)
        if (!validationResult.success) {
            return NextResponse.json(
                { success: false, error: validationResult.error },
                { status: 404 }
            )
        }

        // 使用服務層處理訂閱邏輯
        let result

        if (action === 'toggle') {
            result = await toggleSubscription(user.id, itemType as 'topic' | 'subtopic', itemId)
        } else if (action === 'subscribe') {
            result = await subscribeToItem(user.id, itemType as 'topic' | 'subtopic', itemId)
        } else if (action === 'unsubscribe') {
            result = await unsubscribeFromItem(user.id, itemType as 'topic' | 'subtopic', itemId)
        }

        if (!result || !result.success) {
            return NextResponse.json(
                { success: false, error: result?.error || "操作失敗" },
                { status: 500 }
            )
        }

        return NextResponse.json({
            success: true,
            data: result.data,
            message: result.message
        })
    } catch (error) {
        console.error("訂閱 API 錯誤:", error)
        return NextResponse.json(
            { success: false, error: "內部伺服器錯誤" },
            { status: 500 }
        )
    }
} 