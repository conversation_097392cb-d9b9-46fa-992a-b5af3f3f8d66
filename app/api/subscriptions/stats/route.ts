import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import { getSubscriptionStats } from "@/lib/subscription-service"

/**
 * @swagger
 * /api/subscriptions/stats:
 *   get:
 *     tags: [Subscriptions]
 *     summary: 獲取訂閱統計
 *     description: 獲取特定主題或子主題的訂閱人數統計
 *     parameters:
 *       - in: query
 *         name: itemType
 *         required: true
 *         schema:
 *           type: string
 *           enum: [topic, subtopic]
 *         description: 項目類型
 *       - in: query
 *         name: itemId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 項目 ID
 *     responses:
 *       200:
 *         description: 成功獲取訂閱統計
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     item_type:
 *                       type: string
 *                       enum: [topic, subtopic]
 *                       example: topic
 *                     item_id:
 *                       type: string
 *                       format: uuid
 *                       example: "123e4567-e89b-12d3-a456-************"
 *                     subscriber_count:
 *                       type: integer
 *                       example: 42
 *       400:
 *         description: 請求參數錯誤
 *       500:
 *         description: 伺服器錯誤
 */

export async function GET(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const itemType = searchParams.get("itemType")
        const itemId = searchParams.get("itemId")

        // 驗證請求參數
        if (!itemType || !itemId) {
            return NextResponse.json(
                { success: false, error: "缺少必要參數 itemType 或 itemId" },
                { status: 400 }
            )
        }

        if (!['topic', 'subtopic'].includes(itemType)) {
            return NextResponse.json(
                { success: false, error: "itemType 必須是 'topic' 或 'subtopic'" },
                { status: 400 }
            )
        }

        // 使用服務層獲取訂閱統計
        const result = await getSubscriptionStats(itemType as 'topic' | 'subtopic', itemId)

        if (!result.success) {
            return NextResponse.json(
                { success: false, error: result.error },
                { status: 500 }
            )
        }

        return NextResponse.json({
            success: true,
            data: result.data,
        })
    } catch (error) {
        console.error("訂閱統計 API 錯誤:", error)
        return NextResponse.json(
            { success: false, error: "內部伺服器錯誤" },
            { status: 500 }
        )
    }
} 