import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import { checkSubscriptionStatus } from "@/lib/subscription-service"

/**
 * @swagger
 * /api/subscriptions/status:
 *   get:
 *     tags: [Subscriptions]
 *     summary: 檢查訂閱狀態
 *     description: 檢查用戶是否已訂閱特定主題或子主題
 *     parameters:
 *       - in: query
 *         name: itemType
 *         required: true
 *         schema:
 *           type: string
 *           enum: [topic, subtopic]
 *         description: 項目類型
 *       - in: query
 *         name: itemId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 項目 ID
 *     responses:
 *       200:
 *         description: 成功獲取訂閱狀態
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     subscribed:
 *                       type: boolean
 *                       example: true
 *                     subscription:
 *                       $ref: '#/components/schemas/Subscription'
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未授權
 *       500:
 *         description: 伺服器錯誤
 */

export async function GET(request: NextRequest) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶身份驗證 - 使用 safeGetUser 處理 AuthSessionMissingError
        const { safeGetUser } = await import("@/lib/api-utils")
        const { user, error: authError, isAuthError } = await safeGetUser(supabase)
        if (authError || isAuthError || !user) {
            return NextResponse.json(
                { success: false, error: "未授權訪問" },
                { status: 401 }
            )
        }

        const searchParams = request.nextUrl.searchParams
        const itemType = searchParams.get("itemType")
        const itemId = searchParams.get("itemId")

        // 驗證請求參數
        if (!itemType || !itemId) {
            return NextResponse.json(
                { success: false, error: "缺少必要參數 itemType 或 itemId" },
                { status: 400 }
            )
        }

        if (!['topic', 'subtopic'].includes(itemType)) {
            return NextResponse.json(
                { success: false, error: "itemType 必須是 'topic' 或 'subtopic'" },
                { status: 400 }
            )
        }

        // 使用服務層檢查訂閱狀態
        const result = await checkSubscriptionStatus(user.id, itemType as 'topic' | 'subtopic', itemId)

        if (!result.success) {
            return NextResponse.json(
                { success: false, error: result.error },
                { status: 500 }
            )
        }

        return NextResponse.json({
            success: true,
            data: result.data,
        })
    } catch (error) {
        console.error("訂閱狀態 API 錯誤:", error)
        return NextResponse.json(
            { success: false, error: "內部伺服器錯誤" },
            { status: 500 }
        )
    }
} 