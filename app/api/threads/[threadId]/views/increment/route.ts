import { NextResponse } from "next/server"
import { incrementThreadViewCount } from "@/lib/thread-service"

/**
 * @swagger
 * /api/threads/{threadId}/views/increment:
 *   post:
 *     tags: [Threads]
 *     summary: 增加討論串瀏覽數
 *     description: 對指定討論串的瀏覽數 +1
 *     parameters:
 *       - in: path
 *         name: threadId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 討論串 ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       200:
 *         description: 成功增加瀏覽數
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     count:
 *                       type: integer
 *                       example: 151
 *                       description: 新的瀏覽總數
 *       404:
 *         description: 討論串不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

export async function POST(request: Request, { params }: { params: Promise<{ threadId: string }> }) {
    try {
        const { threadId } = await params
        const response = await incrementThreadViewCount(threadId)
        return NextResponse.json(response)
    } catch (error) {
        console.error("Error incrementing thread view count:", error)
        return NextResponse.json({ success: false, data: null, error: "Failed to increment view count" }, { status: 500 })
    }
} 