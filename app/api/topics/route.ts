import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

/**
 * @swagger
 * /api/topics:
 *   get:
 *     tags: [Topics]
 *     summary: 獲取主題列表
 *     description: 獲取所有可用的主題，可選擇包含子主題
 *     parameters:
 *       - in: query
 *         name: includeSubtopics
 *         required: false
 *         schema:
 *           type: boolean
 *           default: false
 *         description: 是否包含子主題
 *       - in: query
 *         name: limit
 *         required: false
 *         schema:
 *           type: integer
 *           default: 100
 *           minimum: 1
 *           maximum: 1000
 *         description: 回傳結果的最大數量
 *     responses:
 *       200:
 *         description: 成功獲取主題列表
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Topic'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
export async function GET(request: NextRequest) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)
        const searchParams = request.nextUrl.searchParams

        const includeSubtopics = searchParams.get("includeSubtopics") === "true"
        const limit = Number.parseInt(searchParams.get("limit") || "100", 10)

        let query = supabase
            .from("topics")
            .select(`
                id,
                name,
                slug,
                description
                ${includeSubtopics ? `, subtopics:subtopics!topic_id(id, name, slug)` : ""}
            `)
            .order("name")
            .limit(limit)

        const { data: topics, error } = await query

        if (error) {
            console.error("Error fetching topics:", error)
            return NextResponse.json(
                { success: false, error: "Failed to fetch topics" },
                { status: 500 }
            )
        }

        return NextResponse.json({
            success: true,
            data: topics || [],
        })
    } catch (error) {
        console.error("Topics API error:", error)
        return NextResponse.json(
            { success: false, error: "Internal server error" },
            { status: 500 }
        )
    }
} 