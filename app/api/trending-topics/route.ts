/**
 * @swagger
 * /api/trending-topics:
 *   get:
 *     tags: [Topics]
 *     summary: 獲取熱門主題
 *     description: 獲取熱門主題列表，依據觀點卡和討論串的數量進行排序。返回前10個最活躍的主題。
 *     responses:
 *       200:
 *         description: 成功獲取熱門主題列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 topics:
 *                   type: array
 *                   description: 熱門主題列表，按活躍度降序排列
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         format: uuid
 *                         description: 主題 ID
 *                         example: "123e4567-e89b-12d3-a456-************"
 *                       name:
 *                         type: string
 *                         description: 主題名稱
 *                         example: "人工智能"
 *                       count:
 *                         type: integer
 *                         description: 相關內容總數（觀點卡 + 討論串）
 *                         example: 42
 *                         minimum: 0
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Failed to fetch trending topics"
 */
import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

export async function GET() {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)

    // 獲取熱門主題
    // 這裡使用一個簡單的查詢，實際應該基於卡片和討論的數量或熱度
    const { data: topics, error } = await supabase.from("TOPICS").select("id, name").limit(10)

    if (error) throw error

    // 為每個主題獲取相關內容數量
    const topicsWithCount = await Promise.all(
      topics.map(async (topic) => {
        // 獲取相關卡片數量
        const { count: cardCount } = await supabase
          .from("CARD_TOPICS")
          .select("*", { count: "exact" })
          .eq("topic_id", topic.id)

        // 獲取相關討論數量
        const { count: threadCount } = await supabase
          .from("THREAD_TOPICS")
          .select("*", { count: "exact" })
          .eq("topic_id", topic.id)

        return {
          id: topic.id,
          name: topic.name,
          count: (cardCount || 0) + (threadCount || 0),
        }
      }),
    )

    // 按內容數量排序
    topicsWithCount.sort((a, b) => b.count - a.count)

    return NextResponse.json({
      success: true,
      topics: topicsWithCount,
    })
  } catch (error) {
    console.error("Trending topics API error:", error)
    return NextResponse.json({ success: false, error: "Failed to fetch trending topics" }, { status: 500 })
  }
}
