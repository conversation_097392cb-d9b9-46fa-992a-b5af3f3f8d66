"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON><PERSON>riangle, CheckCircle } from "lucide-react"

export default function AuthCleanupPage() {
    const [isClearing, setIsClearing] = useState(false)
    const [clearingStatus, setClearingStatus] = useState<string[]>([])
    const [isComplete, setIsComplete] = useState(false)

    const performCleanup = () => {
        if (typeof window === 'undefined') return

        setIsClearing(true)
        setClearingStatus([])

        const status: string[] = []

        try {
            // 1. 清除所有 localStorage 中的認證數據
            const localStorageKeys = Object.keys(localStorage)
            const authKeys = localStorageKeys.filter(key =>
                key.includes('sb-') ||
                key.includes('supabase') ||
                key.includes('auth') ||
                key.includes('session')
            )

            if (authKeys.length > 0) {
                authKeys.forEach(key => localStorage.removeItem(key))
                status.push(`✅ 清除了 ${authKeys.length} 個 localStorage 項目：${authKeys.join(', ')}`)
            } else {
                status.push("ℹ️ 未發現需要清除的 localStorage 項目")
            }

            // 2. 清除所有認證相關的 cookies
            const cookies = document.cookie.split(';')
            const authCookies = cookies.filter(cookie => {
                const name = cookie.trim().split('=')[0]
                return name.includes('sb-') ||
                    name.includes('supabase') ||
                    name.includes('auth')
            })

            if (authCookies.length > 0) {
                authCookies.forEach(cookie => {
                    const name = cookie.trim().split('=')[0]
                    // 清除 cookie 的所有可能路徑
                    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`
                    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=${window.location.hostname}`
                    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=.${window.location.hostname}`
                })
                status.push(`✅ 清除了 ${authCookies.length} 個認證 cookies`)
            } else {
                status.push("ℹ️ 未發現需要清除的認證 cookies")
            }

            // 3. 清除 sessionStorage
            const sessionStorageKeys = Object.keys(sessionStorage)
            const authSessionKeys = sessionStorageKeys.filter(key =>
                key.includes('sb-') ||
                key.includes('supabase') ||
                key.includes('auth')
            )

            if (authSessionKeys.length > 0) {
                authSessionKeys.forEach(key => sessionStorage.removeItem(key))
                status.push(`✅ 清除了 ${authSessionKeys.length} 個 sessionStorage 項目`)
            } else {
                status.push("ℹ️ 未發現需要清除的 sessionStorage 項目")
            }

            // 4. 嘗試清除 IndexedDB 中的 Supabase 數據
            if ('indexedDB' in window) {
                status.push("ℹ️ 如果有 IndexedDB 數據，需要手動清除或重新啟動瀏覽器")
            }

            status.push("🎉 清理完成！建議重新載入頁面或重新啟動瀏覽器")
            setIsComplete(true)

        } catch (error: any) {
            status.push(`❌ 清理過程中發生錯誤：${error.message}`)
        }

        setClearingStatus(status)
        setIsClearing(false)
    }

    const reloadPage = () => {
        window.location.reload()
    }

    return (
        <div className="container mx-auto p-6 max-w-2xl">
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <AlertTriangle className="h-6 w-6 text-orange-500" />
                        身份驗證數據清理工具
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                            此工具會清除瀏覽器中所有與 Supabase 認證相關的數據，包括：
                            <ul className="mt-2 list-disc list-inside space-y-1">
                                <li>LocalStorage 中的認證 tokens</li>
                                <li>認證相關的 Cookies</li>
                                <li>SessionStorage 中的臨時數據</li>
                            </ul>
                            清理後您需要重新登入。
                        </AlertDescription>
                    </Alert>

                    <div className="space-y-4">
                        <Button
                            onClick={performCleanup}
                            disabled={isClearing}
                            className="w-full"
                            variant="destructive"
                        >
                            {isClearing ? "清理中..." : "開始清理認證數據"}
                        </Button>

                        {clearingStatus.length > 0 && (
                            <div className="space-y-2">
                                <h3 className="font-semibold">清理結果：</h3>
                                <div className="bg-muted p-4 rounded-md space-y-1">
                                    {clearingStatus.map((status, index) => (
                                        <div key={index} className="text-sm font-mono">
                                            {status}
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {isComplete && (
                            <div className="space-y-2">
                                <Alert>
                                    <CheckCircle className="h-4 w-4" />
                                    <AlertDescription>
                                        清理完成！請重新載入頁面或前往登入頁面。
                                    </AlertDescription>
                                </Alert>

                                <div className="flex gap-2">
                                    <Button onClick={reloadPage} className="flex-1">
                                        重新載入頁面
                                    </Button>
                                    <Button
                                        onClick={() => window.location.href = '/auth/login'}
                                        variant="outline"
                                        className="flex-1"
                                    >
                                        前往登入頁面
                                    </Button>
                                </div>
                            </div>
                        )}
                    </div>
                </CardContent>
            </Card>
        </div>
    )
} 