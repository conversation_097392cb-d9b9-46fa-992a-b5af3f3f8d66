"use client"

import { useAuth } from "@/contexts/auth-context"
import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export default function AuthFixTestPage() {
    const { user, isAuthenticated, isLoading } = useAuth()
    const [windowId] = useState(() => Math.random().toString(36).substr(2, 6))
    const [logs, setLogs] = useState<string[]>([])
    const [sessionInfo, setSessionInfo] = useState<any>(null)

    const addLog = (message: string) => {
        const timestamp = new Date().toLocaleTimeString()
        setLogs(prev => [`[${timestamp}] ${message}`, ...prev].slice(0, 15))
    }

    useEffect(() => {
        addLog(`視窗 ${windowId} 初始化 - 認證=${isAuthenticated}, 載入=${isLoading}`)
    }, [windowId])

    useEffect(() => {
        addLog(`狀態變化 - 認證=${isAuthenticated}, 載入=${isLoading}, 用戶=${user?.email || '無'}`)
    }, [isAuthenticated, isLoading, user])

    const openNewTab = () => {
        window.open('/auth-fix-test', '_blank')
        addLog("開啟新標籤頁")
    }

    const openLibrary = () => {
        window.open('/library', '_blank')
        addLog("開啟 /library 標籤頁")
    }

    const checkAuthState = async () => {
        addLog("手動檢查認證狀態")
        const { createBrowserClient } = await import("@/lib/supabase/client")
        const supabase = createBrowserClient()

        try {
            const { data: { session }, error } = await supabase.auth.getSession()
            setSessionInfo({
                hasSession: !!session,
                hasUser: !!session?.user,
                userId: session?.user?.id?.slice(0, 8) || 'none',
                email: session?.user?.email || 'none',
                error: error?.message || 'none'
            })
            addLog(`Session 檢查: ${session ? '有會話' : '無會話'}`)
        } catch (error) {
            addLog(`檢查失敗: ${error}`)
        }
    }

    return (
        <div className="container mx-auto p-6 space-y-6">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">認證修復測試（改進版）</h1>
                <Badge variant="outline">視窗 ID: {windowId}</Badge>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* 當前狀態 */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            當前狀態
                            {isLoading ? (
                                <Badge variant="secondary">載入中</Badge>
                            ) : (
                                <Badge variant={isAuthenticated ? "default" : "destructive"}>
                                    {isAuthenticated ? "已認證" : "未認證"}
                                </Badge>
                            )}
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                        <div><strong>用戶郵箱:</strong> {user?.email || "無"}</div>
                        <div><strong>載入狀態:</strong> {isLoading ? "載入中" : "完成"}</div>
                        <div><strong>認證狀態:</strong> {isAuthenticated ? "已認證" : "未認證"}</div>
                        <div><strong>用戶ID:</strong> {user?.id?.slice(0, 8) || "無"}</div>
                    </CardContent>
                </Card>

                {/* Session 資訊 */}
                <Card>
                    <CardHeader>
                        <CardTitle>Session 詳情</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                        {sessionInfo ? (
                            <>
                                <div><strong>有 Session:</strong> {sessionInfo.hasSession ? "是" : "否"}</div>
                                <div><strong>有用戶:</strong> {sessionInfo.hasUser ? "是" : "否"}</div>
                                <div><strong>用戶ID:</strong> {sessionInfo.userId}</div>
                                <div><strong>郵箱:</strong> {sessionInfo.email}</div>
                                <div><strong>錯誤:</strong> {sessionInfo.error}</div>
                            </>
                        ) : (
                            <div className="text-muted-foreground">點擊檢查按鈕獲取資訊</div>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* 控制面板 */}
            <Card>
                <CardHeader>
                    <CardTitle>測試控制</CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    <Button onClick={openNewTab} className="w-full">
                        開啟新標籤頁
                    </Button>
                    <Button onClick={openLibrary} variant="outline" className="w-full">
                        測試 /library
                    </Button>
                    <Button onClick={checkAuthState} variant="outline" className="w-full">
                        檢查 Session
                    </Button>
                    <Button onClick={() => setLogs([])} variant="ghost" className="w-full">
                        清除日誌
                    </Button>
                </CardContent>
            </Card>

            {/* 事件日誌 */}
            <Card>
                <CardHeader>
                    <CardTitle>事件日誌</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="space-y-1 max-h-60 overflow-y-auto">
                        {logs.length > 0 ? (
                            logs.map((log, index) => (
                                <div key={index} className="text-sm font-mono bg-muted/50 p-2 rounded">
                                    {log}
                                </div>
                            ))
                        ) : (
                            <div className="text-muted-foreground">暫無日誌</div>
                        )}
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardHeader>
                    <CardTitle>🔧 主要修復內容</CardTitle>
                </CardHeader>
                <CardContent className="text-sm space-y-2">
                    <ul className="list-disc list-inside space-y-1">
                        <li><strong>改善初始化邏輯</strong> - 先檢查 session 再獲取用戶</li>
                        <li><strong>超時保護</strong> - 5秒超時防止卡在載入狀態</li>
                        <li><strong>跨標籤頁同步優化</strong> - 減少不必要的狀態更新</li>
                        <li><strong>Middleware 改善</strong> - 優先檢查 session</li>
                        <li><strong>PKCE 流程</strong> - 改善認證安全性</li>
                    </ul>
                </CardContent>
            </Card>

            <Card>
                <CardHeader>
                    <CardTitle>📝 測試建議</CardTitle>
                </CardHeader>
                <CardContent className="text-sm space-y-2">
                    <p><strong>預期改善：</strong></p>
                    <ul className="list-disc list-inside space-y-1">
                        <li>新標籤頁不再卡在載入狀態</li>
                        <li>開啟新標籤頁不會讓原標籤頁登出</li>
                        <li>/library 頁面能正常訪問</li>
                        <li>跨標籤頁認證狀態自動同步</li>
                    </ul>

                    <p className="mt-4"><strong>如果問題仍存在：</strong></p>
                    <ol className="list-decimal list-inside space-y-1">
                        <li>檢查瀏覽器控制台的錯誤訊息</li>
                        <li>清除瀏覽器 localStorage 和 cookies</li>
                        <li>重新登入並測試</li>
                        <li>報告控制台中任何與 "InitAuth" 或 "Cross-tab sync" 相關的訊息</li>
                    </ol>
                </CardContent>
            </Card>
        </div>
    )
} 