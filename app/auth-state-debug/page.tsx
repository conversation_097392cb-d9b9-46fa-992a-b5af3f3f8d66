"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/auth-context"
import { createBrowserClient } from "@/lib/supabase/client"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export default function AuthStateDebugPage() {
    const { user, profile, isAuthenticated, isLoading } = useAuth()
    const [clientState, setClientState] = useState<any>(null)
    const [serverState, setServerState] = useState<any>(null)
    const [refreshCount, setRefreshCount] = useState(0)

    const checkStates = async () => {
        if (typeof window === 'undefined') return

        // 檢查客戶端狀態
        const supabase = createBrowserClient()
        const { data: { user: clientUser }, error: clientError } = await supabase.auth.getUser()
        const { data: { session: clientSession }, error: sessionError } = await supabase.auth.getSession()

        // 檢查 localStorage
        const authKeys = Object.keys(localStorage).filter(key =>
            key.includes('sb-auth-token')
        )
        const localStorageAuth = authKeys.reduce((acc, key) => {
            const value = localStorage.getItem(key)
            return {
                ...acc,
                [key]: value ? {
                    length: value.length,
                    parsed: (() => {
                        try {
                            return JSON.parse(value)
                        } catch {
                            return 'Invalid JSON'
                        }
                    })()
                } : null
            }
        }, {})

        setClientState({
            contextState: {
                user: user ? { id: user.id, email: user.email } : null,
                profile: profile ? {
                    id: profile.id,
                    name: profile.name,
                    email: profile.email
                } : null,
                isAuthenticated,
                isLoading
            },
            supabaseUser: clientUser ? {
                id: clientUser.id,
                email: clientUser.email,
                lastSignInAt: clientUser.last_sign_in_at
            } : null,
            supabaseSession: clientSession ? {
                expiresAt: new Date(clientSession.expires_at! * 1000).toISOString(),
                accessTokenLength: clientSession.access_token?.length || 0,
                refreshTokenLength: clientSession.refresh_token?.length || 0
            } : null,
            localStorage: localStorageAuth,
            errors: {
                clientError: clientError?.message,
                sessionError: sessionError?.message
            }
        })

        // 檢查服務端狀態
        try {
            const response = await fetch('/api/debug/auth-status', {
                credentials: 'include',
                headers: {
                    'Cache-Control': 'no-cache'
                }
            })
            const result = await response.json()
            setServerState(result)
        } catch (error: any) {
            setServerState({ error: error.message })
        }

        setRefreshCount(prev => prev + 1)
    }

    useEffect(() => {
        checkStates()
    }, [user, profile, isAuthenticated, isLoading])

    const testNavigations = async () => {
        const pages = ['/library', '/my-posts', '/submit']
        const results = []

        for (const page of pages) {
            try {
                const response = await fetch(page, {
                    method: 'HEAD',
                    credentials: 'include'
                })
                results.push({
                    page,
                    status: response.status,
                    redirected: response.redirected,
                    url: response.url
                })
            } catch (error: any) {
                results.push({
                    page,
                    error: error.message
                })
            }
        }

        console.log('Navigation test results:', results)
        alert('檢查控制台中的導航測試結果')
    }

    return (
        <div className="container mx-auto p-6 space-y-6">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">認證狀態詳細診斷</h1>
                <div className="flex gap-2">
                    <Badge>刷新次數: {refreshCount}</Badge>
                    <Button onClick={checkStates} size="sm">重新檢查</Button>
                    <Button onClick={testNavigations} size="sm" variant="outline">測試導航</Button>
                </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* 客戶端狀態 */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            客戶端狀態
                            <Badge variant={clientState?.contextState?.isAuthenticated ? "default" : "destructive"}>
                                {clientState?.contextState?.isAuthenticated ? "已認證" : "未認證"}
                            </Badge>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div>
                                <h4 className="font-semibold text-sm mb-2">Auth Context:</h4>
                                <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                                    {JSON.stringify(clientState?.contextState, null, 2)}
                                </pre>
                            </div>

                            <div>
                                <h4 className="font-semibold text-sm mb-2">Supabase User:</h4>
                                <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                                    {JSON.stringify(clientState?.supabaseUser, null, 2)}
                                </pre>
                            </div>

                            <div>
                                <h4 className="font-semibold text-sm mb-2">Supabase Session:</h4>
                                <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                                    {JSON.stringify(clientState?.supabaseSession, null, 2)}
                                </pre>
                            </div>

                            <div>
                                <h4 className="font-semibold text-sm mb-2">LocalStorage:</h4>
                                <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-40">
                                    {JSON.stringify(clientState?.localStorage, null, 2)}
                                </pre>
                            </div>

                            {clientState?.errors && (
                                <div>
                                    <h4 className="font-semibold text-sm mb-2 text-red-600">錯誤:</h4>
                                    <pre className="text-xs bg-red-50 p-2 rounded overflow-auto">
                                        {JSON.stringify(clientState.errors, null, 2)}
                                    </pre>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* 服務端狀態 */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            服務端狀態
                            <Badge variant={serverState?.success && serverState?.data?.serverSide?.user ? "default" : "destructive"}>
                                {serverState?.success && serverState?.data?.serverSide?.user ? "已認證" : "未認證"}
                            </Badge>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-96">
                            {JSON.stringify(serverState, null, 2)}
                        </pre>
                    </CardContent>
                </Card>
            </div>

            {/* 狀態比較 */}
            <Card>
                <CardHeader>
                    <CardTitle>狀態一致性檢查</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="space-y-2">
                            <div className="flex justify-between">
                                <span>Context 認證狀態:</span>
                                <Badge variant={clientState?.contextState?.isAuthenticated ? "default" : "destructive"}>
                                    {clientState?.contextState?.isAuthenticated ? "✓" : "✗"}
                                </Badge>
                            </div>
                            <div className="flex justify-between">
                                <span>Supabase User:</span>
                                <Badge variant={clientState?.supabaseUser ? "default" : "destructive"}>
                                    {clientState?.supabaseUser ? "✓" : "✗"}
                                </Badge>
                            </div>
                            <div className="flex justify-between">
                                <span>Profile 數據:</span>
                                <Badge variant={clientState?.contextState?.profile ? "default" : "destructive"}>
                                    {clientState?.contextState?.profile ? "✓" : "✗"}
                                </Badge>
                            </div>
                        </div>
                        <div className="space-y-2">
                            <div className="flex justify-between">
                                <span>服務端認證:</span>
                                <Badge variant={serverState?.success && serverState?.data?.serverSide?.user ? "default" : "destructive"}>
                                    {serverState?.success && serverState?.data?.serverSide?.user ? "✓" : "✗"}
                                </Badge>
                            </div>
                            <div className="flex justify-between">
                                <span>Cookies 數量:</span>
                                <span>{serverState?.data?.cookies?.total || 0}</span>
                            </div>
                            <div className="flex justify-between">
                                <span>認證 Cookies:</span>
                                <span>{serverState?.data?.cookies?.authRelated?.length || 0}</span>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
} 