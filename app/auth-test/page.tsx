"use client"

import { useAuth } from "@/contexts/auth-context"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { createBrowserClient } from "@/lib/supabase/client"
import { useEffect, useState } from "react"

export default function AuthTestPage() {
    const { user, profile, isAuthenticated, isLoading, signOut } = useAuth()
    const [sessionInfo, setSessionInfo] = useState<any>(null)
    const [loading, setLoading] = useState(false)
    const supabase = createBrowserClient()

    useEffect(() => {
        checkSession()
    }, [])

    const checkSession = async () => {
        setLoading(true)
        try {
            const { data: { session }, error } = await supabase.auth.getSession()
            setSessionInfo({
                hasSession: !!session,
                user: session?.user || null,
                error: error?.message || null
            })
        } catch (err) {
            console.error('Session check error:', err)
            setSessionInfo({
                hasSession: false,
                user: null,
                error: 'Failed to check session'
            })
        } finally {
            setLoading(false)
        }
    }

    const handleSignOut = async () => {
        await signOut()
        await checkSession()
    }

    if (isLoading) {
        return (
            <div className="container mx-auto py-8">
                <Card>
                    <CardContent className="p-6">
                        <p>載入中...</p>
                    </CardContent>
                </Card>
            </div>
        )
    }

    return (
        <div className="container mx-auto py-8 space-y-6">
            <h1 className="text-2xl font-bold">認證系統測試</h1>
            
            {/* AuthContext 狀態 */}
            <Card>
                <CardHeader>
                    <CardTitle>AuthContext 狀態</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                    <p><strong>已認證:</strong> {isAuthenticated ? '是' : '否'}</p>
                    <p><strong>載入中:</strong> {isLoading ? '是' : '否'}</p>
                    <p><strong>用戶 ID:</strong> {user?.id || '無'}</p>
                    <p><strong>用戶郵箱:</strong> {user?.email || '無'}</p>
                    <p><strong>用戶名稱:</strong> {profile?.name || '無'}</p>
                </CardContent>
            </Card>

            {/* Supabase Session 狀態 */}
            <Card>
                <CardHeader>
                    <CardTitle>Supabase Session 狀態</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                    <Button onClick={checkSession} disabled={loading}>
                        {loading ? '檢查中...' : '重新檢查 Session'}
                    </Button>
                    {sessionInfo && (
                        <div className="mt-4 space-y-2">
                            <p><strong>有 Session:</strong> {sessionInfo.hasSession ? '是' : '否'}</p>
                            <p><strong>Session 用戶 ID:</strong> {sessionInfo.user?.id || '無'}</p>
                            <p><strong>Session 用戶郵箱:</strong> {sessionInfo.user?.email || '無'}</p>
                            {sessionInfo.error && (
                                <p className="text-red-500"><strong>錯誤:</strong> {sessionInfo.error}</p>
                            )}
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* 操作按鈕 */}
            <Card>
                <CardHeader>
                    <CardTitle>操作</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                    {isAuthenticated ? (
                        <Button onClick={handleSignOut} variant="destructive">
                            登出
                        </Button>
                    ) : (
                        <Button onClick={() => window.location.href = '/auth/login'}>
                            前往登入
                        </Button>
                    )}
                </CardContent>
            </Card>
        </div>
    )
}
