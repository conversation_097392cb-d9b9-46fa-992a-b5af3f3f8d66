"use client"

import type React from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useAuth } from "@/contexts/auth-context"
import { Github, Loader2 } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { FcGoogle } from "react-icons/fc"
import { FaFacebook } from "react-icons/fa"
import { Separator } from "@/components/ui/separator"

export default function RegisterPage() {
  const [name, setName] = useState("")
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [passwordError, setPasswordError] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isProviderLoading, setIsProviderLoading] = useState<string | null>(null)
  const { signUp, signInWithProvider, isAuthenticated } = useAuth()
  const router = useRouter()

  // 如果已經登入，重定向到首頁
  useEffect(() => {
    if (isAuthenticated) {
      router.push("/")
    }
  }, [isAuthenticated, router])

  const validatePasswords = () => {
    if (password !== confirmPassword) {
      setPasswordError("密碼不匹配")
      return false
    }
    if (password.length < 8) {
      setPasswordError("密碼長度至少為 8 個字符")
      return false
    }
    setPasswordError("")
    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validatePasswords()) {
      return
    }

    setIsSubmitting(true)

    try {
      const { error } = await signUp(email, password, name)
      if (!error) {
        router.push("/")
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleProviderSignIn = async (provider: "google" | "facebook" | "github") => {
    setIsProviderLoading(provider)
    try {
      await signInWithProvider(provider)
    } finally {
      setIsProviderLoading(null)
    }
  }

  return (
    <div className="flex justify-center items-center min-h-[80vh]">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">註冊</CardTitle>
          <CardDescription>創建一個新帳戶加入 AI Logora 社群</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 第三方登入按鈕 */}
          <div className="grid grid-cols-3 gap-2">
            <Button
              variant="outline"
              onClick={() => handleProviderSignIn("google")}
              disabled={isProviderLoading !== null}
              className="w-full"
            >
              {isProviderLoading === "google" ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <FcGoogle className="h-5 w-5" />
              )}
              <span className="sr-only md:not-sr-only md:ml-2 md:text-sm">Google</span>
            </Button>
            <Button
              variant="outline"
              onClick={() => handleProviderSignIn("facebook")}
              disabled={isProviderLoading !== null}
              className="w-full"
            >
              {isProviderLoading === "facebook" ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <FaFacebook className="h-5 w-5 text-blue-600" />
              )}
              <span className="sr-only md:not-sr-only md:ml-2 md:text-sm">Facebook</span>
            </Button>
            <Button
              variant="outline"
              onClick={() => handleProviderSignIn("github")}
              disabled={isProviderLoading !== null}
              className="w-full"
            >
              {isProviderLoading === "github" ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Github className="h-5 w-5" />
              )}
              <span className="sr-only md:not-sr-only md:ml-2 md:text-sm">GitHub</span>
            </Button>
          </div>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <Separator className="w-full" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-card px-2 text-muted-foreground">或使用郵箱註冊</span>
            </div>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">姓名</Label>
                <Input
                  id="name"
                  placeholder="您的姓名"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">郵箱</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">密碼</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">確認密碼</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                />
                {passwordError && <p className="text-sm text-destructive">{passwordError}</p>}
              </div>
              <Button type="submit" className="w-full" disabled={isSubmitting}>
                {isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                註冊
              </Button>
            </div>
          </form>
        </CardContent>
        <CardFooter className="flex justify-center">
          <div className="text-center text-sm">
            已有帳戶？{" "}
            <Link href="/auth/login" className="text-primary hover:underline">
              登入
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
