"use client"

import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function CookieTestPage() {
    const [cookies, setCookies] = useState<any>({})
    const [serverResult, setServerResult] = useState<any>(null)

    const refreshCookies = () => {
        if (typeof document === 'undefined') return

        const allCookies = document.cookie.split(';').reduce((acc, cookie) => {
            const [name, value] = cookie.trim().split('=')
            return { ...acc, [name]: value }
        }, {})
        setCookies(allCookies)
    }

    const checkServer = async () => {
        try {
            const response = await fetch('/api/debug/auth-status', {
                credentials: 'include'
            })
            const result = await response.json()
            setServerResult(result)
        } catch (error: any) {
            setServerResult({ error: error.message })
        }
    }

    const testCookie = () => {
        // 設置測試 cookie
        document.cookie = `test-cookie=test-value-${Date.now()}; path=/; samesite=lax; max-age=3600`
        refreshCookies()
    }

    useEffect(() => {
        refreshCookies()
        checkServer()
    }, [])

    return (
        <div className="container mx-auto p-6 space-y-6">
            <h1 className="text-2xl font-bold">Cookie 測試頁面</h1>

            <div className="flex gap-2">
                <Button onClick={refreshCookies}>刷新 Cookies</Button>
                <Button onClick={checkServer}>檢查服務端</Button>
                <Button onClick={testCookie}>設置測試 Cookie</Button>
            </div>

            <Card>
                <CardHeader>
                    <CardTitle>客戶端 Cookies</CardTitle>
                </CardHeader>
                <CardContent>
                    <pre className="text-xs bg-muted p-4 rounded overflow-auto">
                        {JSON.stringify(cookies, null, 2)}
                    </pre>
                </CardContent>
            </Card>

            <Card>
                <CardHeader>
                    <CardTitle>服務端響應</CardTitle>
                </CardHeader>
                <CardContent>
                    <pre className="text-xs bg-muted p-4 rounded overflow-auto">
                        {JSON.stringify(serverResult, null, 2)}
                    </pre>
                </CardContent>
            </Card>
        </div>
    )
} 