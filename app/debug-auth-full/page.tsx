"use client"

import { useState, useEffect } from "react"
import { createBrowserClient } from "@/lib/supabase/client"
import { useAuth } from "@/contexts/auth-context"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

interface AuthDebugInfo {
    clientSide: any
    serverSide: any
    cookies: any
    localStorage: any
    timestamp: string
}

export default function DebugAuthFullPage() {
    const [debugInfo, setDebugInfo] = useState<AuthDebugInfo | null>(null)
    const [isLoading, setIsLoading] = useState(false)
    const { user, isAuthenticated, isLoading: authLoading } = useAuth()

    const collectDebugInfo = async () => {
        // 確保只在客戶端執行
        if (typeof window === 'undefined') return

        setIsLoading(true)
        try {
            const supabase = createBrowserClient()

            // 客戶端檢查
            const { data: { user: clientUser }, error: clientError } = await supabase.auth.getUser()
            const { data: { session: clientSession }, error: sessionError } = await supabase.auth.getSession()

            // 服務端檢查
            const serverResponse = await fetch('/api/debug/auth-status', {
                credentials: 'include',
                headers: {
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                }
            })
            const serverResult = await serverResponse.json()

            // Cookies 檢查
            const allCookies: Record<string, string> = document.cookie.split(';').reduce((acc, cookie) => {
                const [name, value] = cookie.trim().split('=')
                return { ...acc, [name]: value }
            }, {} as Record<string, string>)

            // LocalStorage 檢查
            const authKeys = Object.keys(localStorage).filter(key =>
                key.includes('sb-') || key.includes('auth') || key.includes('supabase')
            )
            const localStorageAuth = authKeys.reduce((acc, key) => {
                const value = localStorage.getItem(key)
                return {
                    ...acc,
                    [key]: value ? {
                        length: value.length,
                        preview: value.slice(0, 100) + (value.length > 100 ? '...' : ''),
                        isJson: (() => {
                            try {
                                JSON.parse(value)
                                return true
                            } catch {
                                return false
                            }
                        })()
                    } : null
                }
            }, {})

            setDebugInfo({
                clientSide: {
                    user: clientUser ? {
                        id: clientUser.id,
                        email: clientUser.email,
                        lastSignInAt: clientUser.last_sign_in_at,
                        userMetadata: clientUser.user_metadata
                    } : null,
                    session: clientSession ? {
                        accessToken: clientSession.access_token ? {
                            length: clientSession.access_token.length,
                            expiresAt: new Date(clientSession.expires_at! * 1000).toISOString()
                        } : null,
                        refreshToken: clientSession.refresh_token ? {
                            length: clientSession.refresh_token.length
                        } : null,
                        expiresAt: clientSession.expires_at
                    } : null,
                    error: clientError || sessionError || null,
                    contextState: {
                        user: user ? { id: user.id, email: user.email } : null,
                        isAuthenticated,
                        authLoading
                    }
                },
                serverSide: serverResult,
                cookies: {
                    all: allCookies,
                    authRelated: Object.keys(allCookies).filter(key =>
                        key.includes('sb-') || key.includes('auth')
                    ).reduce((acc, key) => ({
                        ...acc,
                        [key]: allCookies[key] ? {
                            length: allCookies[key].length,
                            preview: allCookies[key].slice(0, 50) + '...'
                        } : null
                    }), {})
                },
                localStorage: localStorageAuth,
                timestamp: new Date().toISOString()
            })

        } catch (error: any) {
            console.error('Debug collection error:', error)
            setDebugInfo({
                clientSide: { error: error.message },
                serverSide: { error: 'Failed to fetch' },
                cookies: {},
                localStorage: {},
                timestamp: new Date().toISOString()
            })
        } finally {
            setIsLoading(false)
        }
    }

    useEffect(() => {
        collectDebugInfo()
    }, [])

    const refreshSession = async () => {
        if (typeof window === 'undefined') return

        const supabase = createBrowserClient()
        await supabase.auth.refreshSession()
        await collectDebugInfo()
    }

    const clearAllAuth = () => {
        // 確保只在客戶端執行
        if (typeof window === 'undefined') return

        // 清除所有認證相關的 localStorage
        Object.keys(localStorage).forEach(key => {
            if (key.includes('sb-') || key.includes('auth') || key.includes('supabase')) {
                localStorage.removeItem(key)
            }
        })

        // 清除所有認證相關的 cookies
        document.cookie.split(';').forEach(cookie => {
            const name = cookie.trim().split('=')[0]
            if (name.includes('sb-') || name.includes('auth')) {
                document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`
            }
        })

        collectDebugInfo()
    }

    return (
        <div className="container mx-auto p-6 space-y-6">
            <div className="flex items-center justify-between">
                <h1 className="text-3xl font-bold">身份驗證完整診斷</h1>
                <div className="space-x-2">
                    <Button onClick={collectDebugInfo} disabled={isLoading}>
                        {isLoading ? "載入中..." : "重新檢查"}
                    </Button>
                    <Button onClick={refreshSession} variant="outline">
                        刷新 Session
                    </Button>
                    <Button onClick={clearAllAuth} variant="destructive">
                        清除所有認證
                    </Button>
                </div>
            </div>

            {debugInfo && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* 客戶端狀態 */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                客戶端狀態
                                <Badge variant={debugInfo.clientSide.user ? "default" : "destructive"}>
                                    {debugInfo.clientSide.user ? "已認證" : "未認證"}
                                </Badge>
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <h4 className="font-semibold">Auth Context:</h4>
                                <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                                    {JSON.stringify(debugInfo.clientSide.contextState, null, 2)}
                                </pre>
                            </div>

                            <Separator />

                            <div>
                                <h4 className="font-semibold">Supabase User:</h4>
                                <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                                    {JSON.stringify(debugInfo.clientSide.user, null, 2)}
                                </pre>
                            </div>

                            <Separator />

                            <div>
                                <h4 className="font-semibold">Session:</h4>
                                <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                                    {JSON.stringify(debugInfo.clientSide.session, null, 2)}
                                </pre>
                            </div>

                            {debugInfo.clientSide.error && (
                                <>
                                    <Separator />
                                    <div>
                                        <h4 className="font-semibold text-red-600">錯誤:</h4>
                                        <pre className="text-xs bg-red-50 p-2 rounded overflow-auto">
                                            {JSON.stringify(debugInfo.clientSide.error, null, 2)}
                                        </pre>
                                    </div>
                                </>
                            )}
                        </CardContent>
                    </Card>

                    {/* 服務端狀態 */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                服務端狀態
                                <Badge variant={debugInfo.serverSide.success && debugInfo.serverSide.data?.serverSide?.user ? "default" : "destructive"}>
                                    {debugInfo.serverSide.success && debugInfo.serverSide.data?.serverSide?.user ? "已認證" : "未認證"}
                                </Badge>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-96">
                                {JSON.stringify(debugInfo.serverSide, null, 2)}
                            </pre>
                        </CardContent>
                    </Card>

                    {/* Cookies */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Cookies</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-2">
                                <div>
                                    <h4 className="font-semibold">認證相關 Cookies:</h4>
                                    <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                                        {JSON.stringify(debugInfo.cookies.authRelated, null, 2)}
                                    </pre>
                                </div>
                                <div>
                                    <h4 className="font-semibold">所有 Cookies ({Object.keys(debugInfo.cookies.all).length}):</h4>
                                    <div className="text-xs">
                                        {Object.keys(debugInfo.cookies.all).join(', ')}
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* LocalStorage */}
                    <Card>
                        <CardHeader>
                            <CardTitle>LocalStorage</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-96">
                                {JSON.stringify(debugInfo.localStorage, null, 2)}
                            </pre>
                        </CardContent>
                    </Card>
                </div>
            )}

            <Card>
                <CardHeader>
                    <CardTitle>環境資訊</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>NODE_ENV: {process.env.NODE_ENV}</div>
                        <div>時間戳記: {debugInfo?.timestamp}</div>
                        <div>User Agent: {typeof navigator !== 'undefined' ? navigator.userAgent.slice(0, 50) + '...' : 'N/A'}</div>
                        <div>協議: {typeof window !== 'undefined' ? window.location.protocol : 'N/A'}</div>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
} 