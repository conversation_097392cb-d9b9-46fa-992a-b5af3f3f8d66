"use client"

import React, { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { createBrowserClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

export default function DebugAuthPage() {
    const { user, isLoading } = useAuth()
    const [email, setEmail] = useState('<EMAIL>')
    const [password, setPassword] = useState('')
    const [logs, setLogs] = useState<string[]>([])
    const [sessionInfo, setSessionInfo] = useState<any>(null)
    const [cookieInfo, setCookieInfo] = useState<any>(null)
    const [documentCookies, setDocumentCookies] = useState<string>('')

    const supabase = createBrowserClient()

    const log = (message: string) => {
        const timestamp = new Date().toLocaleTimeString()
        setLogs(prev => [...prev, `[${timestamp}] ${message}`])
    }

    const checkCookies = async () => {
        try {
            // 檢查 document.cookie
            setDocumentCookies(document.cookie)

            // 檢查 API 中的 cookies
            const response = await fetch('/api/debug/cookies')
            const data = await response.json()
            setCookieInfo(data)

            log(`Document cookies: ${document.cookie}`)
            log(`API cookies count: ${data.totalCookies}`)
            log(`Auth cookies count: ${data.authCookies.length}`)
        } catch (error) {
            log(`檢查 cookies 錯誤: ${error}`)
        }
    }

    const testLogin = async () => {
        try {
            log('開始登入測試...')

            const { data, error } = await supabase.auth.signInWithPassword({
                email,
                password,
            })

            if (error) {
                log(`登入錯誤: ${error.message}`)
                return
            }

            log('登入成功!')
            log(`用戶 ID: ${data.user?.id}`)

            // 檢查是否有 session
            if (data.session) {
                log(`Session 存在: ${data.session.access_token.slice(0, 20)}...`)
                log(`Session 過期時間: ${new Date(data.session.expires_at! * 1000).toLocaleString()}`)
            } else {
                log('警告: 登入成功但沒有 session')
            }

            // 立即檢查瀏覽器 localStorage
            if (typeof window !== 'undefined') {
                const localStorageKeys = Object.keys(localStorage)
                log(`LocalStorage keys: ${localStorageKeys.join(', ')}`)

                const authKey = localStorageKeys.find(key => key.includes('supabase') || key.includes('sb-auth'))
                if (authKey) {
                    const authData = localStorage.getItem(authKey)
                    log(`Found auth in localStorage: ${authKey} = ${authData?.slice(0, 50)}...`)
                } else {
                    log('警告: LocalStorage 中沒有找到認證數據')
                }
            }

            // 登入成功後立即檢查 cookies
            setTimeout(checkCookies, 1000)

            // 檢查 AuthContext 狀態
            setTimeout(() => {
                log(`AuthContext user: ${user ? user.email : 'null'}`)
                log(`AuthContext isLoading: ${isLoading}`)
            }, 2000)

        } catch (error) {
            log(`登入異常: ${error}`)
        }
    }

    const testLogout = async () => {
        try {
            log('開始登出...')
            const { error } = await supabase.auth.signOut()

            if (error) {
                log(`登出錯誤: ${error.message}`)
                return
            }

            log('登出成功!')
            setTimeout(checkCookies, 1000)

        } catch (error) {
            log(`登出異常: ${error}`)
        }
    }

    const checkSession = async () => {
        try {
            log('檢查 Session...')
            const { data: { session }, error } = await supabase.auth.getSession()

            if (error) {
                log(`Session 錯誤: ${error.message}`)
                return
            }

            setSessionInfo(session)
            log(`Session 狀態: ${session ? '有效' : '無效'}`)
            if (session) {
                log(`用戶: ${session.user.email}`)
                log(`過期時間: ${new Date(session.expires_at! * 1000).toLocaleString()}`)
            }

        } catch (error) {
            log(`檢查 Session 異常: ${error}`)
        }
    }

    const clearLogs = () => {
        setLogs([])
    }

    // 頁面載入時檢查 cookies
    useEffect(() => {
        checkCookies()
        checkSession()
    }, [])

    return (
        <div className="container mx-auto p-4">
            <Card className="border-border/40 hover:shadow-md transition-all duration-200 relative overflow-hidden flex flex-col">
                <CardHeader>
                    <CardTitle className="text-2xl">認證系統調試</CardTitle>
                </CardHeader>
                <CardContent className="flex flex-col gap-2 flex-grow space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-4">
                            <div>
                                <Label htmlFor="email">電子郵件</Label>
                                <Input
                                    type="email"
                                    id="email"
                                    placeholder="<EMAIL>"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                />
                            </div>

                            <div>
                                <Label htmlFor="password">密碼</Label>
                                <Input
                                    type="password"
                                    id="password"
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                />
                            </div>

                            <div className="flex gap-2">
                                <Button onClick={testLogin}>測試登入</Button>
                                <Button variant="outline" onClick={testLogout}>測試登出</Button>
                                <Button variant="secondary" onClick={checkSession}>檢查 Session</Button>
                                <Button variant="ghost" onClick={clearLogs}>清除日誌</Button>
                            </div>

                            <div className="flex gap-2">
                                <Button variant="secondary" onClick={checkCookies}>檢查 Cookies</Button>
                            </div>
                        </div>

                        <div>
                            <h3 className="font-semibold mb-2">當前 Session：</h3>
                            <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto h-32">
                                {sessionInfo ? JSON.stringify(sessionInfo, null, 2) : '無 Session'}
                            </pre>
                        </div>
                    </div>

                    <div>
                        <h3 className="font-semibold mb-2">Cookie 詳情：</h3>
                        <div className="space-y-2">
                            <div>
                                <h4 className="text-sm font-medium">Document Cookies:</h4>
                                <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-20">
                                    {documentCookies || '無 cookies'}
                                </pre>
                            </div>

                            <div>
                                <h4 className="text-sm font-medium">API Cookies:</h4>
                                <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-32">
                                    {cookieInfo ? JSON.stringify(cookieInfo, null, 2) : '載入中...'}
                                </pre>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h3 className="font-semibold mb-2">調試日誌：</h3>
                        <div className="bg-black text-green-400 p-4 rounded font-mono text-sm h-64 overflow-y-auto">
                            {logs.length === 0 ? (
                                <div className="text-gray-500">暫無日誌...</div>
                            ) : (
                                logs.map((log, index) => (
                                    <div key={index}>{log}</div>
                                ))
                            )}
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
} 