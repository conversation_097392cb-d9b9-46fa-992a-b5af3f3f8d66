@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    /* Sidebar colors */
    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-primary: 222.2 47.4% 11.2%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 210 40% 96.1%;
    --sidebar-accent-foreground: 222.2 47.4% 11.2%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 222.2 84% 4.9%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    /* Sidebar colors */
    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 210 40% 98%;
    --sidebar-primary-foreground: 222.2 47.4% 11.2%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* 添加 prose-compact 類的樣式，減少段落和標題之間的間距 */
.prose-compact p {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.prose-compact h1,
.prose-compact h2,
.prose-compact h3,
.prose-compact h4,
.prose-compact h5,
.prose-compact h6 {
  margin-top: 1em;
  margin-bottom: 0.5em;
}

.prose-compact ul,
.prose-compact ol {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.prose-compact li {
  margin-top: 0.25em;
  margin-bottom: 0.25em;
}

.prose-compact blockquote {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

/* 確保 TipTap 編輯器中的列表樣式正確顯示 */
.ProseMirror ul {
  list-style: disc !important;
  list-style-position: outside !important;
  padding-left: 1.5em !important;
  margin-left: 0 !important;
}

.ProseMirror ol {
  list-style: decimal !important;
  list-style-position: outside !important;
  padding-left: 1.5em !important;
  margin-left: 0 !important;
}

.ProseMirror li {
  margin: 0.25em 0 !important;
  padding-left: 0 !important;
}

/* 嵌套列表縮進 */
.ProseMirror ul ul,
.ProseMirror ol ol,
.ProseMirror ul ol,
.ProseMirror ol ul {
  margin-left: 0 !important;
  padding-left: 1.5em !important;
}

/* 修正 prose 類與列表的衝突 */
.prose ul,
.prose ol {
  list-style: revert !important;
  list-style-position: outside !important;
  padding-left: 1.5em !important;
}

.prose li {
  margin: 0.25em 0 !important;
}

/* 確保對話框中的卡片正確顯示 */
.dialog-preview {
  max-height: none !important;
}

.dialog-preview .rich-text-content {
  max-height: 150px;
  overflow-y: hidden;
}

/* 確保對話框有足夠的空間 */
@media (min-width: 640px) {
  .dialog-content {
    max-width: 32rem;
  }
}

/* 拖拽相關樣式 */
.dragging-item {
  cursor: grabbing !important;
}

.drag-preview {
  position: absolute;
  pointer-events: none;
  z-index: 9999;
  opacity: 0;
}

/* 拖拽目標高亮效果 */
.droppable-hover {
  background-color: rgba(var(--primary-rgb), 0.1);
  outline: 2px solid var(--primary);
}

/* 成功添加動畫 */
@keyframes success-pulse {
  0% {
    background-color: rgba(var(--success-rgb), 0.1);
  }

  50% {
    background-color: rgba(var(--success-rgb), 0.2);
  }

  100% {
    background-color: rgba(var(--success-rgb), 0.1);
  }
}

.success-drop {
  animation: success-pulse 1s ease-in-out;
}

/* 在文件末尾添加以下 CSS 規則，以確保我們可以全局控制這些元素的字體大小 */
/* 添加全局樣式來控制 grid 變體中的內容字體大小 */
.grid-content-small p,
.grid-content-small span,
.grid-content-small div {
  font-size: 0.75rem !important;
}

/* 添加網格背景樣式 */
.bg-grid-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.15'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}