"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { useState, useEffect, use } from "react"
import { useRouter } from "next/navigation"
import { LibrarySidebar } from "@/components/library-sidebar"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useAuth } from "@/contexts/auth-context"
import { useToast } from "@/components/ui/use-toast"
import { ContentFilter, FilterTags } from "@/components/content-filter"
import { useContentFilter } from "@/hooks/use-content-filter"
import {
  Pencil,
  Share2,
  Trash2,
  MoreHorizontal,
  Search,
  Loader2,
  BookmarkIcon,
  Plus,
  Filter,
  Check,
  ArrowLeft,
} from "lucide-react"
import { ContentCard } from "@/components/content-card"
import type { ViewpointSemanticType, ThreadSemanticType, ContributionType } from "@/components/content-card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"
import { CollectionEditDialog } from "@/components/collection-edit-dialog"

interface Collection {
  id: string
  name: string
  description: string
  coverImage: string
  itemCount: number
  isPublic: boolean
  createdAt: string
  updatedAt: string
}

interface CollectionItem {
  id: string
  contentType: "viewpoint" | "thread"
  semanticType?: string
  title: string
  content: string
  author: {
    id: string
    name: string
    avatar: string
  }
  timestamp: string
  topics?: string[]
  subtopics?: string[]
  tags?: string[]
  stats?: {
    likes?: number
    dislikes?: number
    comments?: number
    replies?: number
    views?: number
    bookmarks?: number
  }
  // 觀點卡特有屬性
  contribution_type?: string
  originalAuthor?: string
  originalSource?: string
  addedAt?: string
}

interface Category {
  id: string
  name: string
  description?: string
  sortOrder: number
  count: number
}

interface LibraryTag {
  id: number
  name: string
  count: number
}

export default function CollectionDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params)
  const { isAuthenticated, isLoading: authLoading } = useAuth()
  const router = useRouter()
  const { toast } = useToast()
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isAddItemDialogOpen, setIsAddItemDialogOpen] = useState(false)
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [searchItemQuery, setSearchItemQuery] = useState("")
  const [activeCollection, setActiveCollection] = useState<string | undefined>(undefined)

  // 使用模組化的過濾器 hook
  const {
    filterState,
    filterActions,
    availableTopics,
    availableSubtopics,
    isLoadingFilters,
    hasActiveFilters,
    applyFiltersToItems
  } = useContentFilter()

  // 資料狀態
  const [collection, setCollection] = useState<Collection | null>(null)
  const [collectionItems, setCollectionItems] = useState<CollectionItem[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [tags, setTags] = useState<LibraryTag[]>([])
  const [availableItems, setAvailableItems] = useState<CollectionItem[]>([])
  const [collections, setCollections] = useState<Array<{
    id: string
    name: string
    itemCount?: number
    categoryId?: string
  }>>([])

  // 載入狀態
  const [isLoading, setIsLoading] = useState(true)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isAddingItems, setIsAddingItems] = useState(false)

  // 如果未登入，重定向到登入頁面
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push("/auth/login")
    }
  }, [authLoading, isAuthenticated, router])

  // 獲取收藏牆資料
  const fetchCollectionData = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/collections/${id}`)
      const result = await response.json()

      if (result.success) {
        setCollection(result.data.collection)
        setCollectionItems(result.data.items || [])
        setActiveCollection(result.data.collection.name)
      } else {
        console.error("獲取收藏牆失敗:", result.error)
        if (response.status === 404) {
          // 收藏牆不存在，保持 collection 為 null
          setCollection(null)
        } else {
          toast({
            title: "獲取收藏牆失敗",
            description: result.error,
            variant: "destructive",
          })
        }
      }
    } catch (error) {
      console.error("獲取收藏牆時出錯:", error)
      toast({
        title: "獲取收藏牆失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 獲取側邊欄資料
  const fetchSidebarData = async () => {
    try {
      // 獲取分類
      const categoriesResponse = await fetch("/api/collections/categories")
      const categoriesResult = await categoriesResponse.json()
      if (categoriesResult.success) {
        setCategories(categoriesResult.data)
      }

      // 獲取收藏牆列表
      const collectionsResponse = await fetch("/api/collections")
      const collectionsResult = await collectionsResponse.json()
      if (collectionsResult.success) {
        setCollections(collectionsResult.data.map((col: any) => ({
          id: col.id,
          name: col.name,
          itemCount: col.itemCount || 0,
          categoryId: col.categoryId
        })))
      }

      // 獲取標籤
      const tagsResponse = await fetch("/api/library/tags")
      const tagsResult = await tagsResponse.json()
      if (tagsResult.success) {
        setTags(tagsResult.data)
      }
    } catch (error) {
      console.error("獲取側邊欄資料時出錯:", error)
    }
  }

  // 獲取可添加的項目
  const fetchAvailableItems = async () => {
    try {
      const response = await fetch("/api/library/bookmarks")
      const result = await response.json()

      if (result.success) {
        // 過濾掉已在當前收藏牆中的項目
        const currentItemIds = collectionItems.map(item => item.id)
        const available = result.data.filter((item: CollectionItem) =>
          !currentItemIds.includes(item.id)
        )
        setAvailableItems(available)
      }
    } catch (error) {
      console.error("獲取可添加項目時出錯:", error)
    }
  }

  useEffect(() => {
    if (isAuthenticated) {
      fetchCollectionData()
      fetchSidebarData()
    }
  }, [isAuthenticated, id])

  useEffect(() => {
    if (collection && isAddItemDialogOpen) {
      fetchAvailableItems()
    }
  }, [collection, isAddItemDialogOpen, collectionItems])

  // 如果收藏牆不存在，顯示錯誤
  if (!isLoading && !collection && isAuthenticated) {
    return (
      <div className="flex">
        <LibrarySidebar
          categories={categories}
          tags={tags}
          collections={collections}
          activeCategory={undefined}
          activeCollection={activeCollection}
          activeTag={undefined}
          onCategoryClick={() => { }}
          onCollectionClick={(name) => setActiveCollection(name)}
          onTagClick={() => { }}
          onQuickLinkClick={() => { }}
          onCreateCollection={() => { }}
          onCreateCategory={() => { }}
          onAddToCollection={() => { }}
        />
        <div className="flex-1 p-6">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold">收藏牆不存在</h2>
            <p className="mt-2 text-muted-foreground">您請求的收藏牆不存在或已被刪除</p>
            <Button className="mt-4" onClick={() => router.push("/library")}>
              返回收藏庫
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // 處理刪除收藏牆
  const handleDeleteCollection = async () => {
    try {
      setIsDeleting(true)
      const response = await fetch(`/api/collections/${id}`, {
        method: "DELETE",
      })
      const result = await response.json()

      if (result.success) {
        toast({
          title: "收藏牆已刪除",
          description: "收藏牆已成功刪除",
        })
        router.push("/library")
      } else {
        toast({
          title: "刪除失敗",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("刪除收藏牆時出錯:", error)
      toast({
        title: "刪除失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
      setIsDeleteDialogOpen(false)
    }
  }

  // 處理添加項目到收藏牆
  const handleAddItems = async () => {
    try {
      setIsAddingItems(true)

      const promises = selectedItems.map(itemId => {
        const item = availableItems.find(i => i.id === itemId)
        if (!item) return Promise.resolve()

        return fetch(`/api/collections/items`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            collectionId: id,
            itemId: item.id,
            itemType: item.contentType === "viewpoint" ? "card" : "thread",
          }),
        })
      })

      await Promise.all(promises)

      toast({
        title: "項目已添加",
        description: `已成功添加 ${selectedItems.length} 個項目到收藏牆`,
      })

      // 重新獲取收藏牆資料
      await fetchCollectionData()

      setIsAddItemDialogOpen(false)
      setSelectedItems([])
    } catch (error) {
      console.error("添加項目時出錯:", error)
      toast({
        title: "添加失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setIsAddingItems(false)
    }
  }

  // 處理項目選擇
  const toggleItemSelection = (id: string) => {
    setSelectedItems((prev) => (prev.includes(id) ? prev.filter((itemId) => itemId !== id) : [...prev, id]))
  }

  // 處理收藏牆分類和收藏牆點擊
  const handleCategoryClick = (category: string) => {
    console.log("Category clicked:", category)
  }

  const handleCollectionClick = (collection: string) => {
    setActiveCollection(collection)
  }

  // 過濾收藏牆項目
  const filteredItems = (() => {
    let items = collectionItems

    // 先應用模組化的過濾器
    items = applyFiltersToItems(items)

    // 根據標籤過濾
    if (activeTab !== "all") {
      items = items.filter(item => item.contentType === activeTab)
    }

    // 根據搜索查詢過濾
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      items = items.filter((item) =>
        item.title.toLowerCase().includes(query) ||
        item.content.toLowerCase().includes(query) ||
        item.topics?.some((topic) => topic.toLowerCase().includes(query)) ||
        item.subtopics?.some((subtopic) => subtopic.toLowerCase().includes(query)) ||
        item.tags?.some((tag) => tag.toLowerCase().includes(query))
      )
    }

    return items
  })()

  // 過濾可添加的項目
  const filteredAvailableItems = availableItems.filter((item) => {
    if (searchItemQuery) {
      const query = searchItemQuery.toLowerCase()
      return (
        item.title.toLowerCase().includes(query) ||
        item.content.toLowerCase().includes(query) ||
        item.topics?.some((topic) => topic.toLowerCase().includes(query)) ||
        item.tags?.some((tag) => tag.toLowerCase().includes(query))
      )
    }
    return true
  })

  // 處理分享收藏牆
  const handleShareCollection = async () => {
    console.log("分享收藏牆被點擊") // 調試訊息
    try {
      const url = `${window.location.origin}/library/collection/${id}`
      console.log("要複製的網址:", url) // 調試訊息

      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(url)
        console.log("使用 navigator.clipboard.writeText 複製成功") // 調試訊息
      } else {
        // 備用方法：使用舊的方式複製到剪貼簿
        const textArea = document.createElement("textarea")
        textArea.value = url
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand("copy")
        document.body.removeChild(textArea)
        console.log("使用備用方法複製成功") // 調試訊息
      }

      console.log("準備顯示成功 toast") // 調試訊息
      toast({
        title: "複製成功",
        description: "收藏牆網址已複製到剪貼簿",
      })
      console.log("成功 toast 已調用") // 調試訊息
    } catch (error) {
      console.error("複製網址失敗:", error)
      console.log("準備顯示失敗 toast") // 調試訊息
      toast({
        title: "複製失敗",
        description: "請手動複製網址",
        variant: "destructive",
      })
      console.log("失敗 toast 已調用") // 調試訊息
    }
  }

  // 處理分類變更
  const handleCategoryChange = async (collectionId: string, categoryId: string | null) => {
  }

  if (authLoading || isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[80vh]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="flex">
      {/* 左側收藏牆側邊欄 */}
      <LibrarySidebar
        categories={categories}
        tags={tags}
        collections={collections}
        activeCategory={undefined}
        activeCollection={activeCollection}
        activeTag={undefined}
        onCategoryClick={handleCategoryClick}
        onCollectionClick={handleCollectionClick}
        onTagClick={() => { }}
        onQuickLinkClick={() => { }}
        onCreateCollection={() => { }}
        onCreateCategory={() => { }}
        onAddToCollection={() => { }}
      />

      {/* 主要內容區域 */}
      <div className="flex-1 p-6">
        {collection && (
          <>
            <div className="flex items-center gap-2 mb-4">
              <Button variant="ghost" size="sm" onClick={() => router.push("/library")} className="flex items-center">
                <ArrowLeft className="h-4 w-4 mr-1" />
                返回收藏庫
              </Button>
            </div>

            <div className="flex justify-between items-center mb-6">
              <div>
                <h1 className="text-3xl font-bold">{collection.name}</h1>
                <p className="text-muted-foreground mt-1">{collection.description}</p>
                <p className="text-sm text-muted-foreground mt-1">
                  {collection.itemCount} 個項目
                </p>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => setIsAddItemDialogOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  添加項目
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => setIsEditDialogOpen(true)}>
                      <Pencil className="mr-2 h-4 w-4" />
                      編輯收藏牆
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleShareCollection}>
                      <Share2 className="mr-2 h-4 w-4" />
                      分享收藏牆
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => setIsDeleteDialogOpen(true)} className="text-destructive">
                      <Trash2 className="mr-2 h-4 w-4" />
                      刪除收藏牆
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            {/* 搜索和篩選 */}
            <div className="flex justify-between items-center mb-6">
              <div className="relative w-96">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="搜索收藏項目..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <ContentFilter
                filterState={filterState}
                filterActions={filterActions}
                availableTopics={availableTopics}
                availableSubtopics={availableSubtopics}
                isLoadingFilters={isLoadingFilters}
              />
            </div>

            {/* 顯示已選擇的過濾器 */}
            {(hasActiveFilters || searchQuery) && (
              <div className="mb-4">
                <FilterTags
                  filterState={filterState}
                  filterActions={filterActions}
                  availableTopics={availableTopics}
                  availableSubtopics={availableSubtopics}
                  onClearAll={() => setSearchQuery("")}
                />
                {searchQuery && (
                  <Badge variant="secondary" className="ml-2 cursor-pointer" onClick={() => setSearchQuery("")}>
                    搜索: {searchQuery} <Check className="ml-1 h-3 w-3" />
                  </Badge>
                )}
              </div>
            )}

            {/* 標籤篩選 */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
              <TabsList>
                <TabsTrigger value="all">全部 ({filteredItems.length})</TabsTrigger>
                <TabsTrigger value="viewpoint">
                  觀點卡 ({filteredItems.filter(item => item.contentType === "viewpoint").length})
                </TabsTrigger>
                <TabsTrigger value="thread">
                  討論 ({filteredItems.filter(item => item.contentType === "thread").length})
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* 收藏項目網格 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredItems.map((item) => (
                <ContentCard
                  key={item.id}
                  {...(item as any)}
                  id={item.id as any}
                  contentType={item.contentType}
                  variant="grid"
                  semanticType={item.semanticType as any}
                  contribution_type={item.contribution_type as any}
                  forceBookmarked={true}
                  onAction={(action: string, cardId: number) => {
                    const path = item.contentType === "viewpoint" ? `/card/${item.id}` : `/thread/${item.id}`
                    router.push(path)
                  }}
                />
              ))}
            </div>

            {filteredItems.length === 0 && (
              <div className="text-center py-12">
                <BookmarkIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">沒有找到項目</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchQuery ? "請嘗試不同的搜索詞彙" : "這個收藏牆還沒有任何項目"}
                </p>
              </div>
            )}

            {/* 刪除收藏牆對話框 */}
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>刪除收藏牆</DialogTitle>
                  <DialogDescription>
                    您確定要刪除收藏牆「{collection.name}」嗎？此操作無法撤銷。
                  </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                    取消
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleDeleteCollection}
                    disabled={isDeleting}
                  >
                    {isDeleting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        刪除中...
                      </>
                    ) : (
                      "刪除"
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {/* 編輯收藏牆對話框 */}
            <CollectionEditDialog
              collectionId={id}
              open={isEditDialogOpen}
              onOpenChange={setIsEditDialogOpen}
              onSaved={() => {
                // 重新獲取收藏牆資料以更新顯示
                fetchCollectionData()
              }}
              onDeleted={() => {
                // 刪除成功後跳轉到收藏庫頁面
                router.push("/library")
              }}
            />

            {/* 添加項目對話框 */}
            <Dialog open={isAddItemDialogOpen} onOpenChange={setIsAddItemDialogOpen}>
              <DialogContent className="max-w-4xl max-h-[80vh]">
                <DialogHeader>
                  <DialogTitle>添加項目到收藏牆</DialogTitle>
                  <DialogDescription>選擇您要添加到「{collection.name}」的項目</DialogDescription>
                </DialogHeader>

                <div className="space-y-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="搜索項目..."
                      value={searchItemQuery}
                      onChange={(e) => setSearchItemQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  <ScrollArea className="h-96">
                    <div className="space-y-2">
                      {filteredAvailableItems.map((item) => (
                        <div
                          key={item.id}
                          className="flex items-start space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                          onClick={() => toggleItemSelection(item.id)}
                        >
                          <Checkbox
                            checked={selectedItems.includes(item.id)}
                            onChange={() => toggleItemSelection(item.id)}
                          />
                          <div className="flex-1 min-w-0">
                            <h4 className="text-sm font-medium truncate">{item.title}</h4>
                            <p className="text-xs text-gray-500 mt-1 line-clamp-2">{item.content}</p>
                            <div className="flex items-center mt-2 space-x-2">
                              <Badge variant="secondary" className="text-xs">
                                {item.contentType === "viewpoint" ? "觀點卡" : "討論"}
                              </Badge>
                              {item.topics?.slice(0, 2).map((topic) => (
                                <Badge key={topic} variant="outline" className="text-xs">
                                  {topic}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsAddItemDialogOpen(false)}>
                    取消
                  </Button>
                  <Button
                    onClick={handleAddItems}
                    disabled={selectedItems.length === 0 || isAddingItems}
                  >
                    {isAddingItems ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        添加中...
                      </>
                    ) : (
                      <>
                        <Check className="mr-2 h-4 w-4" />
                        添加選中項目 ({selectedItems.length})
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </>
        )}
      </div>
    </div>
  )
}
