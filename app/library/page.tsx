"use client"

import { useState, useEffect } from "react"
import { LibrarySidebar } from "@/components/library-sidebar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Separator } from "@/components/ui/separator"
import { useAuth } from "@/contexts/auth-context"
import { useRouter } from "next/navigation"
import { Plus, Search, Loader2, BookmarkIcon, FolderPlus } from "lucide-react"
import { CollectionCard } from "@/components/collection-card"
import { ContentFilter, FilterTags } from "@/components/content-filter"
import { useContentFilter } from "@/hooks/use-content-filter"
import { useToast } from "@/components/ui/use-toast"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { BookmarkedItemCard } from "@/components/bookmarked-item-card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface Collection {
  id: string
  name: string
  description: string
  coverImage: string
  itemCount: number
  isPublic: boolean
  categoryId?: string
  categoryName?: string
  createdAt: string
  updatedAt: string
}

interface Category {
  id: string
  name: string
  description?: string
  sortOrder: number
  count: number
  createdAt: string
  updatedAt: string
}

interface BookmarkedItem {
  id: string // 改為 string 以支援 UUID
  type: string
  contentType: "viewpoint" | "thread"
  semanticType?: string
  title: string
  content: string
  author: {
    id: string
    name: string
    avatar: string
  }
  timestamp: string
  topics?: string[]
  subtopics?: string[]
  tags?: string[]
  stats?: {
    likes?: number
    dislikes?: number
    comments?: number
    replies?: number
    views?: number
    bookmarks?: number
  }
  collectionIds: string[] // 改為 string[] 以支援 UUID
  // 觀點卡特有屬性
  contribution_type?: string
  originalAuthor?: string
  originalSource?: string
}

interface LibraryTag {
  id: string
  name: string
  count: number
  type: "topic" | "subtopic"
  originalId: string
}

export default function LibraryPage() {
  const { isAuthenticated, isLoading, user } = useAuth()
  const router = useRouter()
  const { toast } = useToast()

  // 狀態管理
  const [searchQuery, setSearchQuery] = useState("")
  const [activeCategory, setActiveCategory] = useState<string | undefined>(undefined)
  const [activeTag, setActiveTag] = useState<string | undefined>(undefined)
  const [activeTab, setActiveTab] = useState("collections")
  const [activeCollection, setActiveCollection] = useState<string | undefined>(undefined)
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [showCreateCollectionDialog, setShowCreateCollectionDialog] = useState(false)
  const [showCreateCategoryDialog, setShowCreateCategoryDialog] = useState(false)
  const [newCollectionName, setNewCollectionName] = useState("")
  const [newCollectionDescription, setNewCollectionDescription] = useState("")
  const [newCollectionCategoryId, setNewCollectionCategoryId] = useState<string | undefined>(undefined)
  const [newCategoryName, setNewCategoryName] = useState("")
  const [newCategoryDescription, setNewCategoryDescription] = useState("")

  // 數據狀態
  const [collections, setCollections] = useState<Collection[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [bookmarkedItems, setBookmarkedItems] = useState<BookmarkedItem[]>([])
  const [tags, setTags] = useState<LibraryTag[]>([])

  // 使用新的模組化 filter hook
  const {
    filterState,
    filterActions,
    availableTopics,
    availableSubtopics,
    isLoadingFilters,
    hasActiveFilters,
    applyFiltersToItems
  } = useContentFilter()

  // 加載狀態
  const [collectionsLoading, setCollectionsLoading] = useState(true)
  const [categoriesLoading, setCategoriesLoading] = useState(true)
  const [bookmarksLoading, setBookmarksLoading] = useState(true)
  const [tagsLoading, setTagsLoading] = useState(true)
  const [creatingCollection, setCreatingCollection] = useState(false)
  const [bookmarksLoaded, setBookmarksLoaded] = useState(false)

  // 未登入自動重定向至登入頁（與 MyPostsPage 行為一致）
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.replace('/auth/login')
    }
  }, [isLoading, isAuthenticated, router])

  // 獲取標籤列表
  const fetchTags = async () => {
    try {
      setTagsLoading(true)
      const response = await fetch("/api/library/tags")
      const result = await response.json()

      if (result.success) {
        setTags(result.data)
      } else {
        console.error("獲取標籤列表失敗:", result.error)
        toast({
          title: "獲取標籤列表失敗",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("獲取標籤列表時出錯:", error)
      toast({
        title: "獲取標籤列表失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setTagsLoading(false)
    }
  }

  // 獲取分類列表
  const fetchCategories = async () => {
    try {
      setCategoriesLoading(true)
      console.log("正在獲取分類列表...")
      const response = await fetch("/api/collections/categories")
      const result = await response.json()

      console.log("分類 API 回應:", result)

      if (result.success) {
        console.log("成功獲取分類數據:", result.data)
        setCategories(result.data)
      } else {
        console.error("獲取分類列表失敗:", result.error)
        toast({
          title: "獲取分類列表失敗",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("獲取分類列表時出錯:", error)
      toast({
        title: "獲取分類列表失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setCategoriesLoading(false)
    }
  }

  // 獲取收藏牆列表
  const fetchCollections = async (categoryId?: string) => {
    try {
      setCollectionsLoading(true)
      let url = "/api/collections"
      if (categoryId) {
        url += `?categoryId=${categoryId}`
      }

      const response = await fetch(url)
      const result = await response.json()

      if (result.success) {
        setCollections(result.data)
      } else {
        console.error("獲取收藏牆列表失敗:", result.error)
        toast({
          title: "獲取收藏牆列表失敗",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("獲取收藏牆列表時出錯:", error)
      toast({
        title: "獲取收藏牆列表失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setCollectionsLoading(false)
    }
  }

  // 獲取收藏項目
  const fetchBookmarks = async () => {
    console.log("Library: 開始獲取收藏項目...")
    setBookmarksLoading(true)

    try {
      // 添加超時處理
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 15000) // 15秒超時

      const response = await fetch("/api/bookmarks?limit=100", {
        credentials: 'include',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      console.log("Library: API 響應狀態", {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      })

      if (!response.ok) {
        throw new Error(`API 響應錯誤: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()

      console.log("Library: API 響應結果", {
        success: result.success,
        dataLength: result.data?.length,
        error: result.error
      })

      if (result.success) {
        // 轉換數據格式以匹配現有的 BookmarkedItem 接口
        const formattedItems = result.data.map((bookmark: any) => {
          const content = bookmark.content
          const isCard = bookmark.item_type === "card"

          return {
            id: content.id, // 保持 UUID 格式
            type: isCard ? "viewpoint" : "thread", // 添加 type 屬性
            contentType: isCard ? "viewpoint" : "thread",
            semanticType: content.semantic_type,
            title: content.title,
            content: content.content,
            author: {
              id: content.profiles?.id || "unknown",
              name: content.profiles?.name || "未知用戶",
              avatar: content.profiles?.avatar || "/placeholder.svg",
            },
            timestamp: new Date(content.created_at || content.published_at).toLocaleDateString("zh-TW"),
            topics: content.topics || [], // 使用從 API 返回的主題信息
            subtopics: isCard ? (content.subtopics || []) : undefined,
            tags: !isCard ? (content.topics || []) : undefined, // 討論串使用 topics 作為 tags
            stats: {
              likes: 0,
              dislikes: isCard ? 0 : undefined,
              comments: isCard ? 0 : undefined,
              replies: !isCard ? 0 : undefined,
              bookmarks: 0,
              hasLiked: false,
              hasDisliked: false,
              hasBookmarked: true, // 收藏頁面中的項目都是已收藏的
            },
            collectionIds: [], // 將在後續獲取真實的收藏牆關聯
            // 觀點卡特有屬性
            ...(isCard && {
              contribution_type: content.contribution_type,
              originalAuthor: content.original_author,
              originalSource: content.original_url,
            }),
          }
        })

        console.log("Library: 數據格式化完成", { formattedItemsLength: formattedItems.length })

        // 簡化流程：直接設置基本數據，然後異步獲取額外信息
        setBookmarkedItems(formattedItems)

        // 異步獲取統計數據（不阻塞顯示）
        if (isAuthenticated && user && formattedItems.length > 0) {
          console.log(`Library: 開始異步獲取統計數據...`)

          // 不等待，讓數據先顯示
          Promise.resolve().then(async () => {
            try {
              const itemsForStats = formattedItems.map((item: BookmarkedItem) => ({
                id: item.id,
                type: item.contentType === "viewpoint" ? "card" : "thread"
              }))

              const { getBatchStats } = await import("@/lib/batch-stats-service")
              const statsResult = await getBatchStats(itemsForStats, user.id)

              const itemsWithStats = formattedItems.map((item: BookmarkedItem) => ({
                ...item,
                stats: {
                  ...item.stats,
                  likes: statsResult.stats[item.id]?.likes || 0,
                  dislikes: statsResult.stats[item.id]?.dislikes || 0,
                  comments: statsResult.stats[item.id]?.comments || 0,
                  replies: statsResult.stats[item.id]?.replies || 0,
                  bookmarks: statsResult.stats[item.id]?.bookmarks || 0,
                  views: statsResult.stats[item.id]?.views || 0,
                  hasLiked: statsResult.userStates[item.id]?.liked || false,
                  hasDisliked: statsResult.userStates[item.id]?.disliked || false,
                  hasBookmarked: true,
                }
              }))

              console.log("Library: 統計數據更新完成")
              setBookmarkedItems(itemsWithStats)
            } catch (statsError) {
              console.warn("Library: 統計數據獲取失敗，使用基本數據", statsError)
            }
          })
        }

        // 異步獲取收藏關聯（不阻塞顯示）
        Promise.resolve().then(async () => {
          try {
            if (!isAuthenticated || !user) return

            const { getBatchCollectionAssociations } = await import("@/lib/batch-collection-service")

            // 轉換為批量服務需要的格式
            const batchItems = formattedItems.map((item: BookmarkedItem) => ({
              id: item.id,
              type: item.contentType === "viewpoint" ? "card" as const : "thread" as const
            }))

            const collectionResult = await getBatchCollectionAssociations(batchItems, user.id)

            console.log("Library: 收藏關聯更新完成")
            setBookmarkedItems(prev => {
              // 更新收藏關聯信息
              return prev.map(item => ({
                ...item,
                collectionIds: collectionResult.itemCollections[item.id] || []
              }))
            })
          } catch (collectionError) {
            console.warn("Library: 收藏關聯獲取失敗", collectionError)
          }
        })

      } else {
        console.error("Library: 獲取收藏項目失敗:", result.error)

        // 根據錯誤類型提供不同的用戶反饋
        let errorMessage = "獲取收藏項目失敗"
        if (result.error?.includes("登入")) {
          errorMessage = "請重新登入後再試"
        } else if (result.error?.includes("認證")) {
          errorMessage = "認證狀態異常，請重新登入"
        }

        toast({
          title: errorMessage,
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Library: 獲取收藏項目時出錯:", error)

      // 提供更詳細的錯誤信息
      let errorMessage = "網路連線問題，請檢查網路狀態"
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = "請求超時，請稍後再試"
        } else if (error.message.includes('fetch')) {
          errorMessage = "無法連接伺服器，請稍後再試"
        }
      }

      toast({
        title: "獲取收藏項目失敗",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setBookmarksLoading(false)
      setBookmarksLoaded(true)
    }
  }

  // 初始化數據
  useEffect(() => {
    if (isAuthenticated) {
      fetchCategories()
      fetchCollections()
      fetchBookmarks()
      fetchTags()
    }
  }, [isAuthenticated])

  // 當活動分類變更時，重新獲取收藏牆
  useEffect(() => {
    if (isAuthenticated) {
      fetchCollections(activeCategory)
    }
  }, [activeCategory, isAuthenticated])

  // 當使用者切換到 "bookmarks" 分頁時，若尚未載入，則觸發載入
  useEffect(() => {
    if (isAuthenticated && activeTab === "bookmarks" && !bookmarksLoaded) {
      fetchBookmarks()
    }
  }, [activeTab, isAuthenticated, bookmarksLoaded])

  // 過濾收藏項目
  const filteredItems = (() => {
    let items = bookmarkedItems

    // 先應用模組化的過濾器
    items = applyFiltersToItems(items)

    // 然後應用標籤過濾
    if (activeTag) {
      items = items.filter((item) => {
        const selectedTag = tags.find(tag => tag.name === activeTag)
        if (selectedTag) {
          const hasTopicMatch = item.topics?.includes(selectedTag.name)
          const hasSubtopicMatch = item.subtopics?.includes(selectedTag.name)
          const hasTagMatch = item.tags?.includes(selectedTag.name)
          return hasTopicMatch || hasSubtopicMatch || hasTagMatch
        } else {
          return item.topics?.includes(activeTag) || item.subtopics?.includes(activeTag) || item.tags?.includes(activeTag)
        }
      })
    }

    // 最後應用搜索過濾
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      items = items.filter((item) =>
        item.title.toLowerCase().includes(query) ||
        item.content.toLowerCase().includes(query) ||
        item.topics?.some((topic) => topic.toLowerCase().includes(query)) ||
        item.subtopics?.some((subtopic) => subtopic.toLowerCase().includes(query)) ||
        item.tags?.some((tag) => tag.toLowerCase().includes(query))
      )
    }

    return items
  })()

  // 過濾收藏牆 (現在根據真實分類過濾)
  const filteredCollections = collections.filter((collection) => {
    // 根據搜索查詢過濾
    if (searchQuery && !collection.name.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false
    }
    return true
  })

  // 如果正在檢查認證狀態，顯示加載畫面
  if (isLoading) {
    console.log("Library page: Still loading auth state")
    return (
      <div className="flex justify-center items-center min-h-[80vh]">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto" />
          <p className="text-muted-foreground">正在檢查認證狀態...</p>
        </div>
      </div>
    )
  }

  // 如果未認證，middleware 會自動重定向，這裡只是防禦性編程
  if (!isAuthenticated) {
    console.log("Library page: User not authenticated, should redirect")
    return (
      <div className="flex justify-center items-center min-h-[80vh]">
        <div className="text-center space-y-4">
          <p className="text-muted-foreground">正在重定向到登入頁面...</p>
        </div>
      </div>
    )
  }

  console.log("Library page: User authenticated, rendering page", {
    userId: user?.id?.slice(0, 8),
    email: user?.email
  })

  // 處理拖放到側邊欄收藏牆
  const handleAddToSidebarCollection = async (itemId: string, itemType: string, collectionId: string) => {
    try {
      // 將前端的 itemType 轉換為 API 期望的格式
      const apiItemType = itemType === "viewpoint" ? "card" : "thread"

      console.log("handleAddToSidebarCollection called with:", { itemId, itemType, apiItemType, collectionId })

      // 調用 API 添加項目到收藏牆
      const response = await fetch("/api/collections/items", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          collectionId: collectionId,
          itemId: itemId, // 已經是 string 格式
          itemType: apiItemType, // 使用轉換後的類型
        }),
      })

      console.log("API 響應狀態:", response.status, response.statusText)

      // 檢查響應是否為 JSON
      const contentType = response.headers.get("content-type")
      if (!contentType || !contentType.includes("application/json")) {
        const text = await response.text()
        console.error("API 返回非 JSON 響應:", text.substring(0, 200))
        throw new Error("API 返回了無效的響應格式")
      }

      const result = await response.json()
      console.log("API 響應結果:", result)

      if (result.success) {
        // 清除批量收藏服務的快取
        if (user) {
          const { BatchCollectionService } = await import("@/lib/batch-collection-service")
          const service = BatchCollectionService.getInstance()
          service.clearItemCache([{ id: itemId, type: apiItemType as "card" | "thread" }], user.id)
        }

        // 更新收藏牆的項目數量
        setCollections(prevCollections =>
          prevCollections.map(collection =>
            collection.id === collectionId
              ? { ...collection, itemCount: collection.itemCount + 1 }
              : collection
          )
        )

        // 更新卡片的收藏狀態
        setBookmarkedItems(prevItems =>
          prevItems.map(item =>
            item.id === itemId
              ? { ...item, collectionIds: [...(item.collectionIds || []), collectionId] }
              : item
          )
        )

        toast({
          title: "已添加到收藏牆",
          description: result.message,
        })
      } else {
        if (result.error === "項目已經在收藏牆中") {
          toast({
            title: "項目已在收藏牆中",
            description: result.error,
          })
        } else {
          toast({
            title: "添加失敗",
            description: result.error,
            variant: "destructive",
          })
        }
      }
    } catch (error) {
      console.error("添加項目到收藏牆時出錯:", error)
      toast({
        title: "添加失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    }
  }

  // 處理收藏牆切換（從 BookmarkedItemCard 的下拉選單）
  const handleCollectionToggle = async (itemId: string, collectionId: string, isAdd: boolean) => {
    try {
      const itemType = bookmarkedItems.find(item => item.id === itemId)?.contentType === "viewpoint" ? "card" : "thread"

      if (isAdd) {
        // 添加到收藏牆
        const response = await fetch("/api/collections/items", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            collectionId: collectionId,
            itemId: itemId,
            itemType: itemType,
          }),
        })

        const result = await response.json()

        if (result.success) {
          // 清除批量收藏服務的快取
          if (user) {
            const { BatchCollectionService } = await import("@/lib/batch-collection-service")
            const service = BatchCollectionService.getInstance()
            service.clearItemCache([{ id: itemId, type: itemType as "card" | "thread" }], user.id)
          }

          // 更新本地狀態
          setBookmarkedItems(prevItems =>
            prevItems.map(item =>
              item.id === itemId
                ? { ...item, collectionIds: [...(item.collectionIds || []), collectionId] }
                : item
            )
          )

          // 更新收藏牆項目數量
          setCollections(prevCollections =>
            prevCollections.map(collection =>
              collection.id === collectionId
                ? { ...collection, itemCount: collection.itemCount + 1 }
                : collection
            )
          )

          toast({
            title: "已添加到收藏牆",
            description: result.message,
          })
        } else {
          toast({
            title: "添加失敗",
            description: result.error,
            variant: "destructive",
          })
        }
      } else {
        // 從收藏牆移除
        const response = await fetch("/api/collections/items", {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            collectionId: collectionId,
            itemId: itemId,
            itemType: itemType,
          }),
        })

        const result = await response.json()

        if (result.success) {
          // 清除批量收藏服務的快取
          if (user) {
            const { BatchCollectionService } = await import("@/lib/batch-collection-service")
            const service = BatchCollectionService.getInstance()
            service.clearItemCache([{ id: itemId, type: itemType as "card" | "thread" }], user.id)
          }

          // 更新本地狀態
          setBookmarkedItems(prevItems =>
            prevItems.map(item =>
              item.id === itemId
                ? { ...item, collectionIds: (item.collectionIds || []).filter(id => id !== collectionId) }
                : item
            )
          )

          // 更新收藏牆項目數量
          setCollections(prevCollections =>
            prevCollections.map(collection =>
              collection.id === collectionId
                ? { ...collection, itemCount: Math.max(0, collection.itemCount - 1) }
                : collection
            )
          )

          toast({
            title: "已從收藏牆移除",
            description: result.message,
          })
        } else {
          toast({
            title: "移除失敗",
            description: result.error,
            variant: "destructive",
          })
        }
      }
    } catch (error) {
      console.error("切換收藏牆狀態時出錯:", error)
      toast({
        title: "操作失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    }
  }

  // 處理移除收藏
  const handleRemoveBookmark = async (itemId: string) => {
    try {
      const item = bookmarkedItems.find(item => item.id === itemId)
      if (!item) return

      const itemType = item.contentType === "viewpoint" ? "card" : "thread"

      const response = await fetch("/api/bookmarks", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          itemId: itemId, // 已經是 string 格式
          itemType: itemType,
        }),
      })

      const result = await response.json()

      if (result.success) {
        // 從本地狀態中移除項目
        setBookmarkedItems(prevItems => prevItems.filter(item => item.id !== itemId))

        toast({
          title: "已取消收藏",
          description: "項目已從收藏中移除",
        })
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      console.error("移除收藏時出錯:", error)
      throw error // 重新拋出錯誤，讓 BookmarkedItemCard 處理
    }
  }

  // 處理收藏牆分類變更
  const handleMoveCollectionToCategory = async (collectionId: string, categoryId: string | null) => {
    try {
      const response = await fetch(`/api/collections/${collectionId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          categoryId: categoryId,
        }),
      })

      const result = await response.json()

      if (result.success) {
        // 更新本地狀態
        setCollections(prevCollections =>
          prevCollections.map(collection =>
            collection.id === collectionId
              ? { ...collection, categoryId: categoryId || undefined }
              : collection
          )
        )

        // 重新獲取分類數據以更新計數
        fetchCategories()

        toast({
          title: "已移動收藏牆",
          description: result.message,
        })
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      console.error("移動收藏牆時出錯:", error)
      toast({
        title: "移動失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
      throw error
    }
  }

  // 處理收藏牆分類變更（從 CollectionCard 的下拉選單）
  const handleCollectionCategoryChange = async (collectionId: string, categoryId: string | null) => {
    await handleMoveCollectionToCategory(collectionId, categoryId)
  }

  // 處理創建新收藏牆
  const handleCreateCollection = async () => {
    if (!newCollectionName.trim()) {
      toast({
        title: "請輸入收藏牆名稱",
        variant: "destructive",
      })
      return
    }

    try {
      setCreatingCollection(true)

      const response = await fetch("/api/collections", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: newCollectionName,
          description: newCollectionDescription,
          isPublic: !newCollectionCategoryId,
          categoryId: newCollectionCategoryId || undefined,
        }),
      })

      const result = await response.json()

      if (result.success) {
        // 添加新收藏牆到列表
        setCollections(prevCollections => [result.data, ...prevCollections])

        setShowCreateCollectionDialog(false)
        setNewCollectionName("")
        setNewCollectionDescription("")
        setNewCollectionCategoryId(undefined)

        toast({
          title: "收藏牆已創建",
          description: result.message,
        })
      } else {
        toast({
          title: "創建失敗",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("創建收藏牆時出錯:", error)
      toast({
        title: "創建失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setCreatingCollection(false)
    }
  }

  // 處理分類點擊
  const handleCategoryClick = (categoryId: string) => {
    if (categoryId === "uncategorized") {
      // 處理未分類的特殊情況
      setActiveCategory(categoryId === activeCategory ? undefined : categoryId)
    } else {
      setActiveCategory(categoryId === activeCategory ? undefined : categoryId)
    }
  }

  // 處理創建新分類
  const handleCreateCategory = async () => {
    if (!newCategoryName.trim()) {
      toast({
        title: "請輸入分類名稱",
        variant: "destructive",
      })
      return
    }

    try {
      setCategoriesLoading(true)

      // 調用 API 創建分類
      const response = await fetch("/api/collections/categories", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: newCategoryName.trim(),
          description: newCategoryDescription.trim(),
        }),
      })

      const result = await response.json()

      if (result.success) {
        // 添加新分類到列表
        setCategories(prevCategories => [result.data, ...prevCategories])

        setShowCreateCategoryDialog(false)
        setNewCategoryName("")
        setNewCategoryDescription("")

        toast({
          title: "分類已創建",
          description: result.message,
        })
      } else {
        toast({
          title: "創建失敗",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("創建分類時出錯:", error)
      toast({
        title: "創建失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setCategoriesLoading(false)
    }
  }

  return (
    <>
      <style jsx global>{`
        .dragging-collection .collection-card {
          pointer-events: none;
        }

        .dragging-collection .category-drop-zone {
          background-color: rgba(59, 130, 246, 0.1);
          border: 2px dashed rgba(59, 130, 246, 0.3);
        }

        .dragging-collection .category-drop-zone:hover {
          background-color: rgba(59, 130, 246, 0.2);
          border-color: rgba(59, 130, 246, 0.5);
        }
      `}</style>

      <div className="flex min-h-screen">
        <div className="w-64 min-w-64 border-r">
          <LibrarySidebar
            categories={categories.map(c => ({ id: c.id, name: c.name, count: c.count }))}
            tags={tags.map((t, index) => ({
              id: parseInt(t.id) || (index + 1), // Convert string id to number, fallback to index
              name: t.name,
              count: t.count
            }))}
            collections={collections.map(c => ({
              id: c.id,
              name: c.name,
              itemCount: c.itemCount,
              categoryId: c.categoryId
            }))}
            activeCategory={activeCategory}
            activeCollection={activeCollection}
            activeTag={activeTag}
            onCategoryClick={handleCategoryClick}
            onCollectionClick={(collection) =>
              setActiveCollection(collection === activeCollection ? undefined : collection)
            }
            onTagClick={(tag) => setActiveTag(tag === activeTag ? undefined : tag)}
            onQuickLinkClick={(link) => {
              if (link === "all") {
                setActiveTab("bookmarks")
                setActiveCategory(undefined)
                setActiveTag(undefined)
              } else if (link === "recent") {
                setActiveTab("bookmarks")
                // 這裡可以添加按時間排序的邏輯
              } else if (link === "starred") {
                // 這裡可以添加已標記項目的邏輯
              }
            }}
            onCreateCollection={() => setShowCreateCollectionDialog(true)}
            onCreateCategory={() => setShowCreateCategoryDialog(true)}
            onAddToCollection={handleAddToSidebarCollection}
            onMoveCollectionToCategory={handleMoveCollectionToCategory}
          />
        </div>
        <div className="flex-1 p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold">我的收藏庫</h1>
            <div className="flex gap-2">
              <Button onClick={() => setShowCreateCollectionDialog(true)}>
                <FolderPlus className="mr-2 h-4 w-4" />
                創建收藏牆
              </Button>
            </div>
          </div>

          {/* 標籤頁和搜索過濾區域 */}
          <div className="mt-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <div className="flex items-center justify-between border-b pb-2">
                <TabsList>
                  <TabsTrigger value="collections">收藏牆</TabsTrigger>
                  <TabsTrigger value="bookmarks">所有收藏</TabsTrigger>
                </TabsList>

                <div className="flex items-center gap-2">
                  <div className="relative w-64">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="搜尋收藏..."
                      className="pl-8 h-9"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>

                  <ContentFilter
                    filterState={filterState}
                    filterActions={filterActions}
                    availableTopics={availableTopics}
                    availableSubtopics={availableSubtopics}
                    isLoadingFilters={isLoadingFilters}
                  />
                </div>
              </div>

              {/* 標籤和過濾器顯示 */}
              {(activeCategory || activeTag || searchQuery || hasActiveFilters) && (
                <div className="flex flex-wrap items-center gap-2 my-4">
                  {activeCategory && (
                    <div className="bg-secondary text-secondary-foreground px-3 py-1 rounded-full text-sm flex items-center gap-1">
                      分類: {activeCategory === "uncategorized"
                        ? "未分類"
                        : categories.find(c => c.id === activeCategory)?.name || activeCategory}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 ml-1"
                        onClick={() => setActiveCategory(undefined)}
                      >
                        ✕
                      </Button>
                    </div>
                  )}
                  {activeTag && (
                    <div className="bg-secondary text-secondary-foreground px-3 py-1 rounded-full text-sm flex items-center gap-1">
                      標籤: #{activeTag}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 ml-1"
                        onClick={() => setActiveTag(undefined)}
                      >
                        ✕
                      </Button>
                    </div>
                  )}
                  {searchQuery && (
                    <div className="bg-secondary text-secondary-foreground px-3 py-1 rounded-full text-sm flex items-center gap-1">
                      搜尋: {searchQuery}
                      <Button variant="ghost" size="icon" className="h-4 w-4 ml-1" onClick={() => setSearchQuery("")}>
                        ✕
                      </Button>
                    </div>
                  )}

                  {/* 顯示過濾器標籤 */}
                  <FilterTags
                    filterState={filterState}
                    filterActions={filterActions}
                    availableTopics={availableTopics}
                    availableSubtopics={availableSubtopics}
                    showClearAll={false}
                  />

                  {(activeCategory || activeTag || searchQuery || hasActiveFilters) && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-xs"
                      onClick={() => {
                        setActiveCategory(undefined)
                        setActiveTag(undefined)
                        setSearchQuery("")
                        filterActions.clearFilters()
                      }}
                    >
                      清除全部
                    </Button>
                  )}
                </div>
              )}

              <TabsContent value="collections">
                {collectionsLoading ? (
                  <div className="flex justify-center items-center py-12">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : filteredCollections.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
                    {filteredCollections.map((collection) => {
                      // 找到收藏牆對應的分類名稱
                      const categoryName = collection.categoryId
                        ? categories.find(c => c.id === collection.categoryId)?.name || "未分類"
                        : "未分類"

                      return (
                        <CollectionCard
                          key={collection.id}
                          {...collection}
                          category={categoryName}
                          categories={categories.map(c => ({ id: c.id, name: c.name }))}
                          onCategoryChange={handleCollectionCategoryChange}
                          onCollectionUpdated={() => {
                            // 重新獲取收藏牆和分類資料
                            fetchCollections(activeCategory)
                            fetchCategories()
                          }}
                        />
                      )
                    })}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="mx-auto h-12 w-12 text-muted-foreground">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                        />
                      </svg>
                    </div>
                    <h3 className="mt-4 text-lg font-medium">尚無收藏牆</h3>
                    <p className="mt-2 text-sm text-muted-foreground">創建您的第一個收藏牆來整理您的收藏。</p>
                    <Button className="mt-4" onClick={() => setShowCreateCollectionDialog(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      創建收藏牆
                    </Button>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="bookmarks">
                {bookmarksLoading ? (
                  <div className="flex justify-center items-center py-12">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : filteredItems.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
                    {filteredItems.map((item) => (
                      <BookmarkedItemCard
                        key={item.id}
                        {...item}
                        collections={collections.map(c => ({ id: c.id, name: c.name, itemCount: c.itemCount }))}
                        onCollectionToggle={handleCollectionToggle}
                        onRemoveBookmark={handleRemoveBookmark}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <BookmarkIcon className="h-12 w-12 mx-auto text-muted-foreground opacity-50" />
                    <h2 className="mt-4 text-xl font-medium">暫無收藏項目</h2>
                    <p className="mt-2 text-muted-foreground">
                      {searchQuery ? "沒有符合搜尋條件的收藏項目" : "您尚未收藏任何項目"}
                    </p>
                    {!searchQuery && (
                      <Button className="mt-4" asChild>
                        <a href="/">
                          <Plus className="mr-2 h-4 w-4" />
                          瀏覽內容
                        </a>
                      </Button>
                    )}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </div>

          {/* 創建收藏牆對話框 */}
          <Dialog open={showCreateCollectionDialog} onOpenChange={setShowCreateCollectionDialog}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>創建新收藏牆</DialogTitle>
                <DialogDescription>建立一個新的收藏牆來整理您的收藏項目。</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    名稱
                  </Label>
                  <Input
                    id="name"
                    value={newCollectionName}
                    onChange={(e) => setNewCollectionName(e.target.value)}
                    className="col-span-3"
                    disabled={creatingCollection}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="description" className="text-right">
                    描述
                  </Label>
                  <Input
                    id="description"
                    value={newCollectionDescription}
                    onChange={(e) => setNewCollectionDescription(e.target.value)}
                    className="col-span-3"
                    disabled={creatingCollection}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="category" className="text-right">
                    分類
                  </Label>
                  <Select
                    value={newCollectionCategoryId}
                    onValueChange={(value) => setNewCollectionCategoryId(value as string | undefined)}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="選擇分類" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="null">未分類</SelectItem>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <DialogFooter>
                <Button
                  type="submit"
                  onClick={handleCreateCollection}
                  disabled={creatingCollection}
                >
                  {creatingCollection && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  創建
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* 創建分類對話框 */}
          <Dialog open={showCreateCategoryDialog} onOpenChange={setShowCreateCategoryDialog}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>創建新分類</DialogTitle>
                <DialogDescription>建立一個新的收藏牆分類來整理您的收藏牆。</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="categoryName" className="text-right">
                    名稱
                  </Label>
                  <Input
                    id="categoryName"
                    value={newCategoryName}
                    onChange={(e) => setNewCategoryName(e.target.value)}
                    className="col-span-3"
                    disabled={categoriesLoading}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="categoryDescription" className="text-right">
                    描述
                  </Label>
                  <Input
                    id="categoryDescription"
                    value={newCategoryDescription}
                    onChange={(e) => setNewCategoryDescription(e.target.value)}
                    className="col-span-3"
                    disabled={categoriesLoading}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  type="submit"
                  onClick={handleCreateCategory}
                  disabled={categoriesLoading}
                >
                  {categoriesLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  創建
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </>
  )
}
