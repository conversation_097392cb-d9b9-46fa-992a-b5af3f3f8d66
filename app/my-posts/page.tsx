"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { useAuth } from "@/contexts/auth-context"
import { FileText, Loader2, Plus, Clock, CheckCircle, FileEdit, MessageSquare, AlertTriangle } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { useToast } from "@/components/ui/use-toast"
import { Separator } from "@/components/ui/separator"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { ContentCard } from "@/components/content-card"

export default function MyPostsPage() {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  // 立即檢查登入狀態並重定向
  useEffect(() => {
    // 如果已確定未登入，立即重定向
    if (!isLoading && !isAuthenticated) {
      router.replace("/auth/login") // 使用 replace 而不是 push 以避免瀏覽歷史堆疊
    }
  }, [isLoading, isAuthenticated, router])

  // 如果未登入或正在檢查登入狀態，顯示最小的加載指示器
  if (isLoading || !isAuthenticated) {
    return (
      <div className="flex justify-center items-center min-h-[80vh]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  // 以下是原始的 MyPostsPage 內容，只有在確認用戶已登入後才會渲染
  return <MyPostsContent />
}

// 將主要內容分離到單獨的組件，只有在用戶已登入時才會渲染
function MyPostsContent() {
  const { profile } = useAuth()
  const router = useRouter()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("pending")
  const [activeSubTab, setActiveSubTab] = useState("viewpoints")
  const [isLoadingData, setIsLoadingData] = useState(true)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [draftToDelete, setDraftToDelete] = useState<{ id: number; type: "viewpoint" | "thread" } | null>(null)

  // 數據狀態
  const [pendingViewpoints, setPendingViewpoints] = useState<any[]>([])
  const [draftViewpoints, setDraftViewpoints] = useState<any[]>([])
  const [publishedViewpoints, setPublishedViewpoints] = useState<any[]>([])
  const [draftDiscussions, setDraftDiscussions] = useState<any[]>([])
  const [publishedDiscussions, setPublishedDiscussions] = useState<any[]>([])
  const [stats, setStats] = useState({
    cardsCount: { draft: 0, pending: 0, published: 0 },
    threadsCount: { draft: 0, published: 0 },
    interactionStats: { totalLikes: 0, totalComments: 0, totalBookmarks: 0, totalViews: 0 },
  })

  // Read query parameters for tab selection
  useEffect(() => {
    const params = new URLSearchParams(window.location.search)
    const tabParam = params.get("tab")
    const subTabParam = params.get("subTab")

    if (tabParam && ["pending", "drafts", "published"].includes(tabParam)) {
      setActiveTab(tabParam)
    }

    if (subTabParam && ["viewpoints", "threads"].includes(subTabParam)) {
      setActiveSubTab(subTabParam)
    }
  }, [])

  // 載入用戶內容
  useEffect(() => {
    loadUserContent()
  }, [])

  const loadUserContent = async () => {
    setIsLoadingData(true)
    try {
      // 使用優化後的端點
      const response = await fetch("/api/my-posts")
      const result = await response.json()

      if (result.success && result.data) {
        const data = result.data
        setPendingViewpoints(data.pendingViewpoints || [])
        setDraftViewpoints(data.draftViewpoints || [])
        setPublishedViewpoints(data.publishedViewpoints || [])
        setDraftDiscussions(data.draftDiscussions || [])
        setPublishedDiscussions(data.publishedDiscussions || [])
        setStats(data.stats)
      } else {
        throw new Error(result.error || "獲取數據失敗")
      }

    } catch (error) {
      console.error("載入用戶內容錯誤:", error)
      toast({
        title: "載入失敗",
        description: "無法載入您的內容，請稍後再試",
        variant: "destructive",
      })
    } finally {
      setIsLoadingData(false)
    }
  }

  const openDeleteDialog = (id: number, type: "viewpoint" | "thread") => {
    setDraftToDelete({ id, type })
    setDeleteDialogOpen(true)
  }

  const confirmDelete = async () => {
    if (draftToDelete) {
      try {
        const apiType = draftToDelete.type === "viewpoint" ? "card" : "thread"
        const response = await fetch(`/api/my-content?type=${apiType}&id=${draftToDelete.id}`, {
          method: "DELETE",
        })

        const result = await response.json()

        if (result.success) {
          toast({
            title: "草稿已刪除",
            description: result.message || `${draftToDelete.type === "viewpoint" ? "觀點卡" : "討論"}草稿已成功刪除`,
          })

          // 重新載入數據
          loadUserContent()
        } else {
          toast({
            title: "刪除失敗",
            description: result.error || "刪除時發生錯誤",
            variant: "destructive",
          })
        }
      } catch (error) {
        console.error("刪除草稿錯誤:", error)
        toast({
          title: "刪除失敗",
          description: "網路連線錯誤",
          variant: "destructive",
        })
      }

      setDeleteDialogOpen(false)
      setDraftToDelete(null)
    }
  }

  // 處理繼續編輯草稿
  const handleEditDraft = (id: number, type: "viewpoint" | "thread") => {
    const contentType = type === "viewpoint" ? "original" : "thread"
    router.push(`/submit?draft=${id}&type=${contentType}`)
  }

  // 處理討論操作
  const handleDiscussionAction = (action: string, id: number) => {
    if (action === "edit") {
      router.push(`/submit?draft=${id}&type=thread`)
    } else if (action === "view") {
      router.push(`/thread/${id}`)
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">我的發表</h1>
        <div className="flex gap-2">
          <Button onClick={() => router.push("/submit")}>
            <Plus className="mr-2 h-4 w-4" />
            投稿觀點卡
          </Button>
          <Button onClick={() => router.push("/submit?type=thread")}>
            <MessageSquare className="mr-2 h-4 w-4" />
            發起討論
          </Button>
        </div>
      </div>

      {/* 統計信息 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              觀點卡統計
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-2">
            <div className="grid grid-cols-3 gap-2 text-center">
              <div className="space-y-1">
                <div className="text-2xl font-bold">{stats.cardsCount.published}</div>
                <div className="text-xs text-muted-foreground">已發布</div>
              </div>
              <div className="space-y-1">
                <div className="text-2xl font-bold">{stats.cardsCount.pending}</div>
                <div className="text-xs text-muted-foreground">待審核</div>
              </div>
              <div className="space-y-1">
                <div className="text-2xl font-bold">{stats.cardsCount.draft}</div>
                <div className="text-xs text-muted-foreground">草稿</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <MessageSquare className="h-5 w-5 mr-2" />
              討論統計
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-2">
            <div className="grid grid-cols-2 gap-2 text-center">
              <div className="space-y-1">
                <div className="text-2xl font-bold">{stats.threadsCount.published}</div>
                <div className="text-xs text-muted-foreground">已發布</div>
              </div>
              <div className="space-y-1">
                <div className="text-2xl font-bold">{stats.threadsCount.draft}</div>
                <div className="text-xs text-muted-foreground">草稿</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <CheckCircle className="h-5 w-5 mr-2" />
              互動統計
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-2">
            <div className="grid grid-cols-3 gap-2 text-center">
              <div className="space-y-1">
                <div className="text-2xl font-bold">
                  {stats.interactionStats?.totalLikes || 0}
                </div>
                <div className="text-xs text-muted-foreground">獲得點讚</div>
              </div>
              <div className="space-y-1">
                <div className="text-2xl font-bold">
                  {stats.interactionStats?.totalComments || 0}
                </div>
                <div className="text-xs text-muted-foreground">獲得評論/回覆</div>
              </div>
              <div className="space-y-1">
                <div className="text-2xl font-bold">
                  {stats.interactionStats?.totalBookmarks || 0}
                </div>
                <div className="text-xs text-muted-foreground">被收藏</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Separator className="my-6" />

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="pending" className="flex items-center">
            <Clock className="h-4 w-4 mr-2" />
            待審核
          </TabsTrigger>
          <TabsTrigger value="drafts" className="flex items-center">
            <FileEdit className="h-4 w-4 mr-2" />
            草稿
          </TabsTrigger>
          <TabsTrigger value="published" className="flex items-center">
            <CheckCircle className="h-4 w-4 mr-2" />
            已發布
          </TabsTrigger>
        </TabsList>

        {/* 待審核內容 */}
        <TabsContent value="pending" className="mt-6">
          {isLoadingData ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : pendingViewpoints.length > 0 ? (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold">待審核觀點卡</h2>
                <p className="text-sm text-muted-foreground">共 {pendingViewpoints.length} 張觀點卡</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {pendingViewpoints.map((viewpoint) => (
                  <div key={viewpoint.id} className="h-full">
                    <ContentCard {...viewpoint} />
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <Clock className="h-12 w-12 mx-auto text-muted-foreground opacity-50" />
              <h2 className="mt-4 text-xl font-medium">暫無待審核內容</h2>
              <p className="mt-2 text-muted-foreground">您目前沒有正在審核中的觀點卡或討論</p>
              <Button className="mt-4" asChild>
                <Link href="/submit">
                  <Plus className="mr-2 h-4 w-4" />
                  投稿觀點卡
                </Link>
              </Button>
            </div>
          )}
        </TabsContent>

        {/* 草稿內容 */}
        <TabsContent value="drafts" className="mt-6">
          {isLoadingData ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : draftViewpoints.length > 0 || draftDiscussions.length > 0 ? (
            <div className="space-y-8">
              <Tabs value={activeSubTab} onValueChange={setActiveSubTab}>
                <TabsList>
                  <TabsTrigger value="viewpoints" className="flex items-center">
                    <FileText className="h-4 w-4 mr-2" />
                    觀點卡草稿
                  </TabsTrigger>
                  <TabsTrigger value="threads" className="flex items-center">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    討論草稿
                  </TabsTrigger>
                </TabsList>

                {/* 觀點卡草稿 */}
                <TabsContent value="viewpoints" className="mt-4">
                  {draftViewpoints.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {draftViewpoints.map((draft) => (
                        <div key={draft.id} className="relative group h-full">
                          <ContentCard {...draft} />
                          <div className="absolute inset-0 bg-black/5 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                            <div className="flex gap-2">
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => openDeleteDialog(draft.id, "viewpoint")}
                              >
                                刪除
                              </Button>
                              <Button
                                variant="default"
                                size="sm"
                                onClick={() => handleEditDraft(draft.id, "viewpoint")}
                              >
                                繼續編輯
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <FileEdit className="h-12 w-12 mx-auto text-muted-foreground opacity-50" />
                      <h3 className="mt-4 text-lg font-medium">暫無觀點卡草稿</h3>
                      <p className="mt-2 text-muted-foreground">您目前沒有保存的觀點卡草稿</p>
                    </div>
                  )}
                </TabsContent>

                {/* 討論草稿 */}
                <TabsContent value="threads" className="mt-4">
                  {draftDiscussions.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {draftDiscussions.map((draft) => (
                        <div key={draft.id} className="relative group h-full">
                          <ContentCard {...draft} />
                          <div className="absolute inset-0 bg-black/5 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                            <div className="flex gap-2">
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => openDeleteDialog(draft.id, "thread")}
                              >
                                刪除
                              </Button>
                              <Button
                                variant="default"
                                size="sm"
                                onClick={() => handleEditDraft(draft.id, "thread")}
                              >
                                繼續編輯
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground opacity-50" />
                      <h3 className="mt-4 text-lg font-medium">暫無討論草稿</h3>
                      <p className="mt-2 text-muted-foreground">您目前沒有保存的討論草稿</p>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </div>
          ) : (
            <div className="text-center py-12">
              <FileEdit className="h-12 w-12 mx-auto text-muted-foreground opacity-50" />
              <h2 className="mt-4 text-xl font-medium">暫無草稿</h2>
              <p className="mt-2 text-muted-foreground">您目前沒有保存的草稿</p>
              <Button className="mt-4" asChild>
                <Link href="/submit">
                  <Plus className="mr-2 h-4 w-4" />
                  投稿觀點卡
                </Link>
              </Button>
            </div>
          )}
        </TabsContent>

        {/* 已發布內容 */}
        <TabsContent value="published" className="mt-6">
          {isLoadingData ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : publishedViewpoints.length > 0 || publishedDiscussions.length > 0 ? (
            <div className="space-y-8">
              <Tabs value={activeSubTab} onValueChange={setActiveSubTab}>
                <TabsList>
                  <TabsTrigger value="viewpoints" className="flex items-center">
                    <FileText className="h-4 w-4 mr-2" />
                    已發布觀點卡
                  </TabsTrigger>
                  <TabsTrigger value="threads" className="flex items-center">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    已發布討論
                  </TabsTrigger>
                </TabsList>

                {/* 已發布觀點卡 */}
                <TabsContent value="viewpoints" className="mt-4">
                  {publishedViewpoints.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {publishedViewpoints.map((viewpoint) => (
                        <div key={viewpoint.id} className="h-full">
                          <ContentCard {...viewpoint} />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 mx-auto text-muted-foreground opacity-50" />
                      <h3 className="mt-4 text-lg font-medium">暫無已發布觀點卡</h3>
                      <p className="mt-2 text-muted-foreground">您目前沒有已發布的觀點卡</p>
                    </div>
                  )}
                </TabsContent>

                {/* 已發布討論 */}
                <TabsContent value="threads" className="mt-4">
                  {publishedDiscussions.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {publishedDiscussions.map((discussion) => (
                        <ContentCard key={discussion.id} {...discussion} onAction={handleDiscussionAction} />
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground opacity-50" />
                      <h3 className="mt-4 text-lg font-medium">暫無已發布討論</h3>
                      <p className="mt-2 text-muted-foreground">您目前沒有已發布的討論</p>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </div>
          ) : (
            <div className="text-center py-12">
              <CheckCircle className="h-12 w-12 mx-auto text-muted-foreground opacity-50" />
              <h2 className="mt-4 text-xl font-medium">暫無已發布內容</h2>
              <p className="mt-2 text-muted-foreground">您目前沒有已發布的觀點卡或討論</p>
              <Button className="mt-4" asChild>
                <Link href="/submit">
                  <Plus className="mr-2 h-4 w-4" />
                  投稿觀點卡
                </Link>
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* 刪除確認對話框 */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-destructive mr-2" />
              確認刪除
            </DialogTitle>
            <DialogDescription>
              您確定要刪除這個{draftToDelete?.type === "viewpoint" ? "觀點卡" : "討論"}草稿嗎？此操作無法撤銷。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex justify-end gap-2 sm:justify-end">
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              確認刪除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
