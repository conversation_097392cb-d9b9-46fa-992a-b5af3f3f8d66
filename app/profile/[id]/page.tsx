import { getProfile, getCurrentUserProfile } from "@/lib/profile-service"
import { ProfileCard } from "@/components/profile-card"
import type { ProfileUpdatePayload } from "@/lib/types"
import { redirect } from "next/navigation"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

async function updateUserProfile(userId: string, profile: ProfileUpdatePayload) {
  "use server"

  const cookieStore = cookies()
  const supabase = createServerClient(cookieStore)

  // 檢查當前用戶是否有權限更新此資料
  const {
    data: { session },
  } = await supabase.auth.getSession()
  if (!session || session.user.id !== userId) {
    throw new Error("Unauthorized")
  }

  const { error } = await supabase
    .from("profiles")
    .update({
      ...profile,
      updated_at: new Date().toISOString(),
    })
    .eq("id", userId)

  if (error) throw new Error(error.message)
}

export default async function ProfilePage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params
  const profile = await getProfile(id)
  const currentUserProfile = await getCurrentUserProfile()

  if (!profile) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Profile Not Found</h1>
          <p>The requested profile does not exist or you don't have permission to view it.</p>
        </div>
      </div>
    )
  }

  const isCurrentUser = currentUserProfile?.id === profile.id

  const handleUpdate = async (profileData: ProfileUpdatePayload) => {
    "use server"
    await updateUserProfile(profile.id, profileData)
    redirect(`/profile/${profile.id}`)
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-8 text-center">User Profile</h1>
      <ProfileCard
        profile={profile}
        isCurrentUser={isCurrentUser}
        onUpdate={isCurrentUser ? handleUpdate : undefined}
      />
    </div>
  )
}
