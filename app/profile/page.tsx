"use client"

import type React from "react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useAuth } from "@/contexts/auth-context"
import { Loader2, User } from "lucide-react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function ProfilePage() {
  const { user, profile, isAuthenticated, isLoading, updateProfile } = useAuth()
  const router = useRouter()
  const [name, setName] = useState("")
  const [bio, setBio] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [pageLoading, setPageLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 初始化頁面加載狀態
  useEffect(() => {
    // 設置超時，如果加載時間過長，顯示錯誤信息
    const timeout = setTimeout(() => {
      if (pageLoading) {
        setError("加載時間過長，請刷新頁面重試")
      }
    }, 10000)

    // 如果認證狀態已確定（無論是已認證還是未認證），結束頁面加載
    if (!isLoading) {
      setPageLoading(false)
    }

    return () => clearTimeout(timeout)
  }, [isLoading, pageLoading])

  // 如果未登入，重定向到登入頁面
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/auth/login")
    }
  }, [isLoading, isAuthenticated, router])

  // 初始化表單數據
  useEffect(() => {
    if (profile) {
      setName(profile.name || "")
      setBio(profile.bio || "")
    }
  }, [profile])

  // 顯示加載狀態
  if (pageLoading || isLoading) {
    return (
      <div className="flex flex-col justify-center items-center min-h-[80vh]">
        <Loader2 className="h-8 w-8 animate-spin mb-4" />
        <p className="text-muted-foreground">加載個人資料中...</p>
        {error && <p className="text-red-500 mt-4">{error}</p>}
      </div>
    )
  }

  // 如果沒有用戶資料但已通過認證，顯示錯誤信息
  if (!profile && isAuthenticated) {
    return (
      <div className="flex flex-col justify-center items-center min-h-[80vh]">
        <p className="text-red-500 mb-4">無法加載用戶資料，請刷新頁面重試</p>
        <Button onClick={() => router.refresh()}>刷新頁面</Button>
      </div>
    )
  }

  // 如果沒有通過認證，應該已經被重定向到登入頁面
  if (!isAuthenticated || !user || !profile) {
    return (
      <div className="flex flex-col justify-center items-center min-h-[80vh]">
        <p className="text-muted-foreground mb-4">請先登入以查看個人資料</p>
        <Button onClick={() => router.push("/auth/login")}>前���登入</Button>
      </div>
    )
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      await updateProfile({ name, bio })
    } catch (error) {
      console.error("更新資料時出錯:", error)
      setError("更新資料時出錯，請重試")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="flex items-center space-x-4">
        <Avatar className="h-16 w-16">
          <AvatarImage
            src={profile.avatar_url || `/placeholder.svg?height=64&width=64&query=${profile.name}`}
            alt={profile.name}
          />
          <AvatarFallback>{profile.name.substring(0, 2)}</AvatarFallback>
        </Avatar>
        <div>
          <h1 className="text-2xl font-bold">{profile.name}</h1>
          <p className="text-muted-foreground">加入於 {new Date(profile.created_at).toLocaleDateString()}</p>
        </div>
      </div>

      <Tabs defaultValue="profile">
        <TabsList>
          <TabsTrigger value="profile">個人資料</TabsTrigger>
          <TabsTrigger value="contributions">我的貢獻</TabsTrigger>
          <TabsTrigger value="saved">已收藏</TabsTrigger>
        </TabsList>
        <TabsContent value="profile" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>個人資料</CardTitle>
              <CardDescription>更新您的個人資料信息</CardDescription>
            </CardHeader>
            <form onSubmit={handleSubmit}>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">姓名</Label>
                  <Input id="name" value={name} onChange={(e) => setName(e.target.value)} required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">郵箱</Label>
                  <Input id="email" value={user.email || ""} disabled />
                  <p className="text-sm text-muted-foreground">郵箱不可更改</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bio">個人簡介</Label>
                  <Textarea
                    id="bio"
                    value={bio}
                    onChange={(e) => setBio(e.target.value)}
                    placeholder="介紹一下自己..."
                    className="min-h-[100px]"
                  />
                </div>
                {error && <p className="text-red-500">{error}</p>}
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                  保存更改
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>
        <TabsContent value="contributions" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>我的貢獻</CardTitle>
              <CardDescription>您發布的觀點卡和參與的討論</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <User className="mx-auto h-12 w-12 text-muted-foreground opacity-50" />
                <h3 className="mt-4 text-lg font-medium">尚無貢獻</h3>
                <p className="mt-2 text-sm text-muted-foreground">您還沒有發布任何觀點卡或參與討論</p>
                <Button className="mt-4" asChild>
                  <a href="/submit">發布第一張觀點卡</a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="saved" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>已收藏</CardTitle>
              <CardDescription>您收藏的觀點卡和討論</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <User className="mx-auto h-12 w-12 text-muted-foreground opacity-50" />
                <h3 className="mt-4 text-lg font-medium">尚無收藏</h3>
                <p className="mt-2 text-sm text-muted-foreground">您還沒有收藏任何觀點卡或討論</p>
                <Button className="mt-4" asChild>
                  <a href="/">瀏覽觀點卡</a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
