"use client"

import { useEffect, useState } from "react"
import { useAuth } from "@/contexts/auth-context"
import { createBrowserClient } from "@/lib/supabase/client"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function SessionDebugPage() {
    const { user, profile, isAuthenticated, isLoading } = useAuth()
    const [clientSession, setClientSession] = useState<any>(null)
    const [serverDebug, setServerDebug] = useState<any>(null)
    const [cookies, setCookies] = useState<string>("")

    useEffect(() => {
        checkClientSession()
        checkCookies()
    }, [])

    const checkClientSession = async () => {
        try {
            const supabase = createBrowserClient()
            const { data: { session }, error } = await supabase.auth.getSession()
            
            setClientSession({
                hasSession: !!session,
                user: session?.user || null,
                error: error?.message || null,
                accessToken: session?.access_token ? 'exists' : 'missing'
            })
        } catch (error: any) {
            setClientSession({
                hasSession: false,
                user: null,
                error: error.message,
                accessToken: 'error'
            })
        }
    }

    const checkServerDebug = async () => {
        try {
            const response = await fetch('/api/debug/session')
            const data = await response.json()
            setServerDebug(data)
        } catch (error: any) {
            setServerDebug({ error: error.message })
        }
    }

    const checkCookies = () => {
        if (typeof document !== 'undefined') {
            setCookies(document.cookie)
        }
    }

    const testLogin = async () => {
        try {
            const supabase = createBrowserClient()
            const { data, error } = await supabase.auth.signInWithPassword({
                email: '<EMAIL>',
                password: 'wsxqaz123'
            })
            
            if (error) {
                alert('登入失敗: ' + error.message)
            } else {
                alert('登入成功!')
                // 重新檢查狀態
                setTimeout(() => {
                    checkClientSession()
                    checkCookies()
                }, 1000)
            }
        } catch (error: any) {
            alert('登入錯誤: ' + error.message)
        }
    }

    return (
        <div className="container mx-auto py-8 space-y-6">
            <h1 className="text-2xl font-bold">Session 調試頁面</h1>
            
            {/* AuthContext 狀態 */}
            <Card>
                <CardHeader>
                    <CardTitle>AuthContext 狀態</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="space-y-2">
                        <p><strong>載入中:</strong> {isLoading ? '是' : '否'}</p>
                        <p><strong>已認證:</strong> {isAuthenticated ? '是' : '否'}</p>
                        <p><strong>用戶 ID:</strong> {user?.id || '無'}</p>
                        <p><strong>用戶郵箱:</strong> {user?.email || '無'}</p>
                        <p><strong>Profile 名稱:</strong> {profile?.name || '無'}</p>
                    </div>
                </CardContent>
            </Card>

            {/* 客戶端 Session */}
            <Card>
                <CardHeader>
                    <CardTitle>客戶端 Session</CardTitle>
                </CardHeader>
                <CardContent>
                    <Button onClick={checkClientSession} className="mb-4">
                        重新檢查客戶端 Session
                    </Button>
                    {clientSession && (
                        <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto">
                            {JSON.stringify(clientSession, null, 2)}
                        </pre>
                    )}
                </CardContent>
            </Card>

            {/* 服務端調試 */}
            <Card>
                <CardHeader>
                    <CardTitle>服務端調試</CardTitle>
                </CardHeader>
                <CardContent>
                    <Button onClick={checkServerDebug} className="mb-4">
                        檢查服務端 Session
                    </Button>
                    {serverDebug && (
                        <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto">
                            {JSON.stringify(serverDebug, null, 2)}
                        </pre>
                    )}
                </CardContent>
            </Card>

            {/* Cookies */}
            <Card>
                <CardHeader>
                    <CardTitle>Cookies</CardTitle>
                </CardHeader>
                <CardContent>
                    <Button onClick={checkCookies} className="mb-4">
                        重新檢查 Cookies
                    </Button>
                    <div className="text-sm bg-gray-100 p-4 rounded">
                        <strong>Document Cookies:</strong><br />
                        {cookies || '無 cookies'}
                    </div>
                </CardContent>
            </Card>

            {/* 測試登入 */}
            <Card>
                <CardHeader>
                    <CardTitle>測試登入</CardTitle>
                </CardHeader>
                <CardContent>
                    <Button onClick={testLogin} variant="outline">
                        測試登入 (需要實際憑證)
                    </Button>
                    <p className="text-sm text-gray-600 mt-2">
                        請在代碼中替換為實際的測試帳號和密碼
                    </p>
                </CardContent>
            </Card>
        </div>
    )
}
