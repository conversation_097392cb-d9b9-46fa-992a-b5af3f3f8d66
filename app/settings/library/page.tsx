"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useToast } from "@/components/ui/use-toast"
import { Construction, ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"

export default function LibrarySettingsPage() {
    const router = useRouter()
    const { toast } = useToast()

    useEffect(() => {
        // 顯示施工中的 toast
        toast({
            title: "功能開發中",
            description: "收藏庫設定功能正在開發中，敬請期待！",
            variant: "default",
        })
    }, [toast])

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="max-w-2xl mx-auto text-center">
                <div className="mb-6">
                    <Construction className="h-24 w-24 mx-auto text-muted-foreground mb-4" />
                    <h1 className="text-3xl font-bold mb-2">功能開發中</h1>
                    <p className="text-muted-foreground text-lg">
                        收藏庫設定功能正在開發中，敬請期待！
                    </p>
                </div>

                <div className="space-y-4">
                    <p className="text-sm text-muted-foreground">
                        我們正在努力為您打造更好的收藏庫管理體驗
                    </p>

                    <Button
                        onClick={() => router.push("/library")}
                        className="flex items-center gap-2"
                    >
                        <ArrowLeft className="h-4 w-4" />
                        返回收藏庫
                    </Button>
                </div>
            </div>
        </div>
    )
} 