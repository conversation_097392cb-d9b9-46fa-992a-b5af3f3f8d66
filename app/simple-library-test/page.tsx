"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/auth-context"
import { Loader2 } from "lucide-react"

export default function SimpleLibraryTestPage() {
    const { user, profile, isAuthenticated, isLoading } = useAuth()
    const [testState, setTestState] = useState({
        authChecked: false,
        apisCalled: false,
        errors: [] as string[]
    })

    useEffect(() => {
        console.log("SimpleLibraryTest - Auth state:", {
            user: user ? { id: user.id, email: user.email } : null,
            profile: profile ? { id: profile.id, name: profile.name } : null,
            isAuthenticated,
            isLoading
        })

        if (!isLoading) {
            setTestState(prev => ({ ...prev, authChecked: true }))

            if (isAuthenticated) {
                // 測試簡單的 API 調用
                testApiCalls()
            }
        }
    }, [user, profile, isAuthenticated, isLoading])

    const testApiCalls = async () => {
        const errors: string[] = []

        try {
            console.log("Testing API calls...")

            // 測試分類 API
            const categoriesResponse = await fetch("/api/collections/categories")
            if (!categoriesResponse.ok) {
                errors.push(`Categories API failed: ${categoriesResponse.status}`)
            } else {
                const categoriesResult = await categoriesResponse.json()
                console.log("Categories API success:", categoriesResult.success)
            }

            // 測試收藏 API
            const bookmarksResponse = await fetch("/api/bookmarks")
            if (!bookmarksResponse.ok) {
                errors.push(`Bookmarks API failed: ${bookmarksResponse.status}`)
            } else {
                const bookmarksResult = await bookmarksResponse.json()
                console.log("Bookmarks API success:", bookmarksResult.success)
            }

        } catch (error: any) {
            errors.push(`API call error: ${error.message}`)
        }

        setTestState(prev => ({
            ...prev,
            apisCalled: true,
            errors
        }))
    }

    if (isLoading) {
        return (
            <div className="flex justify-center items-center min-h-[80vh]">
                <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                    <p>檢查認證狀態中...</p>
                </div>
            </div>
        )
    }

    if (!isAuthenticated) {
        return (
            <div className="flex justify-center items-center min-h-[80vh]">
                <div className="text-center">
                    <p className="text-red-600">未認證 - 這個頁面應該被 middleware 重定向</p>
                </div>
            </div>
        )
    }

    return (
        <div className="container mx-auto p-6">
            <h1 className="text-2xl font-bold mb-6">簡化 Library 測試</h1>

            <div className="space-y-4">
                <div className="border rounded p-4">
                    <h2 className="font-semibold mb-2">認證狀態</h2>
                    <div className="text-sm space-y-1">
                        <p>✅ isLoading: {isLoading.toString()}</p>
                        <p>✅ isAuthenticated: {isAuthenticated.toString()}</p>
                        <p>✅ User: {user ? `${user.email} (${user.id.slice(0, 8)})` : "null"}</p>
                        <p>✅ Profile: {profile ? `${profile.name} (${profile.id.slice(0, 8)})` : "null"}</p>
                    </div>
                </div>

                <div className="border rounded p-4">
                    <h2 className="font-semibold mb-2">測試狀態</h2>
                    <div className="text-sm space-y-1">
                        <p>{testState.authChecked ? "✅" : "⏳"} Auth checked: {testState.authChecked.toString()}</p>
                        <p>{testState.apisCalled ? "✅" : "⏳"} APIs called: {testState.apisCalled.toString()}</p>
                        {testState.errors.length > 0 && (
                            <div className="text-red-600">
                                <p>❌ Errors:</p>
                                {testState.errors.map((error, index) => (
                                    <p key={index} className="ml-4">• {error}</p>
                                ))}
                            </div>
                        )}
                    </div>
                </div>

                <div className="border rounded p-4">
                    <h2 className="font-semibold mb-2">導航測試</h2>
                    <div className="space-x-2">
                        <a href="/library" className="text-blue-600 underline">
                            測試 /library 頁面
                        </a>
                        <a href="/my-posts" className="text-blue-600 underline">
                            測試 /my-posts 頁面
                        </a>
                        <a href="/submit" className="text-blue-600 underline">
                            測試 /submit 頁面
                        </a>
                    </div>
                </div>
            </div>
        </div>
    )
} 