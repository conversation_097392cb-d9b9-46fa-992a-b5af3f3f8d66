"use client"

import { useEffect, useState } from "react"
import { createBrowserClient } from "@/lib/supabase/client"

export default function SimpleTestPage() {
    const [status, setStatus] = useState("載入中...")
    const [sessionData, setSessionData] = useState<any>(null)
    
    useEffect(() => {
        const checkAuth = async () => {
            try {
                const supabase = createBrowserClient()
                const { data: { session }, error } = await supabase.auth.getSession()
                
                setSessionData({
                    hasSession: !!session,
                    user: session?.user || null,
                    error: error?.message || null
                })
                
                if (session) {
                    setStatus("已登入")
                } else {
                    setStatus("未登入")
                }
            } catch (err) {
                setStatus("檢查失敗: " + (err as Error).message)
            }
        }
        
        checkAuth()
    }, [])
    
    return (
        <div style={{ padding: '20px', fontFamily: 'monospace' }}>
            <h1>簡單認證測試</h1>
            <p><strong>狀態:</strong> {status}</p>
            
            {sessionData && (
                <div>
                    <h2>Session 資料:</h2>
                    <pre>{JSON.stringify(sessionData, null, 2)}</pre>
                </div>
            )}
            
            <div style={{ marginTop: '20px' }}>
                <a href="/auth/login" style={{ marginRight: '10px' }}>前往登入</a>
                <a href="/">回首頁</a>
            </div>
        </div>
    )
}
