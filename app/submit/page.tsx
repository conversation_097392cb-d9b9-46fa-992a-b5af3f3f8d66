import { Suspense } from "react"
import { SubmitTypeSelection } from "@/components/submit-type-selection"
import { SubmitForm } from "@/components/submit-form"
import { ClientSubmitPage } from "./client"

export default async function SubmitPage({
  searchParams,
}: {
  searchParams: Promise<{ topic?: string; tag?: string; type?: string }>
}) {
  const params = await searchParams

  return (
    <Suspense>
      <ClientSubmitPage searchParams={params} />
    </Suspense>
  )
}
