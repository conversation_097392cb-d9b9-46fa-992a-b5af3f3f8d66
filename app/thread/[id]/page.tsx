import type { Metadata } from "next"
import { notFound } from "next/navigation"
import { getThreadById } from "@/lib/thread-service"
import { ThreadDetailContent } from "@/components/thread-detail-content"
import { Suspense } from "react"
import { cookies } from "next/headers"
import { createServerClient } from "@/lib/supabase/server"

// 檢查字符串是否為有效的 UUID
function isValidUUID(uuid: string) {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(uuid)
}

interface ThreadPageProps {
  params: Promise<{
    id: string
  }>
}

// 動態生成元數據
export async function generateMetadata({ params }: ThreadPageProps): Promise<Metadata> {
  try {
    const { id } = await params
    const thread = await getThreadById(id)

    if (!thread) {
      return {
        title: "討論不存在",
        description: "找不到請求的討論",
      }
    }

    return {
      title: `${thread.title} | AILogora`,
      description: typeof thread.content === "string" ? thread.content.substring(0, 160) : "討論內容",
    }
  } catch (error) {
    console.error("Error generating metadata:", error)
    return {
      title: "載入中... | AILogora",
      description: "正在載入討論內容",
    }
  }
}

// 加載中組件
function ThreadLoading() {
  return (
    <div className="space-y-6">
      {/* 麵包屑導航 - 加載中狀態 */}
      <div className="flex items-center text-sm text-muted-foreground mb-4">
        <div className="h-4 w-20 bg-gray-200 rounded"></div>
        <div className="h-4 w-4 mx-2 bg-gray-200 rounded"></div>
        <div className="h-4 w-24 bg-gray-200 rounded"></div>
      </div>

      {/* 討論內容 - 加載中狀態 */}
      <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6 animate-pulse">
        <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-6"></div>
        <div className="h-32 bg-gray-200 rounded mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-6"></div>
        <div className="h-10 bg-gray-200 rounded w-1/4"></div>
      </div>

      {/* 回覆區 - 加載中狀態 */}
      <div className="pt-4">
        <div className="flex justify-between items-center border-b pb-1 mb-4">
          <div className="h-6 bg-gray-200 rounded w-24"></div>
        </div>
        <div className="h-32 bg-gray-200 rounded mb-4"></div>
      </div>
    </div>
  )
}

// 錯誤組件
function ThreadError({ error }: { error: Error }) {
  return (
    <div className="space-y-6">
      <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
        <h2 className="text-lg font-semibold text-red-800 mb-2">載入討論時出現錯誤</h2>
        <p className="text-red-700">{error.message}</p>
      </div>
      <div className="mt-4">
        <a href="/" className="text-blue-600 hover:underline">
          返回首頁
        </a>
      </div>
    </div>
  )
}

export default async function ThreadPage({ params }: ThreadPageProps) {
  try {
    const { id } = await params
    const thread = await getThreadById(id)

    if (!thread) {
      notFound()
    }

    // 檢查用戶認證狀態
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)
    const { safeGetUser } = await import("@/lib/api-utils")
    const { user, error, isAuthError } = await safeGetUser(supabase)
    const isAuthenticated = !!user
    const userId = user?.id

    // 使用 BatchStatsService 獲取統計數據和用戶狀態
    const { enrichWithBatchStats } = await import("@/lib/batch-stats-service")
    const threadsWithStats = await enrichWithBatchStats([{ id }], "thread", userId)
    const threadStats = threadsWithStats[0]?.stats || {
      likes: 0,
      dislikes: 0,
      comments: 0,
      replies: 0,
      bookmarks: 0,
      views: 0,
      hasLiked: false,
      hasDisliked: false,
      hasBookmarked: false
    }

    // 創建包含統計數據的討論串物件
    const threadWithStats = {
      ...thread,
      stats: threadStats,
      likes: threadStats.likes,
      dislikes: threadStats.dislikes,
    }

    console.log("Thread data with stats:", {
      id: threadWithStats.id,
      likes: threadWithStats.likes,
      dislikes: threadWithStats.dislikes,
      stats: threadWithStats.stats,
    })

    return (
      <Suspense fallback={<ThreadLoading />}>
        <div className="space-y-6">
          {/* Add a key to force client-side re-render when thread changes */}
          <ThreadDetailContent key={`thread-${id}`} thread={threadWithStats} initialAuthState={isAuthenticated} />
        </div>
      </Suspense>
    )
  } catch (error) {
    console.error("Error loading thread:", error)
    return <ThreadError error={error instanceof Error ? error : new Error("未知錯誤")} />
  }
}
