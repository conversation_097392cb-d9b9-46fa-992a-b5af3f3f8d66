import type { Metadata } from "next"
import { notFound } from "next/navigation"
import { getTopicBySlug, getSubtopicBySlug } from "@/lib/topic-service"
import { TopicPageContent } from "@/components/topic-page-content"

interface SubtopicPageProps {
  params: Promise<{
    topicName: string
    subtopicName: string
  }>
}

export async function generateMetadata({ params }: SubtopicPageProps): Promise<Metadata> {
  const { topicName, subtopicName } = await params
  const decodedTopicName = decodeURIComponent(topicName)
  const decodedSubtopicName = decodeURIComponent(subtopicName)

  try {
    const topicResponse = await getTopicBySlug(decodedTopicName)
    const subtopicResponse = await getSubtopicBySlug(decodedTopicName, decodedSubtopicName)

    if (!topicResponse.success || !subtopicResponse.success || !topicResponse.data || !subtopicResponse.data) {
      return {
        title: "主題不存在",
        description: "找不到請求的主題或子主題",
      }
    }

    return {
      title: `${subtopicResponse.data.name} - ${topicResponse.data.name} | AI Logora`,
      description:
        subtopicResponse.data.description ||
        `探索 ${topicResponse.data.name} 中的 ${subtopicResponse.data.name} 相關的觀點和討論`,
    }
  } catch (error) {
    console.error(`Error generating metadata for subtopic ${decodedSubtopicName}:`, error)
    return {
      title: "子主題 | AI Logora",
      description: "探索 AI 技術相關的子主題",
    }
  }
}

export default async function SubtopicPage({ params }: SubtopicPageProps) {
  const { topicName, subtopicName } = await params
  const decodedTopicName = decodeURIComponent(topicName)
  const decodedSubtopicName = decodeURIComponent(subtopicName)

  try {
    const topicResponse = await getTopicBySlug(decodedTopicName)
    const subtopicResponse = await getSubtopicBySlug(decodedTopicName, decodedSubtopicName)

    if (!topicResponse.success || !subtopicResponse.success || !topicResponse.data || !subtopicResponse.data) {
      notFound()
    }

    const topic = topicResponse.data
    const subtopic = subtopicResponse.data

    return <TopicPageContent topic={topic} initialSubtopic={subtopic.name} />
  } catch (error) {
    console.error(`Error loading subtopic page:`, error)
    notFound()
  }
}
