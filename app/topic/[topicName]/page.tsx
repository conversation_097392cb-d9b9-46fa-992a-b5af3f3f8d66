import type { Metadata } from "next"
import { notFound } from "next/navigation"
import { getTopicBySlug } from "@/lib/topic-service"
import { TopicPageContent } from "@/components/topic-page-content"

interface TopicPageProps {
  params: Promise<{
    topicName: string
  }>
}

export async function generateMetadata({ params }: TopicPageProps): Promise<Metadata> {
  const { topicName } = await params
  const decodedTopicName = decodeURIComponent(topicName)

  try {
    const topicResponse = await getTopicBySlug(decodedTopicName)

    if (!topicResponse.success || !topicResponse.data) {
      return {
        title: "主題不存在",
        description: "找不到請求的主題",
      }
    }

    return {
      title: `${topicResponse.data.name} | AI Logora`,
      description: topicResponse.data.description || `探索 ${topicResponse.data.name} 相關的觀點和討論`,
    }
  } catch (error) {
    console.error(`Error generating metadata for topic ${decodedTopicName}:`, error)
    return {
      title: "主題 | AI Logora",
      description: "探索 AI 技術相關的主題",
    }
  }
}

export default async function TopicPage({ params }: TopicPageProps) {
  const { topicName } = await params
  const decodedTopicName = decodeURIComponent(topicName)

  try {
    console.log(`Loading topic page for ${decodedTopicName}`)
    const topicResponse = await getTopicBySlug(decodedTopicName)

    console.log(`Topic response:`, topicResponse)

    if (!topicResponse.success || !topicResponse.data) {
      console.log(`Topic not found: ${decodedTopicName}`, topicResponse)
      notFound()
    }

    const topic = topicResponse.data

    return <TopicPageContent topic={topic} />
  } catch (error) {
    console.error(`Error loading topic page for ${decodedTopicName}:`, error)
    notFound()
  }
}
