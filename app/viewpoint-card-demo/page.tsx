"use client"

import { useState } from "react"
import { ContentCard } from "@/components/content-card"
import { ContentCardGrid } from "@/components/content-card-grid"
import { But<PERSON> } from "@/components/ui/button"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"

// Sample data for demonstration
const sampleCards = [
  {
    id: 1,
    semanticType: "insight" as const,
    title: "GPT-4o 與 Claude 3 Opus 的實際應用比較",
    content:
      "經過多次實測，我發現 GPT-4o 在創意寫作和多模態理解上表現更佳，而 Claude 3 Opus 在長文本分析和事實準確性方面略勝一籌。對於需要處理大量文本數據的企業應用，Claude 可能是更好的選擇；而對於需要創意內容生成的場景，GPT-4o 則更為適合。\n\n值得注意的是，GPT-4o 的響應速度明顯快於 Claude 3 Opus，這在實時應用場景中是一個重要優勢。",
    topics: ["LLM"],
    subtopics: ["GPT-4o", "Claude 3", "模型比較"],
    author: {
      id: "user1",
      name: "AI研究員",
      avatar: "/diverse-research-team.png",
    },
    sourceType: "top_author",
    timestamp: "2小時前",
    stats: {
      likes: 42,
      dislikes: 3,
      comments: 12,
      bookmarks: 18,
    },
  },
  {
    id: 2,
    semanticType: "experience" as const,
    title: "使用 LoRA 微調 Llama 3 的實戰經驗分享",
    content:
      "最近嘗試使用 LoRA 技術微調 Llama 3 模型，發現幾個關鍵優化點。首先，參數設置對性能影響顯著，尤其是 rank 和 alpha 值的選擇。其次，訓練數據的質量比數量更重要，精心準備的少量高質量數據往往能帶來更好的效果。\n\n實踐建議：\n- 從小型數據集開始，逐步擴大\n- 使用 rank=16, alpha=32 作為起點\n- 監控驗證損失避免過擬合",
    topics: ["Fine-tuning"],
    subtopics: ["LoRA", "Llama 3", "參數優化"],
    author: {
      id: "user2",
      name: "技術愛好者",
      avatar: "/mystical-forest-spirit.png",
    },
    sourceType: "community",
    timestamp: "1天前",
    stats: {
      likes: 28,
      dislikes: 2,
      comments: 8,
      bookmarks: 15,
    },
  },
  {
    id: 3,
    semanticType: "guide" as const,
    title: "使用 LangChain 構建高效 RAG 系統的完整指南",
    content:
      "本指南將帶你一步步構建一個高效的 RAG 系統。首先，我們需要選擇合適的向量數據庫，如 FAISS 或 Pinecone。然後，設計合理的文檔分塊策略，確保語義完整性。接著，實現檢索策略，包括混合檢索和重排序機制。最後，優化提示模板，確保生成的回答準確且連貫。\n\n完整代碼示例和配置參數請參考原文。",
    topics: ["RAG"],
    subtopics: ["LangChain", "向量數據庫", "檢索策略"],
    author: {
      id: "user3",
      name: "系統架構師",
      avatar: "/modern-architect-studio.png",
    },
    sourceType: "top_author",
    timestamp: "3天前",
    stats: {
      likes: 56,
      dislikes: 1,
      comments: 14,
      bookmarks: 32,
    },
  },
  {
    id: 4,
    semanticType: "trap" as const,
    title: "RAG 系統中的常見陷阱與解決方案",
    content:
      "在構建 RAG 系統時，發現幾個容易被忽視的問題：檢索結果多樣性不足、上下文窗口限制和幻覺生成。通過實施語義去重、分塊策略優化和事實一致性檢查，可以顯著提升系統質量。\n\n避坑指南：\n- 實施 MMR (Maximum Marginal Relevance) 算法提升多樣性\n- 使用重疊分塊策略保持上下文連貫\n- 添加事實來源追蹤機制減少幻覺",
    topics: ["RAG"],
    subtopics: ["檢索優化", "幻覺問題", "Debug"],
    author: {
      id: "user4",
      name: "資深工程師",
      avatar: "",
    },
    sourceType: "community",
    timestamp: "1週前",
    stats: {
      likes: 38,
      dislikes: 0,
      comments: 11,
      bookmarks: 24,
    },
  },
  {
    id: 5,
    semanticType: "debate" as const,
    title: "開源 LLM 是否已經可以替代閉源商業模型？",
    content:
      "隨著 Llama 3、Mistral 和 Gemma 等開源模��的崛起，開��� LLM 與閉源商業模型的差距正在縮小。然而，在某些特定任務上，如複雜推理和專業領域知識，商業模型仍然保持領先。\n\n支持開源的觀點：\n- 成本優勢明顯，尤其是在大規模部署場景\n- 可自由微調和定制，更好地適應特定領域需求\n- 隱私保護更有保障，數據不會離開本地環境\n\n支持商業模型的觀點：\n- 在複雜任務上表現更佳，尤其是多步驟推理\n- 更新迭代更快，能更快獲得最新技術進展\n- 提供完整的技術支持和服務保障",
    topics: ["LLM"],
    subtopics: ["開源模型", "商業模型", "技術選型"],
    author: {
      id: "user5",
      name: "技術評論員",
      avatar: "",
    },
    sourceType: "community",
    timestamp: "5天前",
    stats: {
      likes: 45,
      dislikes: 12,
      comments: 28,
      bookmarks: 19,
    },
  },
  {
    id: 6,
    semanticType: "concept" as const,
    title: "Sora 的技術原理與架構解析",
    content:
      "OpenAI 的 Sora 模型採用了擴散模型架構，但其創新之處在於時空潛在擴散模型的應用。這種方法將視頻視為四維數據（寬、高、通道、時間），使模型能夠生成連貫且高質量的視頻內容。\n\n技術要點：\n- 時空潛在擴散模型\n- 大規模預訓練數據集\n- 多尺度時間注意力機制",
    topics: ["Text-to-Video"],
    subtopics: ["Sora", "擴散模型", "視頻生成"],
    author: {
      id: "user6",
      name: "AI研究員",
      avatar: "/diverse-research-team.png",
    },
    sourceType: "curated",
    originalSource: "https://openai.com/research/video-generation-models-as-world-simulators",
    timestamp: "2週前",
    stats: {
      likes: 67,
      dislikes: 2,
      comments: 15,
      bookmarks: 41,
    },
  },
]

// 將樣本數據轉換為 ContentCardProps 格式
const contentCards = sampleCards.map((card) => ({
  contentType: "viewpoint" as const,
  ...card,
}))

export default function ViewpointCardDemo() {
  const [viewMode, setViewMode] = useState<"single" | "grid">("single")
  const [compactMode, setCompactMode] = useState(false)
  const [columns, setColumns] = useState<1 | 2 | 3>(3)

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">觀點卡設計展示</h1>
        <p className="text-muted-foreground">展示新的觀點卡設計，包含所有語意類型和互動功能</p>
      </div>

      <div className="flex flex-wrap gap-4 items-center">
        <div className="space-x-2">
          <Button variant={viewMode === "single" ? "default" : "outline"} onClick={() => setViewMode("single")}>
            單卡展示
          </Button>
          <Button variant={viewMode === "grid" ? "default" : "outline"} onClick={() => setViewMode("grid")}>
            網格展示
          </Button>
        </div>

        {viewMode === "grid" && (
          <>
            <Separator orientation="vertical" className="h-8" />
            <div className="space-x-2">
              <Button variant={columns === 1 ? "default" : "outline"} onClick={() => setColumns(1)} size="sm">
                1列
              </Button>
              <Button variant={columns === 2 ? "default" : "outline"} onClick={() => setColumns(2)} size="sm">
                2列
              </Button>
              <Button variant={columns === 3 ? "default" : "outline"} onClick={() => setColumns(3)} size="sm">
                3列
              </Button>
            </div>
          </>
        )}

        <Separator orientation="vertical" className="h-8" />
        <div className="space-x-2">
          <Button variant={!compactMode ? "default" : "outline"} onClick={() => setCompactMode(false)} size="sm">
            完整內容
          </Button>
          <Button variant={compactMode ? "default" : "outline"} onClick={() => setCompactMode(true)} size="sm">
            精簡模式
          </Button>
        </div>
      </div>

      {viewMode === "single" ? (
        <Tabs defaultValue="insight" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="insight">💡 看法</TabsTrigger>
            <TabsTrigger value="experience">🧪 實測經驗</TabsTrigger>
            <TabsTrigger value="guide">🛠️ 工具教學</TabsTrigger>
            <TabsTrigger value="trap">⚠️ 踩坑警示</TabsTrigger>
            <TabsTrigger value="debate">🤔 爭議論點</TabsTrigger>
            <TabsTrigger value="concept">📚 概念整理</TabsTrigger>
          </TabsList>

          <TabsContent value="insight" className="mt-0">
            <ContentCard {...contentCards[0]} isCompact={compactMode} />
          </TabsContent>

          <TabsContent value="experience" className="mt-0">
            <ContentCard {...contentCards[1]} isCompact={compactMode} />
          </TabsContent>

          <TabsContent value="guide" className="mt-0">
            <ContentCard {...contentCards[2]} isCompact={compactMode} />
          </TabsContent>

          <TabsContent value="trap" className="mt-0">
            <ContentCard {...contentCards[3]} isCompact={compactMode} />
          </TabsContent>

          <TabsContent value="debate" className="mt-0">
            <ContentCard {...contentCards[4]} isCompact={compactMode} />
          </TabsContent>

          <TabsContent value="concept" className="mt-0">
            <ContentCard {...contentCards[5]} isCompact={compactMode} />
          </TabsContent>
        </Tabs>
      ) : (
        <ContentCardGrid cards={contentCards} columns={columns} gap="md" isCompact={compactMode} />
      )}
    </div>
  )
}
