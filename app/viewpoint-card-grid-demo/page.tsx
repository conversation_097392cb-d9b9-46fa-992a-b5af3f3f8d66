"use client"

import { useState } from "react"
import { ViewpointCardGrid } from "@/components/viewpoint-card-grid"
import { But<PERSON> } from "@/components/ui/button"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"

// Sample data for demonstration
const sampleCards = [
  {
    id: 1,
    semanticType: "insight" as const,
    title: "GPT-4o 與 Claude 3 Opus 的實際應用比較",
    content:
      "經過多次實測，我發現 GPT-4o 在創意寫作和多模態理解上表現更佳，而 Claude 3 Opus 在長文本分析和事實準確性方面略勝一籌。",
    topics: ["LLM"],
    subtopics: ["GPT-4o", "Claude 3"],
    author: {
      id: "user1",
      name: "AI研究員",
      avatar: "/diverse-research-team.png",
    },
    sourceType: "top_author",
    timestamp: "2小時前",
    stats: {
      likes: 42,
      dislikes: 3,
      comments: 12,
      bookmarks: 18,
    },
  },
  {
    id: 2,
    semanticType: "experience" as const,
    title: "使用 LoRA 微調 Llama 3 的實戰經驗分享",
    content:
      "最近嘗試使用 LoRA 技術微調 Llama 3 模型，發現幾個關鍵優化點。首先，參數設置對性能影響顯著，尤其是 rank 和 alpha 值的選擇。",
    topics: ["Fine-tuning"],
    subtopics: ["LoRA", "Llama 3"],
    author: {
      id: "user2",
      name: "技術愛好者",
      avatar: "/mystical-forest-spirit.png",
    },
    sourceType: "community",
    timestamp: "1天前",
    stats: {
      likes: 28,
      dislikes: 2,
      comments: 8,
      bookmarks: 15,
    },
  },
  {
    id: 3,
    semanticType: "guide" as const,
    title: "使用 LangChain 構建高效 RAG 系統的完整指南",
    content: "本指南將帶你一步步構建一個高效的 RAG 系統。首先，我們需要選擇合適的向量數據庫，如 FAISS 或 Pinecone。",
    topics: ["RAG"],
    subtopics: ["LangChain", "向量數據庫"],
    author: {
      id: "user3",
      name: "系統架構師",
      avatar: "/modern-architect-studio.png",
    },
    sourceType: "top_author",
    timestamp: "3天前",
    stats: {
      likes: 56,
      dislikes: 1,
      comments: 14,
      bookmarks: 32,
    },
  },
  {
    id: 4,
    semanticType: "trap" as const,
    title: "RAG 系統中的常見陷阱與解決方案",
    content: "在構建 RAG 系統時，發現幾個容易被忽視的問題：檢索結果多樣性不足、上下文窗口限制和幻覺生成。",
    topics: ["RAG"],
    subtopics: ["檢索優化", "幻覺問題"],
    author: {
      id: "user4",
      name: "資深工程師",
      avatar: "",
    },
    sourceType: "community",
    timestamp: "1週前",
    stats: {
      likes: 38,
      dislikes: 0,
      comments: 11,
      bookmarks: 24,
    },
  },
  {
    id: 5,
    semanticType: "debate" as const,
    title: "開源 LLM 是否已經可以替代閉源商業模型？",
    content: "隨著 Llama 3、Mistral 和 Gemma 等開源模型的崛起，開源 LLM 與閉��商業模型的差距正在縮小。",
    topics: ["LLM"],
    subtopics: ["開源模型", "商業模型"],
    author: {
      id: "user5",
      name: "技術評論員",
      avatar: "",
    },
    sourceType: "community",
    timestamp: "5天前",
    stats: {
      likes: 45,
      dislikes: 12,
      comments: 28,
      bookmarks: 19,
    },
  },
  {
    id: 6,
    semanticType: "concept" as const,
    title: "Sora 的技術原理與架構解析",
    content: "OpenAI 的 Sora 模型採用了擴散模型架構，但其創新之處在於時空潛在擴散模型的應用。",
    topics: ["Text-to-Video"],
    subtopics: ["Sora", "擴散模型"],
    author: {
      id: "user6",
      name: "AI研究員",
      avatar: "/diverse-research-team.png",
    },
    sourceType: "curated",
    originalSource: "https://openai.com/research/video-generation-models-as-world-simulators",
    timestamp: "2週前",
    stats: {
      likes: 67,
      dislikes: 2,
      comments: 15,
      bookmarks: 41,
    },
  },
  {
    id: 7,
    semanticType: "insight" as const,
    title: "多模態模型在醫療影像分析中的應用前景",
    content: "多模態模型在醫療影像分析中展現出巨大潛力，特別是在結合放射學影像與臨床文本數據方面。",
    topics: ["多模態"],
    subtopics: ["醫療AI", "影像分析"],
    author: {
      id: "user7",
      name: "醫療AI研究員",
      avatar: "",
    },
    sourceType: "top_author",
    timestamp: "4天前",
    stats: {
      likes: 52,
      dislikes: 1,
      comments: 9,
      bookmarks: 27,
    },
  },
  {
    id: 8,
    semanticType: "experience" as const,
    title: "使用 Mistral 7B 構建企業知識庫的實踐經驗",
    content: "在企業環境中部署 Mistral 7B 模型作為知識庫基礎，我們發現其在資源效率和回答質量上的表現令人驚喜。",
    topics: ["LLM"],
    subtopics: ["Mistral", "企業應用"],
    author: {
      id: "user8",
      name: "企業AI架構師",
      avatar: "",
    },
    sourceType: "community",
    timestamp: "1週前",
    stats: {
      likes: 34,
      dislikes: 2,
      comments: 16,
      bookmarks: 22,
    },
  },
]

export default function ViewpointCardGridDemo() {
  const [isCompact, setIsCompact] = useState(true)
  const [gap, setGap] = useState<"sm" | "md" | "lg">("sm")

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">四列觀點卡網格展示</h1>
        <p className="text-muted-foreground">展示觀點卡在四列布局下的排列效果</p>
      </div>

      <div className="flex flex-wrap gap-4 items-center">
        <div className="space-x-2">
          <Button variant={isCompact ? "default" : "outline"} onClick={() => setIsCompact(true)} size="sm">
            精簡模式
          </Button>
          <Button variant={!isCompact ? "default" : "outline"} onClick={() => setIsCompact(false)} size="sm">
            完整內容
          </Button>
        </div>

        <div className="space-x-2">
          <Button variant={gap === "sm" ? "default" : "outline"} onClick={() => setGap("sm")} size="sm">
            小間距
          </Button>
          <Button variant={gap === "md" ? "default" : "outline"} onClick={() => setGap("md")} size="sm">
            中間距
          </Button>
          <Button variant={gap === "lg" ? "default" : "outline"} onClick={() => setGap("lg")} size="sm">
            大間距
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="all">全部</TabsTrigger>
          <TabsTrigger value="insight">看法</TabsTrigger>
          <TabsTrigger value="experience">實測經驗</TabsTrigger>
          <TabsTrigger value="guide">工具教學</TabsTrigger>
          <TabsTrigger value="trap">踩坑警示</TabsTrigger>
          <TabsTrigger value="debate">爭議論點</TabsTrigger>
          <TabsTrigger value="concept">概念整理</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-0">
          <ViewpointCardGrid cards={sampleCards} columns={4} gap={gap} isCompact={isCompact} />
        </TabsContent>

        <TabsContent value="insight" className="mt-0">
          <ViewpointCardGrid
            cards={sampleCards.filter((card) => card.semanticType === "insight")}
            columns={4}
            gap={gap}
            isCompact={isCompact}
          />
        </TabsContent>

        <TabsContent value="experience" className="mt-0">
          <ViewpointCardGrid
            cards={sampleCards.filter((card) => card.semanticType === "experience")}
            columns={4}
            gap={gap}
            isCompact={isCompact}
          />
        </TabsContent>

        <TabsContent value="guide" className="mt-0">
          <ViewpointCardGrid
            cards={sampleCards.filter((card) => card.semanticType === "guide")}
            columns={4}
            gap={gap}
            isCompact={isCompact}
          />
        </TabsContent>

        <TabsContent value="trap" className="mt-0">
          <ViewpointCardGrid
            cards={sampleCards.filter((card) => card.semanticType === "trap")}
            columns={4}
            gap={gap}
            isCompact={isCompact}
          />
        </TabsContent>

        <TabsContent value="debate" className="mt-0">
          <ViewpointCardGrid
            cards={sampleCards.filter((card) => card.semanticType === "debate")}
            columns={4}
            gap={gap}
            isCompact={isCompact}
          />
        </TabsContent>

        <TabsContent value="concept" className="mt-0">
          <ViewpointCardGrid
            cards={sampleCards.filter((card) => card.semanticType === "concept")}
            columns={4}
            gap={gap}
            isCompact={isCompact}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
