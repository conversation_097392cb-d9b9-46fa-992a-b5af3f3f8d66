"use client"

import { useEffect } from "react"
import { useAuth } from "@/contexts/auth-context"
import { usePathname } from "next/navigation"

export function AuthStateSync() {
  const { isAuthenticated, refreshSession } = useAuth()
  const pathname = usePathname()

  // 在每次路由變化時同步認證狀態
  useEffect(() => {
    // 如果已經認證，確保會話有效
    if (isAuthenticated) {
      console.log("Syncing auth state on path change:", pathname)
      refreshSession().catch(console.error)
    }
  }, [pathname, isAuthenticated, refreshSession])

  // 定期刷新會話
  useEffect(() => {
    if (!isAuthenticated) return

    // 每 5 分鐘刷新一次會話
    const interval = setInterval(
      () => {
        console.log("Periodic session refresh")
        refreshSession().catch(console.error)
      },
      5 * 60 * 1000,
    )

    return () => clearInterval(interval)
  }, [isAuthenticated, refreshSession])

  return null
}
