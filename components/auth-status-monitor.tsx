"use client"

import { useAuth } from "@/contexts/auth-context"
import { useState } from "react"
import { ChevronDown, ChevronUp, User, X } from "lucide-react"

export function AuthStatusMonitor() {
    const { user, isAuthenticated, isLoading } = useAuth()
    const [isExpanded, setIsExpanded] = useState(false)
    const [isVisible, setIsVisible] = useState(true)

    // 只在開發環境中顯示
    if (process.env.NODE_ENV !== "development" || !isVisible) {
        return null
    }

    return (
        <div className="fixed bottom-4 right-4 z-50">
            <div className="bg-black/80 text-white p-3 rounded-lg shadow-lg relative">
                <button
                    onClick={() => setIsVisible(false)}
                    className="absolute -top-2 -right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 text-xs transition-colors"
                    title="關閉 Auth 狀態監控"
                >
                    <X className="h-3 w-3" />
                </button>

                <button
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="flex items-center gap-2 text-sm font-mono"
                >
                    <User className="h-4 w-4" />
                    <span>
                        Auth: {isLoading ? "Loading" : isAuthenticated ? "✓" : "✗"}
                    </span>
                    {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
                </button>

                {isExpanded && (
                    <div className="mt-2 pt-2 border-t border-white/20 text-xs font-mono">
                        <div>Loading: {String(isLoading)}</div>
                        <div>Authenticated: {String(isAuthenticated)}</div>
                        {user && (
                            <>
                                <div>User ID: {user.id.slice(0, 8)}...</div>
                                <div>Email: {user.email}</div>
                            </>
                        )}
                    </div>
                )}
            </div>
        </div>
    )
} 