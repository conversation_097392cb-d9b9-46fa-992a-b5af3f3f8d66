"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Bookmark, BookmarkCheck } from "lucide-react"
import { useBookmarks } from "@/hooks/use-bookmarks"
import { cn } from "@/lib/utils"

interface BookmarkButtonProps {
    itemId: string
    itemType: "card" | "thread"
    className?: string
    variant?: "default" | "ghost" | "outline" | "secondary"
    size?: "default" | "sm" | "lg" | "icon"
    showCount?: boolean
    disabled?: boolean
    forceBookmarked?: boolean // 強制顯示為已收藏狀態，用於 library 頁面
}

export function BookmarkButton({
    itemId,
    itemType,
    className,
    variant = "ghost",
    size = "sm",
    showCount = false,
    disabled = false,
    forceBookmarked = false,
}: BookmarkButtonProps) {
    const [isInitialized, setIsInitialized] = useState(false)

    const {
        isBookmarked,
        isLoading,
        count,
        toggleBookmark,
        refreshBookmarkStatus,
        refreshBookmarkCount,
    } = useBookmarks({
        itemId,
        itemType,
    })

    // 組件初始化時獲取收藏狀態和數量
    useEffect(() => {
        const initializeBookmarkData = async () => {
            if (forceBookmarked) {
                // 如果強制顯示為已收藏，只獲取收藏數，跳過狀態檢查
                if (showCount) {
                    await refreshBookmarkCount()
                }
            } else {
                // 正常模式：獲取狀態和數量
                await Promise.all([
                    refreshBookmarkStatus(),
                    showCount ? refreshBookmarkCount() : Promise.resolve(),
                ])
            }
            setIsInitialized(true)
        }

        initializeBookmarkData()
    }, [refreshBookmarkStatus, refreshBookmarkCount, showCount, forceBookmarked])

    const handleClick = async () => {
        if (disabled || isLoading) return
        // 如果強制顯示為已收藏，禁用點擊功能
        if (forceBookmarked) return
        await toggleBookmark()
    }

    // 在初始化完成前顯示載入狀態
    if (!isInitialized) {
        return (
            <button
                className={cn(
                    "flex items-center gap-1 cursor-pointer transition-colors opacity-50",
                    className
                )}
                disabled
            >
                <Bookmark className="h-3.5 w-3.5" />
                {showCount && <span>-</span>}
            </button>
        )
    }

    // 決定顯示狀態：如果強制收藏或實際已收藏
    const displayAsBookmarked = forceBookmarked || isBookmarked

    return (
        <button
            className={cn(
                "flex items-center gap-1 cursor-pointer transition-colors",
                displayAsBookmarked ? "text-primary" : "hover:text-primary/70",
                forceBookmarked && "cursor-default", // 強制收藏時禁用點擊樣式
                className
            )}
            onClick={handleClick}
            disabled={disabled || isLoading || forceBookmarked}
            aria-label={`${count > 0 ? count : ''} 個收藏`}
            title={forceBookmarked ? "已收藏" : (displayAsBookmarked ? "取消收藏" : "收藏")}
        >
            <Bookmark className={cn("h-3.5 w-3.5", displayAsBookmarked && "fill-current")} />
            {showCount && count > 0 && (
                <span className="text-xs">
                    {count}
                </span>
            )}
        </button>
    )
} 