"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { CheckCircle } from "lucide-react"

const bugReportSchema = z.object({
  type: z.string({
    required_error: "請選擇問題類型",
  }),
  title: z.string().min(5, {
    message: "標題至少需要 5 個字符",
  }),
  description: z.string().min(10, {
    message: "描述至少需要 10 個字符",
  }),
  email: z
    .string()
    .email({
      message: "請輸入有效的電子郵件地址",
    })
    .optional()
    .or(z.literal("")),
})

type BugReportFormValues = z.infer<typeof bugReportSchema>

export function BugReportForm() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const form = useForm<BugReportFormValues>({
    resolver: zodResolver(bugReportSchema),
    defaultValues: {
      type: "",
      title: "",
      description: "",
      email: "",
    },
  })

  async function onSubmit(data: BugReportFormValues) {
    setIsSubmitting(true)

    // 模擬提交過程
    await new Promise((resolve) => setTimeout(resolve, 1000))

    console.log("Bug report submitted:", data)
    setIsSubmitting(false)
    setIsSubmitted(true)
  }

  if (isSubmitted) {
    return (
      <div className="flex flex-col items-center justify-center py-6 space-y-4 text-center">
        <CheckCircle className="h-12 w-12 text-green-500" />
        <h3 className="text-xl font-medium">感謝您的回報！</h3>
        <p className="text-muted-foreground">我們已收到您的問題回報，將盡快處理並改進平台。</p>
        <Button
          variant="outline"
          onClick={() => {
            setIsSubmitted(false)
            form.reset()
          }}
        >
          提交另一個回報
        </Button>
      </div>
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>問題類型</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="選擇問題類型" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="bug">功能錯誤</SelectItem>
                  <SelectItem value="content">內容問題</SelectItem>
                  <SelectItem value="feature">功能建議</SelectItem>
                  <SelectItem value="other">其他</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>標題</FormLabel>
              <FormControl>
                <Input placeholder="簡短描述問題" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>詳細描述</FormLabel>
              <FormControl>
                <Textarea placeholder="請詳細描述您遇到的問題或建議..." className="min-h-[120px]" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>電子郵件（可選）</FormLabel>
              <FormControl>
                <Input type="email" placeholder="您的電子郵件地址" {...field} />
              </FormControl>
              <FormDescription>如果您希望收到回覆，請提供您的電子郵件地址。</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "提交中..." : "提交回報"}
        </Button>
      </form>
    </Form>
  )
}
