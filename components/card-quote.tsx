"use client"

import type React from "react"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { RichTextContent } from "@/components/rich-text-content"
import { Sparkles } from "lucide-react"
import { getSemanticTypeConfig } from "@/lib/constants"

interface CardQuoteProps {
  card: {
    id: number
    title: string
    content: string
    author: string
    type: string
    tags: string[]
    isLeader?: boolean
    topics?: Array<{ id: string | number; name: string }>
  }
  className?: string
}

export function CardQuote({ card, className }: CardQuoteProps) {
  const typeConfig = getSemanticTypeConfig(card.type as string)

  // 截斷內容
  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + "..."
  }

  return (
    <div className={cn("border rounded-md overflow-hidden bg-card", className)}>
      <div className={`p-3 border-l-4 ${typeConfig.color.replace("bg-", "border-")}`}>
        <div className="flex flex-col gap-1">
          <div className="flex flex-wrap items-center gap-2">
            <Badge variant="outline" className={cn("flex items-center gap-1", typeConfig.color)}>
              {typeConfig.icon}
              <span>{typeConfig.label}</span>
            </Badge>

            {/* 主題標籤 */}
            {card.topics && card.topics.length > 0 && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <Sparkles className="h-3 w-3" />
                <span>{card.topics[0].name}</span>
              </Badge>
            )}

            {card.tags && card.tags.length > 0 && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <span>{card.tags[0]}</span>
              </Badge>
            )}
          </div>
          <div className="text-xs text-muted-foreground">作者：{card.author}</div>
          <h4 className="text-sm font-bold">
            <a href={`/card/${card.id}`} className="text-primary hover:underline">
              {card.title}
            </a>
          </h4>
          <div className="text-xs text-muted-foreground line-clamp-2 overflow-hidden">
            <RichTextContent content={card.content} className="prose-sm" />
          </div>
        </div>
      </div>
    </div>
  )
}
