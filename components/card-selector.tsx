"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Search, X, FileText, MessageSquare } from "lucide-react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  BookOpen,
  Wrench,
  AlertTriangle,
  FlaskConical,
  LightbulbIcon,
  HelpCircle,
  Zap,
  MessageCircle,
  Loader2,
  Sparkles,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { createBrowserClient } from "@/lib/supabase/client"
import { RichTextContent } from "@/components/rich-text-content"
import {
  getSemanticTypeConfig,
  getContributionTypeConfig,
  getContentTypeConfig,
  type ContentType,
  CONTENT_TYPE_CONFIG
} from "@/lib/constants"

interface CardSelectorProps {
  onSelectCard: (card: any) => void
  buttonText?: React.ReactNode
  initialFilter?: ContentType | "all"
}

export function CardSelector({ onSelectCard, buttonText = "引用卡片", initialFilter = "all" }: CardSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [filteredCards, setFilteredCards] = useState<any[]>([])
  const [selectedIndex, setSelectedIndex] = useState(-1) // 初始值設為 -1，表示沒有選中任何卡片
  const [hoveredIndex, setHoveredIndex] = useState(-1) // 新增懸停索引狀態
  const [activeFilter, setActiveFilter] = useState<ContentType | "all">(initialFilter) // 添加过滤状态
  const [isLoading, setIsLoading] = useState(false)
  const [cards, setCards] = useState<any[]>([])
  const [threads, setThreads] = useState<any[]>([])

  const supabase = createBrowserClient()

  // 當對話框打開時獲取數據
  useEffect(() => {
    if (isOpen) {
      fetchData()
    }
  }, [isOpen])

  // 獲取卡片和討論串數據
  const fetchData = async () => {
    setIsLoading(true)
    try {
      // 獲取卡片數據
      const { data: cardsData, error: cardsError } = await supabase
        .from("cards")
        .select(`
          id,
          title,
          content,
          semantic_type,
          contribution_type,
          author:profiles(id, name, avatar),
          created_at,
          card_topics!inner(topic:topics(id, name))
        `)
        .eq("status", "published") // 只顯示已發布的觀點卡
        .order("created_at", { ascending: false })
        .limit(20)

      if (cardsError) {
        console.error("Error fetching cards:", cardsError)
      } else {
        const formattedCards = cardsData.map((card: any) => ({
          ...card,
          contentType: "viewpoint" as ContentType,
          type: card.semantic_type || "concept",
          isLeader: card.contribution_type === "top_author",
          topics: card.card_topics?.map((ct: any) => ct.topic) || [],
          // Process author like in CardMention
          authorName: card.author && !Array.isArray(card.author) ? (card.author.name || "未知作者") : "未知作者",
        }))
        setCards(formattedCards)
      }

      // 獲取討論串數據 - 移除 stats 欄位
      const { data: threadsData, error: threadsError } = await supabase
        .from("threads")
        .select(`
          id,
          title,
          content,
          semantic_type,
          author:profiles(id, name, avatar),
          created_at,
          thread_topics!inner(topic:topics(id, name))
        `)
        .eq("status", "published") // 只顯示已發布的討論
        .order("created_at", { ascending: false })
        .limit(20)

      if (threadsError) {
        console.error("Error fetching threads:", threadsError)
      } else {
        // 處理討論串數據 - 不再使用 stats 欄位
        const formattedThreads = threadsData.map((thread: any) => ({
          ...thread,
          contentType: "thread" as ContentType,
          type: thread.semantic_type || "discussion",
          topics: thread.thread_topics?.map((tt: any) => tt.topic) || [],
          // Process author like in CardMention
          authorName: thread.author && !Array.isArray(thread.author) ? (thread.author.name || "未知作者") : "未知作者",
        }))
        setThreads(formattedThreads)
      }
    } catch (error) {
      console.error("Error fetching data:", error)
    } finally {
      setIsLoading(false)
    }
  }

  // 當搜索詞變化時過濾卡片
  useEffect(() => {
    // 首先按内容类型过滤
    let filtered: any[] = []

    if (activeFilter === "all") {
      filtered = [...cards, ...threads]
    } else if (activeFilter === "viewpoint") {
      filtered = [...cards]
    } else if (activeFilter === "thread") {
      filtered = [...threads]
    }

    // 然后按搜索词过滤
    if (searchTerm.trim()) {
      const lowerSearchTerm = searchTerm.toLowerCase()

      // 检查是否有高级过滤前缀
      if (lowerSearchTerm.startsWith("card:")) {
        const specificTerm = lowerSearchTerm.substring(5).trim()
        filtered = filtered.filter(
          (card) =>
            card.contentType === "viewpoint" &&
            (specificTerm === "" ||
              card.title.toLowerCase().includes(specificTerm) ||
              card.content.toLowerCase().includes(specificTerm) ||
              (card.authorName && card.authorName.toLowerCase().includes(specificTerm)) ||
              card.type.toLowerCase().includes(specificTerm) ||
              (card.topics && card.topics.some((topic: any) => topic.name.toLowerCase().includes(specificTerm)))),
        )
      } else if (lowerSearchTerm.startsWith("thread:")) {
        const specificTerm = lowerSearchTerm.substring(7).trim()
        filtered = filtered.filter(
          (card) =>
            card.contentType === "thread" &&
            (specificTerm === "" ||
              card.title.toLowerCase().includes(specificTerm) ||
              card.content.toLowerCase().includes(specificTerm) ||
              (card.authorName && card.authorName.toLowerCase().includes(specificTerm)) ||
              card.type.toLowerCase().includes(specificTerm) ||
              (card.topics && card.topics.some((topic: any) => topic.name.toLowerCase().includes(specificTerm)))),
        )
      } else {
        // 常规搜索
        filtered = filtered.filter(
          (card) =>
            card.title.toLowerCase().includes(lowerSearchTerm) ||
            card.content.toLowerCase().includes(lowerSearchTerm) ||
            (card.authorName && card.authorName.toLowerCase().includes(lowerSearchTerm)) ||
            card.type.toLowerCase().includes(lowerSearchTerm) ||
            (card.topics && card.topics.some((topic: any) => topic.name.toLowerCase().includes(lowerSearchTerm))),
        )
      }
    }

    setFilteredCards(filtered)
  }, [searchTerm, activeFilter, cards, threads])

  // 按内容类型分组
  const viewpointCards = filteredCards.filter((card) => card.contentType === "viewpoint")
  const threadCards = filteredCards.filter((card) => card.contentType === "thread")

  // 當對話框打開時重置狀態
  useEffect(() => {
    if (isOpen) {
      setSelectedIndex(-1)
      setHoveredIndex(-1)
    }
  }, [isOpen])

  // 處理卡片選擇 - 修改為規範化卡片數據
  const handleSelectCard = (card: any) => {
    // 創建一個新的規範化卡片對象，確保沒有嵌套對象
    const normalizedCard = {
      id: card.id,
      title: card.title,
      content: card.content,
      type: card.type,
      contentType: card.contentType,
      authorId: card.author_id,
      authorName: card.authorName,
      created_at: card.created_at,
      // 只保留主題的 id 和 name
      topics: card.topics
        ? card.topics.map((topic: any) => ({
          id: topic.id,
          name: topic.name,
        }))
        : [],
      // 其他必要的字段
      semantic_type: card.semantic_type,
      contribution_type: card.contribution_type,
    }

    onSelectCard(normalizedCard)
    setIsOpen(false)
    setSearchTerm("")
  }

  // 處理鍵盤導航
  const handleKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case "ArrowDown":
        e.preventDefault()
        setSelectedIndex((prev) => (prev < filteredCards.length - 1 ? prev + 1 : prev))
        break
      case "ArrowUp":
        e.preventDefault()
        setSelectedIndex((prev) => (prev > 0 ? prev - 1 : 0))
        break
      case "Enter":
        if (filteredCards.length > 0 && selectedIndex >= 0) {
          e.preventDefault()
          handleSelectCard(filteredCards[selectedIndex])
        }
        break
      case "Escape":
        e.preventDefault()
        setIsOpen(false)
        break
    }
  }

  // 截断内容
  const truncateText = (text: string, maxLength: number) => {
    if (!text) return ""
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + "..."
  }

  // 处理过滤器点击
  const handleFilterClick = (filter: ContentType | "all") => {
    setActiveFilter(filter)
    setSelectedIndex(-1)
    setHoveredIndex(-1)
  }

  // 处理搜索输入变化
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchTerm(value)

    // 检查是否有高级过滤前缀
    if (value.startsWith("card:")) {
      setActiveFilter("viewpoint")
    } else if (value.startsWith("thread:")) {
      setActiveFilter("thread")
    }
  }

  // 渲染卡片项
  const renderCardItem = (card: any, index: number, globalIndex: number) => {
    const typeConfig = getSemanticTypeConfig(card.type)
    const sourceConfig = getContributionTypeConfig(card.isLeader ? "leader" : "community")
    const contentConfig = getContentTypeConfig(card.contentType)

    // 获取主题名称
    const topicName = card.topics && card.topics.length > 0 ? card.topics[0].name : "未分類"

    return (
      <div
        key={card.id}
        className={cn(
          "px-3 py-2 cursor-pointer hover:bg-muted border-b border-border/40 last:border-b-0",
          (globalIndex === selectedIndex || globalIndex === hoveredIndex) && "bg-muted",
        )}
        onClick={() => handleSelectCard(card)}
        onMouseEnter={() => setHoveredIndex(globalIndex)}
        onMouseLeave={() => setHoveredIndex(-1)}
      >
        <div className="flex flex-col gap-1">
          {/* 卡片头部 - 类型徽章和标签 */}
          <div className="flex flex-wrap items-center gap-2">
            {/* 内容类型徽章 */}
            <Badge variant="outline" className={cn("flex items-center gap-1", contentConfig.color)}>
              {contentConfig.icon}
              <span>{contentConfig.label}</span>
            </Badge>

            {/* 语义类型徽章 */}
            <Badge variant="outline" className={cn("flex items-center gap-1", typeConfig.color)}>
              {typeConfig.icon}
              <span>{typeConfig.label}</span>
            </Badge>

            {/* 主题徽章 */}
            {card.topics && card.topics.length > 0 && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <Sparkles className="h-3 w-3" />
                <span>{topicName}</span>
              </Badge>
            )}

            {/* 来源类型徽章 */}
            {card.isLeader && sourceConfig.show && (
              <Badge variant="default" className={cn("flex items-center gap-1", sourceConfig.color)}>
                <span>{sourceConfig.badge}</span>
              </Badge>
            )}

            {/* 移除状态徽章 */}
          </div>

          {/* 作者信息 - 使用处理过的 authorName */}
          <div className="text-xs text-muted-foreground">作者：{card.authorName}</div>

          {/* 标题 */}
          <h4 className="text-sm font-bold text-foreground">{card.title}</h4>

          {/* 内容 */}
          <div className="text-xs text-muted-foreground line-clamp-2 overflow-hidden">
            <RichTextContent content={card.content} className="prose-sm" />
          </div>

          {/* 讨论串特有信息 - 移除 replies 显示 */}
          {card.contentType === "thread" && (
            <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
              <div className="flex items-center gap-1">
                <MessageSquare className="h-3.5 w-3.5" />
              </div>
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm" className="gap-2">
          {buttonText}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]" onKeyDown={handleKeyDown}>
        <DialogHeader>
          <DialogTitle>選擇要引用的內容</DialogTitle>
        </DialogHeader>

        {/* 过滤器选项 */}
        <div className="flex items-center p-2 gap-2 border-b">
          <Badge
            variant={activeFilter === "all" ? "default" : "outline"}
            className="cursor-pointer"
            onClick={() => handleFilterClick("all")}
          >
            全部
          </Badge>
          <Badge
            variant={activeFilter === "viewpoint" ? "default" : "outline"}
            className={cn("cursor-pointer flex items-center gap-1", activeFilter === "viewpoint" ? "bg-blue-600" : "")}
            onClick={() => handleFilterClick("viewpoint")}
          >
            {CONTENT_TYPE_CONFIG.viewpoint.icon}
            觀點卡
          </Badge>
          <Badge
            variant={activeFilter === "thread" ? "default" : "outline"}
            className={cn("cursor-pointer flex items-center gap-1", activeFilter === "thread" ? "bg-purple-600" : "")}
            onClick={() => handleFilterClick("thread")}
          >
            {CONTENT_TYPE_CONFIG.thread.icon}
            討論串
          </Badge>
        </div>

        <div className="relative mb-4">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索卡片標題、內容、標籤或作者..."
            className="pl-8"
            value={searchTerm}
            onChange={handleSearchChange}
            autoFocus
          />
          {searchTerm && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1 h-7 w-7 p-0"
              onClick={() => setSearchTerm("")}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">清除搜索</span>
            </Button>
          )}
        </div>

        <ScrollArea className="h-[400px] pr-4">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">加載中...</span>
            </div>
          ) : (
            <div className="space-y-1">
              {filteredCards.length > 0 ? (
                <>
                  {/* 如果有观点卡且不是只显示讨论串 */}
                  {viewpointCards.length > 0 && activeFilter !== "thread" && (
                    <>
                      {/* 不显示分组标题 */}
                      {viewpointCards.map((card, index) => renderCardItem(card, index, index))}
                    </>
                  )}

                  {/* 如果有讨论串且不是只显示观点卡 */}
                  {threadCards.length > 0 && activeFilter !== "viewpoint" && (
                    <>
                      {/* 不显示分组标题 */}
                      {threadCards.map((card, index) => renderCardItem(card, index, viewpointCards.length + index))}
                    </>
                  )}
                </>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <p>沒有找到符合條件的內容</p>
                </div>
              )}
            </div>
          )}
        </ScrollArea>

        {/* 底部提示 */}
        <div className="text-xs text-muted-foreground border-t pt-2">
          提示: 輸入 card: 或 thread: 可快速過濾特定類型
        </div>
      </DialogContent>
    </Dialog>
  )
}
