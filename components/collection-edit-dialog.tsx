"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Loader2 } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { getCollectionDetails, updateCollection, deleteCollection } from "@/lib/bookmark-service"

interface Collection {
    id: string
    name: string
    description?: string
    is_public: boolean
}

interface CollectionEditDialogProps {
    collectionId: string
    open: boolean
    onOpenChange: (open: boolean) => void
    onSaved?: () => void
    onDeleted?: () => void
}

export function CollectionEditDialog({
    collectionId,
    open,
    onOpenChange,
    onSaved,
    onDeleted,
}: CollectionEditDialogProps) {
    const { toast } = useToast()
    const [collection, setCollection] = useState<Collection | null>(null)
    const [name, setName] = useState("")
    const [description, setDescription] = useState("")
    const [isPublic, setIsPublic] = useState(true)
    const [isLoading, setIsLoading] = useState(false)
    const [isSaving, setIsSaving] = useState(false)
    const [isDeleting, setIsDeleting] = useState(false)
    const [showDeleteDialog, setShowDeleteDialog] = useState(false)

    // 獲取收藏牆詳情
    useEffect(() => {
        if (open && collectionId) {
            const fetchData = async () => {
                setIsLoading(true)
                try {
                    const result = await getCollectionDetails(collectionId)
                    if (result.success && result.data) {
                        const collectionData = result.data
                        setCollection(collectionData)
                        setName(collectionData.name)
                        setDescription(collectionData.description || "")
                        setIsPublic(collectionData.is_public)
                    } else {
                        toast({
                            title: "無法獲取收藏牆",
                            description: result.error || "該收藏牆可能不存在或您沒有權限訪問",
                            variant: "destructive",
                        })
                        onOpenChange(false)
                    }
                } catch (error) {
                    console.error("Error fetching collection:", error)
                    toast({
                        title: "獲取數據失敗",
                        description: "請稍後再試",
                        variant: "destructive",
                    })
                    onOpenChange(false)
                } finally {
                    setIsLoading(false)
                }
            }
            fetchData()
        }
    }, [open, collectionId, onOpenChange, toast])

    // 重置表單
    const resetForm = () => {
        setName("")
        setDescription("")
        setIsPublic(true)
        setCollection(null)
    }

    // 處理對話框關閉
    const handleOpenChange = (newOpen: boolean) => {
        if (!newOpen && !isSaving && !isDeleting) {
            resetForm()
        }
        onOpenChange(newOpen)
    }

    // 保存收藏牆
    const handleSave = async () => {
        if (!name.trim()) {
            toast({
                title: "請輸入收藏牆名稱",
                variant: "destructive",
            })
            return
        }

        setIsSaving(true)
        try {
            const result = await updateCollection(collectionId, {
                name,
                description,
                isPublic,
            })
            if (result.success) {
                toast({
                    title: "保存成功",
                    description: "收藏牆已更新",
                })
                onSaved?.()
                handleOpenChange(false)
            } else {
                toast({
                    title: "保存失敗",
                    description: result.error || "請稍後再試",
                    variant: "destructive",
                })
            }
        } catch (error) {
            console.error("Error saving collection:", error)
            toast({
                title: "保存失敗",
                description: "請稍後再試",
                variant: "destructive",
            })
        } finally {
            setIsSaving(false)
        }
    }

    // 刪除收藏牆
    const handleDelete = async () => {
        setIsDeleting(true)
        try {
            const result = await deleteCollection(collectionId)
            if (result.success) {
                toast({
                    title: "刪除成功",
                    description: "收藏牆已刪除",
                })
                onDeleted?.()
                handleOpenChange(false)
            } else {
                toast({
                    title: "刪除失敗",
                    description: result.error || "請稍後再試",
                    variant: "destructive",
                })
            }
        } catch (error) {
            console.error("Error deleting collection:", error)
            toast({
                title: "刪除失敗",
                description: "請稍後再試",
                variant: "destructive",
            })
        } finally {
            setIsDeleting(false)
            setShowDeleteDialog(false)
        }
    }

    return (
        <>
            <Dialog open={open} onOpenChange={handleOpenChange}>
                <DialogContent className="sm:max-w-[500px]">
                    <DialogHeader>
                        <DialogTitle>編輯收藏牆</DialogTitle>
                        <DialogDescription>更新您的收藏牆信息和隱私設置</DialogDescription>
                    </DialogHeader>

                    {isLoading ? (
                        <div className="flex justify-center items-center py-8">
                            <Loader2 className="h-6 w-6 animate-spin" />
                        </div>
                    ) : (
                        <>
                            <div className="grid gap-4 py-4">
                                <div className="grid gap-2">
                                    <Label htmlFor="name">收藏牆名稱</Label>
                                    <Input
                                        id="name"
                                        value={name}
                                        onChange={(e) => setName(e.target.value)}
                                        placeholder="輸入收藏牆名稱"
                                        disabled={isSaving}
                                    />
                                </div>
                                <div className="grid gap-2">
                                    <Label htmlFor="description">描述 (選填)</Label>
                                    <Textarea
                                        id="description"
                                        value={description}
                                        onChange={(e) => setDescription(e.target.value)}
                                        placeholder="簡短描述這個收藏牆"
                                        disabled={isSaving}
                                        rows={3}
                                    />
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Switch
                                        id="public"
                                        checked={isPublic}
                                        onCheckedChange={setIsPublic}
                                        disabled={isSaving}
                                    />
                                    <Label htmlFor="public">公開收藏牆</Label>
                                </div>
                            </div>

                            <DialogFooter className="flex justify-between">
                                <Button
                                    variant="destructive"
                                    onClick={() => setShowDeleteDialog(true)}
                                    disabled={isSaving}
                                >
                                    刪除收藏牆
                                </Button>
                                <div className="flex gap-2">
                                    <Button
                                        variant="outline"
                                        onClick={() => handleOpenChange(false)}
                                        disabled={isSaving}
                                    >
                                        取消
                                    </Button>
                                    <Button onClick={handleSave} disabled={isSaving}>
                                        {isSaving ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                                        保存更改
                                    </Button>
                                </div>
                            </DialogFooter>
                        </>
                    )}
                </DialogContent>
            </Dialog>

            {/* 刪除確認對話框 */}
            <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>確定要刪除嗎？</AlertDialogTitle>
                        <AlertDialogDescription>
                            此操作將永久刪除收藏牆「{collection?.name}」及其中的所有項目。此操作無法撤銷。
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel disabled={isDeleting}>取消</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleDelete}
                            disabled={isDeleting}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                            {isDeleting ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                            確定刪除
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    )
} 