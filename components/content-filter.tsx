"use client"

import React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Separator } from "@/components/ui/separator"
import { Filter, X, Loader2 } from "lucide-react"

// 語義類型選項
export const SEMANTIC_TYPES = [
    // 觀點卡語義類型
    { value: "insight", label: "看法" },
    { value: "experience", label: "實測經驗" },
    { value: "guide", label: "工具教學" },
    { value: "trap", label: "踩坑警示" },
    { value: "debate", label: "爭議論點" },
    { value: "concept", label: "概念整理" },
    // 討論語義類型
    { value: "thread", label: "討論" },
    { value: "question", label: "問題" },
    { value: "brainstorm", label: "集思廣益" },
    { value: "chat", label: "閒聊" },
]

// 主題和子主題的類型定義
export interface Topic {
    id: string
    name: string
    slug: string
    description?: string
    subtopics?: Subtopic[]
}

export interface Subtopic {
    id: string
    name: string
    slug: string
    description?: string
    topic_id: string
    topics?: {
        id: string
        name: string
        slug: string
    }
}

// Filter 狀態類型
export interface FilterState {
    selectedSemanticTypes: string[]
    selectedTopics: string[]
    selectedSubtopics: string[]
}

// Filter 操作類型
export interface FilterActions {
    toggleSemanticType: (type: string) => void
    toggleTopic: (topic: string) => void
    toggleSubtopic: (subtopic: string) => void
    clearFilters: () => void
}

// 組件 props 類型
interface ContentFilterProps {
    filterState: FilterState
    filterActions: FilterActions
    availableTopics: Topic[]
    availableSubtopics: Subtopic[]
    isLoadingFilters: boolean
    showSemanticTypes?: boolean
    showTopics?: boolean
    showSubtopics?: boolean
    buttonVariant?: "outline" | "default" | "secondary" | "ghost"
    buttonSize?: "default" | "sm" | "lg" | "icon"
    buttonText?: string
    showFilterCount?: boolean
    popoverAlign?: "start" | "center" | "end"
    popoverWidth?: string
}

// Filter 標籤組件
interface FilterTagsProps {
    filterState: FilterState
    filterActions: FilterActions
    availableTopics: Topic[]
    availableSubtopics: Subtopic[]
    showClearAll?: boolean
    onClearAll?: () => void
}

// 工具函數
export const getSemanticTypeLabel = (value: string) => {
    const semanticType = SEMANTIC_TYPES.find(type => type.value === value)
    return semanticType?.label || value
}

export const getTopicLabel = (value: string, availableTopics: Topic[]) => {
    const topic = availableTopics.find(topic => topic.name === value || topic.slug === value)
    return topic?.name || value
}

export const getSubtopicLabel = (value: string, availableSubtopics: Subtopic[]) => {
    const subtopic = availableSubtopics.find(subtopic => subtopic.name === value || subtopic.slug === value)
    return subtopic?.name || value
}

// Filter 標籤組件
export function FilterTags({
    filterState,
    filterActions,
    availableTopics,
    availableSubtopics,
    showClearAll = true,
    onClearAll
}: FilterTagsProps) {
    const { selectedSemanticTypes, selectedTopics, selectedSubtopics } = filterState
    const { toggleSemanticType, toggleTopic, toggleSubtopic, clearFilters } = filterActions

    const hasAnyFilters = selectedSemanticTypes.length > 0 || selectedTopics.length > 0 || selectedSubtopics.length > 0

    if (!hasAnyFilters) return null

    const handleClearAll = () => {
        clearFilters()
        onClearAll?.()
    }

    return (
        <div className="flex flex-wrap items-center gap-2">
            {/* 語義類型標籤 */}
            {selectedSemanticTypes.map((type) => (
                <Badge
                    key={type}
                    variant="secondary"
                    className="cursor-pointer hover:bg-secondary/80"
                    onClick={() => toggleSemanticType(type)}
                >
                    {getSemanticTypeLabel(type)} <X className="ml-1 h-3 w-3" />
                </Badge>
            ))}

            {/* 主題標籤 */}
            {selectedTopics.map((topic) => (
                <Badge
                    key={topic}
                    variant="outline"
                    className="cursor-pointer hover:bg-accent"
                    onClick={() => toggleTopic(topic)}
                >
                    {getTopicLabel(topic, availableTopics)} <X className="ml-1 h-3 w-3" />
                </Badge>
            ))}

            {/* 子主題標籤 */}
            {selectedSubtopics.map((subtopic) => (
                <Badge
                    key={subtopic}
                    variant="outline"
                    className="cursor-pointer hover:bg-accent border-dashed"
                    onClick={() => toggleSubtopic(subtopic)}
                >
                    {getSubtopicLabel(subtopic, availableSubtopics)} <X className="ml-1 h-3 w-3" />
                </Badge>
            ))}

            {/* 清除全部按鈕 */}
            {showClearAll && hasAnyFilters && (
                <Button
                    variant="ghost"
                    size="sm"
                    className="text-xs"
                    onClick={handleClearAll}
                >
                    清除全部
                </Button>
            )}
        </div>
    )
}

// 主要 Filter 組件
export function ContentFilter({
    filterState,
    filterActions,
    availableTopics,
    availableSubtopics,
    isLoadingFilters,
    showSemanticTypes = true,
    showTopics = true,
    showSubtopics = true,
    buttonVariant = "outline",
    buttonSize = "sm",
    buttonText = "篩選",
    showFilterCount = true,
    popoverAlign = "end",
    popoverWidth = "w-80"
}: ContentFilterProps) {
    const { selectedSemanticTypes, selectedTopics, selectedSubtopics } = filterState
    const { toggleSemanticType, toggleTopic, toggleSubtopic, clearFilters } = filterActions

    const filterCount = selectedSemanticTypes.length + selectedTopics.length + selectedSubtopics.length
    const hasFilters = filterCount > 0

    // 動態計算按鈕樣式
    const getButtonClassName = () => {
        if (buttonSize === "icon") {
            // icon 模式下，如果有計數徽章，需要調整樣式
            return hasFilters && showFilterCount
                ? "h-9 w-auto px-2 relative"
                : "h-9 w-9"
        }
        return "h-9"
    }

    return (
        <Popover>
            <PopoverTrigger asChild>
                <Button variant={buttonVariant} size={buttonSize} className={getButtonClassName()}>
                    <Filter className="h-4 w-4" />
                    {buttonText && <span className="ml-2">{buttonText}</span>}
                    {showFilterCount && hasFilters && (
                        <Badge variant="destructive" className="ml-1 h-4 w-4 p-0 text-xs flex items-center justify-center">
                            {filterCount}
                        </Badge>
                    )}
                </Button>
            </PopoverTrigger>
            <PopoverContent className={popoverWidth} align={popoverAlign}>
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <h4 className="font-medium text-sm">過濾條件</h4>
                        {hasFilters && (
                            <Button variant="ghost" size="sm" onClick={clearFilters} className="h-6 px-2 text-xs">
                                清除全部
                            </Button>
                        )}
                    </div>

                    {/* 語義類型過濾 */}
                    {showSemanticTypes && (
                        <>
                            <div>
                                <h5 className="text-xs font-medium text-muted-foreground mb-3 uppercase tracking-wide">內容類型</h5>
                                <div className="grid grid-cols-2 gap-2">
                                    {SEMANTIC_TYPES.map((semanticType) => (
                                        <div key={semanticType.value} className="flex items-center space-x-2">
                                            <Checkbox
                                                id={semanticType.value}
                                                checked={selectedSemanticTypes.includes(semanticType.value)}
                                                onCheckedChange={() => toggleSemanticType(semanticType.value)}
                                                className="h-4 w-4"
                                            />
                                            <label htmlFor={semanticType.value} className="text-sm cursor-pointer">
                                                {semanticType.label}
                                            </label>
                                        </div>
                                    ))}
                                </div>
                            </div>
                            {(showTopics || showSubtopics) && <Separator />}
                        </>
                    )}

                    {/* 主題過濾 */}
                    {showTopics && (
                        <>
                            <div>
                                <h5 className="text-xs font-medium text-muted-foreground mb-3 uppercase tracking-wide">主題領域</h5>
                                {isLoadingFilters ? (
                                    <div className="flex items-center justify-center py-4">
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                        <span className="ml-2 text-sm text-muted-foreground">載入中...</span>
                                    </div>
                                ) : (
                                    <div className="space-y-2 max-h-48 overflow-y-auto">
                                        {availableTopics.map((topic) => (
                                            <div key={topic.id} className="flex items-center space-x-2">
                                                <Checkbox
                                                    id={`topic-${topic.id}`}
                                                    checked={selectedTopics.includes(topic.name)}
                                                    onCheckedChange={() => toggleTopic(topic.name)}
                                                    className="h-4 w-4"
                                                />
                                                <label htmlFor={`topic-${topic.id}`} className="text-sm cursor-pointer font-medium">
                                                    {topic.name}
                                                </label>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                            {showSubtopics && <Separator />}
                        </>
                    )}

                    {/* 子主題過濾 */}
                    {showSubtopics && (
                        <div>
                            <h5 className="text-xs font-medium text-muted-foreground mb-3 uppercase tracking-wide">子主題</h5>
                            {isLoadingFilters ? (
                                <div className="flex items-center justify-center py-4">
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                    <span className="ml-2 text-sm text-muted-foreground">載入中...</span>
                                </div>
                            ) : (
                                <div className="space-y-2 max-h-48 overflow-y-auto">
                                    {availableSubtopics.map((subtopic) => (
                                        <div key={subtopic.id} className="flex items-center space-x-2">
                                            <Checkbox
                                                id={`subtopic-${subtopic.id}`}
                                                checked={selectedSubtopics.includes(subtopic.name)}
                                                onCheckedChange={() => toggleSubtopic(subtopic.name)}
                                                className="h-4 w-4"
                                            />
                                            <label htmlFor={`subtopic-${subtopic.id}`} className="text-sm cursor-pointer">
                                                {subtopic.name}
                                                {subtopic.topics && (
                                                    <span className="text-xs text-muted-foreground ml-1">
                                                        ({subtopic.topics.name})
                                                    </span>
                                                )}
                                            </label>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </PopoverContent>
        </Popover>
    )
} 