"use client"

import { useEffect, useState } from "react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import { Button } from "@/components/ui/button"

export function EnvChecker() {
  const [missingEnvVars, setMissingEnvVars] = useState<string[]>([])
  const [checked, setChecked] = useState(false)
  const [dismissed, setDismissed] = useState(false)

  useEffect(() => {
    // 檢查是否已經在本地存儲中標記為已關閉
    const isDismissed = localStorage.getItem("envCheckerDismissed") === "true"
    if (isDismissed) {
      setDismissed(true)
      return
    }

    const requiredEnvVars = [
      { name: "NEXT_PUBLIC_SUPABASE_URL", value: process.env.NEXT_PUBLIC_SUPABASE_URL },
      { name: "NEXT_PUBLIC_SUPABASE_ANON_KEY", value: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY }
    ]

    // 檢查環境變數是否存在且不為空
    const missing = requiredEnvVars
      .filter((envVar) => !envVar.value || envVar.value.trim() === "")
      .map(envVar => envVar.name)

    console.log("Environment variables check:", {
      NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL ? "Set" : "Missing",
      NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? "Set" : "Missing",
      missingCount: missing.length
    })

    setMissingEnvVars(missing)
    setChecked(true)
  }, [])

  const handleDismiss = () => {
    localStorage.setItem("envCheckerDismissed", "true")
    setDismissed(true)
  }

  // 只在開發環境中顯示
  if (process.env.NODE_ENV === 'production' || !checked || missingEnvVars.length === 0 || dismissed) {
    return null
  }

  return (
    <Alert variant="destructive" className="fixed bottom-4 right-4 max-w-md z-50">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>環境變量缺失</AlertTitle>
      <AlertDescription>
        <p>以下環境變量未設置：</p>
        <ul className="list-disc pl-5 mt-2">
          {missingEnvVars.map((envVar) => (
            <li key={envVar}>{envVar}</li>
          ))}
        </ul>
        <p className="mt-2">請確保在 .env.local 文件中設置這些變量。</p>
        <div className="flex justify-between mt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open("https://supabase.com/docs/guides/getting-started/local-development", "_blank")}
          >
            查看文檔
          </Button>
          <Button variant="outline" size="sm" onClick={handleDismiss}>
            不再顯示
          </Button>
        </div>
      </AlertDescription>
    </Alert>
  )
}
