"use client"

import { ThemeToggle } from "@/components/theme-toggle"
import { useMediaQuery } from "@/hooks/use-media-query"

export function HeaderThemeToggle() {
  const isDesktop = useMediaQuery("(min-width: 768px)")

  return (
    <div className="fixed top-4 right-4 z-50 md:right-6">
      <ThemeToggle
        variant={isDesktop ? "pill" : "ghost"}
        showLabel={isDesktop}
        compact={!isDesktop}
        showTooltip={!isDesktop}
      />
    </div>
  )
}
