import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  BookMarked,
  Flame,
  MessageSquare,
  Sparkles,
  Plus,
  TrendingUp,
  Clock,
  Search,
  ChevronRight,
  ArrowRight,
} from "lucide-react"
import Link from "next/link"
import { TopicSection } from "@/components/topic-section"
import { ContentCard } from "@/components/content-card"
import Image from "next/image"
import { Suspense } from "react"
import { getAllTopics } from "@/lib/topic-service"
import { getPopularCards, getLatestCards } from "@/lib/card-service"
import { getSupabase } from "@/lib/api-utils"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import { safeGetUser } from "@/lib/api-utils"

// 靜態社區統計數據 - 避免不必要的資料庫查詢
const communityStats = {
  activeUsers: 1243,
  newPostsToday: 78,
  totalCards: 4567,
  totalDiscussions: 892,
}

// 載入中佔位元件
function LoadingCards() {
  return (
    <div className="space-y-4">
      {[1, 2].map((i) => (
        <Card key={i} className="w-full h-[200px] animate-pulse">
          <div className="h-full bg-muted/50"></div>
        </Card>
      ))}
    </div>
  )
}

// 將資料獲取和轉換邏輯拆分為獨立元件
async function TopicsSection() {
  // 獲取所有主題及其子主題
  const topicsResponse = await getAllTopics()
  const topics = topicsResponse.success ? (topicsResponse.data || []) : []

  // 處理主題數據，將其分為核心主題和熱門子主題
  const evergreenTopics = topics.slice(0, 4).map((topic) => ({
    id: topic.id,
    name: topic.name,
    description: topic.description,
    cardCount: (topic as any).subtopics?.length || 0,
    icon: Sparkles,
    slug: topic.slug, // 添加這行
  }))

  // 從所有主題中提取熱門子主題
  const hotTopics = topics.slice(0, 2).flatMap((topic) =>
    ((topic as any).subtopics || []).slice(0, 2).map((subtopic: any) => ({
      id: subtopic.id,
      name: subtopic.name,
      description: subtopic.description || `${subtopic.name} 相關的內容`,
      parentTopic: topic.id,
      parentTopicName: topic.name,
      parentTopicSlug: topic.slug, // 添加這行
      slug: subtopic.slug, // 添加這行
      cardCount: 0, // 這裡可以後續優化獲取實際數量
      icon: Sparkles,
    })),
  )

  return (
    <>
      {/* 熱門主題 */}
      <section className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold flex items-center">
              <Flame className="h-5 w-5 mr-2 text-orange-500" />
              熱門主題
            </h2>
            <p className="text-muted-foreground mt-1">探索當前社群最關注的 AI 技術話題</p>
          </div>
          <Button variant="ghost" size="sm" className="gap-1">
            <Link href="/explore?topics=hot" className="flex items-center gap-1">
              查看全部
              <ChevronRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {hotTopics.map((topic) => (
            <Card
              key={topic.id}
              className="overflow-hidden border-border/60 hover:border-primary/30 hover:shadow-md transition-all"
            >
              <CardHeader className="pb-2 space-y-1">
                <div className="flex items-start justify-between">
                  <Badge variant="outline" className="bg-primary/5">
                    {topic.parentTopicName}
                  </Badge>
                  <topic.icon className="h-5 w-5 text-primary" />
                </div>
                <CardTitle className="text-lg mt-2">{topic.name}</CardTitle>
                <CardDescription className="line-clamp-2">{topic.description}</CardDescription>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="text-sm">
                  <span className="font-medium">{topic.cardCount}</span> 張觀點卡
                </div>
              </CardContent>
              <CardFooter className="pt-0">
                <Button variant="ghost" className="w-full justify-between group">
                  <Link
                    href={`/topic/${topic.parentTopicSlug || topic.parentTopic}/${topic.slug || encodeURIComponent(topic.id)}`}
                    className="flex items-center justify-between w-full"
                  >
                    瀏覽主題
                    <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </section>

      {/* 核心主題牆 */}
      <section className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold flex items-center">
              <Sparkles className="h-5 w-5 mr-2 text-purple-500" />
              核心主題牆
            </h2>
            <p className="text-muted-foreground mt-1">探索 AI 領域的核心技術主題</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {evergreenTopics.map((topic) => (
            <TopicSection
              key={topic.id}
              id={topic.id}
              name={topic.name}
              description={topic.description}
              cardCount={topic.cardCount}
              icon={topic.icon}
              slug={topic.slug} // 添加這行
              className="border-border/60 hover:border-primary/30 hover:shadow-md transition-all"
            />
          ))}
        </div>
      </section>
    </>
  )
}

// 熱門卡片區塊
async function PopularCardsSection() {
  // 獲取用戶認證狀態
  const cookieStore = cookies()
  const supabase = await createServerClient(cookieStore)
  const { user } = await safeGetUser(supabase)
  const userId = user?.id

  // 獲取熱門卡片
  const popularCardsResponse = await getPopularCards({ limit: 2 })
  const popularCards = popularCardsResponse.success ? popularCardsResponse.data : []

  // 使用批量統計服務獲取用戶狀態
  const { enrichWithBatchStats } = await import("@/lib/batch-stats-service")
  const cardsWithStats = await enrichWithBatchStats(popularCards || [], "card", userId)

  // 轉換卡片數據格式
  const leaderCards = cardsWithStats.map((card: any) => ({
    id: card.id,
    contentType: "viewpoint" as const,
    semanticType: card.semantic_type,
    title: card.title,
    content: card.content,
    topics: card.card_topics?.map((ct: any) => ct.topics.name) || [],
    subtopics: card.card_subtopics?.map((cs: any) => cs.subtopics.name) || [],
    author: {
      id: card.profiles?.id || "",
      name: card.profiles?.name || "未知用戶",
      avatar: card.profiles?.avatar || "/mystical-forest-spirit.png",
    },
    contribution_type: card.contribution_type,
    originalAuthor: card.original_author,
    originalSource: card.original_url,
    timestamp: new Date(card.created_at).toLocaleDateString(),
    stats: {
      likes: card.stats?.likes || 0,
      dislikes: card.stats?.dislikes || 0,
      comments: card.stats?.comments || 0,
      bookmarks: card.stats?.bookmarks || 0,
      // 添加用戶狀態
      hasLiked: card.userStates?.liked || false,
      hasDisliked: card.userStates?.disliked || false,
      hasBookmarked: card.userStates?.bookmarked || false,
    },
    variant: "card" as const,
    features: ["like", "comment", "bookmark", "share"] as ("like" | "dislike" | "reply" | "bookmark" | "share")[],
    truncate: true,
  }))

  return (
    <section className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center">
            <Sparkles className="h-5 w-5 mr-2 text-amber-500" />
            精選觀點卡
          </h2>
          <p className="text-muted-foreground mt-1">由編輯精選的高質量觀點和見解</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="gap-1">
            <TrendingUp className="h-4 w-4" />
            熱門
          </Button>
          <Button variant="ghost" size="sm" className="gap-1">
            <Link href="/explore?type=viewpoint&sort=featured" className="flex items-center gap-1">
              查看全部
              <ChevronRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {leaderCards.map((card) => (
          <ContentCard
            key={card.id}
            {...card}
            className="border-border/60 hover:border-primary/30 hover:shadow-md transition-all"
          />
        ))}
      </div>
    </section>
  )
}

// 最新卡片區塊
async function LatestCardsSection() {
  // 獲取用戶認證狀態
  const cookieStore = cookies()
  const supabase = await createServerClient(cookieStore)
  const { user } = await safeGetUser(supabase)
  const userId = user?.id

  // 獲取最新卡片
  const latestCardsResponse = await getLatestCards({ limit: 2 })
  const latestCards = latestCardsResponse.success ? latestCardsResponse.data : []

  // 使用批量統計服務獲取用戶狀態
  const { enrichWithBatchStats } = await import("@/lib/batch-stats-service")
  const cardsWithStats = await enrichWithBatchStats(latestCards || [], "card", userId)

  // 轉換最新卡片數據格式
  const communityCards = cardsWithStats.map((card: any) => ({
    id: card.id,
    contentType: "viewpoint" as const,
    semanticType: card.semantic_type,
    title: card.title,
    content: card.content,
    topics: card.card_topics?.map((ct: any) => ct.topics.name) || [],
    subtopics: card.card_subtopics?.map((cs: any) => cs.subtopics.name) || [],
    author: {
      id: card.profiles?.id || "",
      name: card.profiles?.name || "未知用戶",
      avatar: card.profiles?.avatar || "/mystical-forest-spirit.png",
    },
    contribution_type: card.contribution_type,
    originalAuthor: card.original_author,
    originalSource: card.original_url,
    timestamp: new Date(card.created_at).toLocaleDateString(),
    stats: {
      likes: card.stats?.likes || 0,
      dislikes: card.stats?.dislikes || 0,
      comments: card.stats?.comments || 0,
      bookmarks: card.stats?.bookmarks || 0,
      // 添加用戶狀態
      hasLiked: card.userStates?.liked || false,
      hasDisliked: card.userStates?.disliked || false,
      hasBookmarked: card.userStates?.bookmarked || false,
    },
    variant: "card" as const,
    features: ["like", "comment", "bookmark", "share"] as ("like" | "dislike" | "reply" | "bookmark" | "share")[],
    truncate: true,
  }))

  return (
    <section className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center">
            <Clock className="h-5 w-5 mr-2 text-blue-500" />
            最新觀點卡
          </h2>
          <p className="text-muted-foreground mt-1">社群最新分享的觀點和經驗</p>
        </div>
        <Button variant="ghost" size="sm" className="gap-1">
          <Link href="/explore?type=viewpoint&sort=latest" className="flex items-center gap-1">
            查看全部
            <ChevronRight className="h-4 w-4" />
          </Link>
        </Button>
      </div>

      <div className="space-y-4">
        {communityCards.map((card) => (
          <ContentCard
            key={card.id}
            {...card}
            className="border-border/60 hover:border-primary/30 hover:shadow-md transition-all"
          />
        ))}
      </div>
    </section>
  )
}

// 熱門討論串區塊
async function PopularThreadsSection() {
  // 獲取用戶認證狀態
  const cookieStore = cookies()
  const supabase = await createServerClient(cookieStore)
  const { user } = await safeGetUser(supabase)
  const userId = user?.id

  // 直接使用 Supabase 獲取熱門討論串
  const { data: popularThreads, error } = await supabase
    .from("threads")
    .select(`
      *,
      profiles:author_id (id, name, avatar),
      thread_topics (topics (*)),
      thread_subtopics (subtopics (*))
    `)
    .eq("status", "published")
    .order("created_at", { ascending: false })
    .limit(3)

  if (error) {
    console.error("Error fetching popular threads:", error)
    return <div>無法載入熱門討論串</div>
  }

  // 使用新的批量統計服務，傳入用戶 ID
  const { enrichWithBatchStats } = await import("@/lib/batch-stats-service")
  const threadsWithStats = await enrichWithBatchStats(popularThreads || [], "thread", userId)

  // 轉換討論串數據格式
  const trendingThreads = threadsWithStats.map((thread: any) => ({
    id: thread.id,
    contentType: "thread" as const,
    title: thread.title,
    content: thread.content,
    topics: thread.thread_topics?.map((tt: any) => tt.topics.name) || [],
    subtopics: thread.thread_subtopics?.map((ts: any) => ts.subtopics.name) || [],
    author: {
      id: thread.profiles?.id || "",
      name: thread.profiles?.name || "未知用戶",
      avatar: thread.profiles?.avatar || "/diverse-research-team.png",
    },
    timestamp: new Date(thread.created_at).toLocaleDateString(),
    stats: {
      replies: thread.stats?.replies || 0,
      views: thread.stats?.views || 0,
      likes: thread.stats?.likes || 0,
      bookmarks: thread.stats?.bookmarks || 0,
      // 添加用戶狀態
      hasLiked: thread.userStates?.liked || false,
      hasDisliked: thread.userStates?.disliked || false,
      hasBookmarked: thread.userStates?.bookmarked || false,
    },
    tags: thread.thread_topics?.map((tt: any) => tt.topics.name) || [],
    semanticType: thread.semantic_type || "discussion",
    variant: "card" as const,
    features: ["like", "reply", "bookmark", "share"] as ("like" | "dislike" | "reply" | "bookmark" | "share")[],
    isHot: true,
  }))

  return (
    <section className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center">
            <MessageSquare className="h-5 w-5 mr-2 text-green-500" />
            熱門討論串
          </h2>
          <p className="text-muted-foreground mt-1">社群中最活躍的討論和問答</p>
        </div>
        <Button variant="ghost" size="sm" className="gap-1">
          <Link href="/explore?type=thread&sort=trending" className="flex items-center gap-1">
            查看全部
            <ChevronRight className="h-4 w-4" />
          </Link>
        </Button>
      </div>

      <div className="space-y-4">
        {trendingThreads.map((thread) => (
          <ContentCard
            key={thread.id}
            {...thread}
            className="border-border/60 hover:border-primary/30 hover:shadow-md transition-all"
          />
        ))}
      </div>
    </section>
  )
}

export async function HomeContent() {
  return (
    <div className="space-y-12 w-full max-w-[1400px] mx-auto px-4">
      {/* 英雄區域 */}
      <section className="relative overflow-hidden rounded-xl bg-gradient-to-r from-primary/10 via-primary/5 to-background border">
        <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
        <div className="relative z-10 px-6 py-12 md:py-16 md:px-10 flex flex-col md:flex-row items-center">
          <div className="md:w-3/5 space-y-4">
            <Badge variant="outline" className="bg-background/80 backdrop-blur-sm">
              AI Logora 社群平台
            </Badge>
            <h1 className="text-3xl md:text-4xl font-bold tracking-tight">
              探索 AI 技術的最前沿，
              <br />
              與專業社群共同成長
            </h1>
            <p className="text-muted-foreground text-lg max-w-xl">
              發現最新的 AI 技術觀點、分享實踐經驗、參與深度討論，加入由 AI 愛好者和專業人士組成的知識社群。
            </p>
            <div className="flex flex-nowrap items-center gap-4 pt-2">
              <Button size="lg" className="gap-2">
                <Search className="h-4 w-4" />
                開始探索
              </Button>
              <Button size="lg" variant="outline" className="gap-2">
                <Link href="/submit" className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  分享觀點
                </Link>
              </Button>
            </div>
          </div>
          <div className="md:w-2/5 mt-8 md:mt-0 flex justify-center">
            <div className="relative w-full max-w-sm">
              <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-background/0 rounded-lg"></div>
              <Image
                src="/home.png"
                alt="AI 技術網絡視覺化"
                width={400}
                height={400}
                className="rounded-lg shadow-lg w-full h-auto object-contain"
                priority
              />
            </div>
          </div>
        </div>

        {/* 社區統計 */}
        <div className="bg-background/80 backdrop-blur-sm border-t px-6 py-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold">{communityStats.activeUsers.toLocaleString()}</p>
              <p className="text-sm text-muted-foreground">活躍用戶</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">{communityStats.newPostsToday.toLocaleString()}</p>
              <p className="text-sm text-muted-foreground">今日新增內容</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">{communityStats.totalCards.toLocaleString()}</p>
              <p className="text-sm text-muted-foreground">觀點卡總數</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">{communityStats.totalDiscussions.toLocaleString()}</p>
              <p className="text-sm text-muted-foreground">討論串總數</p>
            </div>
          </div>
        </div>
      </section>

      {/* 使用 Suspense 包裹各個區塊，實現並行資料獲取和流式渲染 */}
      <Suspense fallback={<LoadingCards />}>
        <TopicsSection />
      </Suspense>

      <Suspense fallback={<LoadingCards />}>
        <PopularCardsSection />
      </Suspense>

      <Suspense fallback={<LoadingCards />}>
        <LatestCardsSection />
      </Suspense>

      <Suspense fallback={<LoadingCards />}>
        <PopularThreadsSection />
      </Suspense>

      {/* CTA 區域 */}
      <section className="rounded-xl overflow-hidden">
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary/5"></div>
          <div className="relative z-10 px-6 py-12 text-center space-y-6">
            <h2 className="text-2xl md:text-3xl font-bold">加入 AI Logora 社群</h2>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              投稿或收藏你的第一張觀點卡，開始你的 AI 技術探索之旅。與來自世界各地的 AI 愛好者和專業人士交流，共同成長。
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center pt-2">
              <Button size="lg" className="gap-2">
                <Link href="/submit" className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  投稿觀點卡
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="gap-2">
                <Link href="/library" className="flex items-center gap-2">
                  <BookMarked className="h-4 w-4" />
                  建立收藏牆
                </Link>
              </Button>
              <Button size="lg" className="gap-2 bg-[#a0b8c5] hover:bg-[#8ca7b6] text-white border-none">
                <Link href="/explore" className="flex items-center gap-2">
                  <Search className="h-4 w-4" />
                  自由探索
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
