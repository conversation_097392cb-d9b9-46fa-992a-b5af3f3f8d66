"use client"

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useState } from "react"

interface OptimizedAvatarProps {
  src?: string | null
  alt?: string
  fallback?: string
  className?: string
}

export function OptimizedAvatar({ src, alt, fallback, className }: OptimizedAvatarProps) {
  const [imageError, setImageError] = useState(false)

  // 生成 fallback 文字
  const getFallbackText = () => {
    if (fallback) {
      return fallback.substring(0, 2).toUpperCase()
    }
    if (alt) {
      return alt.substring(0, 2).toUpperCase()
    }
    return "?"
  }

  return (
    <Avatar className={className}>
      {src && !imageError ? (
        <AvatarImage src={src || "/placeholder.svg"} alt={alt} onError={() => setImageError(true)} />
      ) : null}
      <AvatarFallback className="bg-primary/10 text-primary">{getFallbackText()}</AvatarFallback>
    </Avatar>
  )
}
