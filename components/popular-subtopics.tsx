"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"

interface Subtopic {
  id: string
  name: string
  slug: string
  topic_id: string
  description: string
  card_count: number
  topics: {
    name: string
    slug: string
  }
}

export function PopularSubtopics() {
  const [subtopics, setSubtopics] = useState<Subtopic[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchPopularSubtopics = async () => {
      try {
        setLoading(true)
        const response = await fetch("/api/subtopics/popular?limit=10")

        if (!response.ok) {
          throw new Error("Failed to fetch popular subtopics")
        }

        const data = await response.json()
        setSubtopics(data)
      } catch (error) {
        console.error("Error fetching popular subtopics:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchPopularSubtopics()
  }, [])

  if (loading) {
    return (
      <div className="space-y-2">
        <h3 className="text-lg font-semibold mb-3">熱門標籤</h3>
        <div className="flex flex-wrap gap-2">
          {Array.from({ length: 10 }).map((_, i) => (
            <Skeleton key={i} className="h-6 w-16 rounded-full" />
          ))}
        </div>
      </div>
    )
  }

  if (subtopics.length === 0) {
    return (
      <div className="space-y-2">
        <h3 className="text-lg font-semibold mb-3">熱門標籤</h3>
        <p className="text-sm text-muted-foreground">暫無熱門標籤</p>
      </div>
    )
  }

  return (
    <div className="space-y-2">
      <h3 className="text-lg font-semibold mb-3">熱門標籤</h3>
      <div className="flex flex-wrap gap-2">
        {subtopics.map((subtopic) => (
          <Link key={subtopic.id} href={`/topic/${subtopic.topics.slug}/${subtopic.slug}`}>
            <Badge variant="secondary" className="cursor-pointer hover:bg-secondary/80">
              {subtopic.name}
            </Badge>
          </Link>
        ))}
      </div>
    </div>
  )
}
