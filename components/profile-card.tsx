"use client"

import type React from "react"

import { useState } from "react"
import type { Profile, ProfileUpdatePayload } from "@/lib/types"
import { Card, CardContent, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

interface ProfileCardProps {
  profile: Profile
  isCurrentUser: boolean
  onUpdate?: (profile: ProfileUpdatePayload) => Promise<void>
}

export function ProfileCard({ profile, isCurrentUser, onUpdate }: ProfileCardProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [name, setName] = useState(profile.name || "")
  const [bio, setBio] = useState(profile.bio || "")
  const [avatar, setAvatar] = useState(profile.avatar || "") // 修改為 avatar 而不是 avatar_url
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!onUpdate) return

    setIsLoading(true)
    try {
      await onUpdate({ name, bio, avatar }) // 修改為 avatar 而不是 avatar_url
      setIsEditing(false)
    } catch (error) {
      console.error("Failed to update profile:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase()
      .substring(0, 2)
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="flex flex-col items-center">
        <Avatar className="h-24 w-24 mb-4">
          <AvatarImage src={profile.avatar || ""} alt={profile.name || "User"} />{" "}
          {/* 修改為 avatar 而不是 avatar_url */}
          <AvatarFallback>{profile.name ? getInitials(profile.name) : "U"}</AvatarFallback>
        </Avatar>
        {!isEditing ? <CardTitle>{profile.name || "Anonymous User"}</CardTitle> : null}
      </CardHeader>

      <CardContent>
        {!isEditing ? (
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Bio</h3>
              <p className="mt-1">{profile.bio || "No bio provided"}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Member since</h3>
              <p className="mt-1">{new Date(profile.created_at).toLocaleDateString()}</p>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium mb-1">
                Name
              </label>
              <Input id="name" value={name} onChange={(e) => setName(e.target.value)} placeholder="Your name" />
            </div>

            <div>
              <label htmlFor="avatar" className="block text-sm font-medium mb-1">
                Avatar URL
              </label>
              <Input
                id="avatar"
                value={avatar}
                onChange={(e) => setAvatar(e.target.value)}
                placeholder="https://example.com/avatar.jpg"
              />
            </div>

            <div>
              <label htmlFor="bio" className="block text-sm font-medium mb-1">
                Bio
              </label>
              <Textarea
                id="bio"
                value={bio}
                onChange={(e) => setBio(e.target.value)}
                placeholder="Tell us about yourself"
                rows={4}
              />
            </div>
          </form>
        )}
      </CardContent>

      {isCurrentUser && (
        <CardFooter className="flex justify-end">
          {!isEditing ? (
            <Button onClick={() => setIsEditing(true)}>Edit Profile</Button>
          ) : (
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => setIsEditing(false)}>
                Cancel
              </Button>
              <Button onClick={handleSubmit} disabled={isLoading}>
                {isLoading ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          )}
        </CardFooter>
      )}
    </Card>
  )
}
