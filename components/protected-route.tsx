"use client"

import type React from "react"

import { useAuth } from "@/contexts/auth-context"
import { Loader2 } from "lucide-react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

interface ProtectedRouteProps {
  children: React.ReactNode
  redirectTo?: string
}

export function ProtectedRoute({ children, redirectTo = "/auth/login" }: ProtectedRouteProps) {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // 如果已確定未登入，立即重定向
    if (!isLoading && !isAuthenticated) {
      router.push(redirectTo)
    }
  }, [isLoading, isAuthenticated, router, redirectTo])

  // 添加一個立即執行的檢查
  useEffect(() => {
    // 初始檢查，如果已知未登入狀態，立即重定向
    if (!isAuthenticated && !isLoading) {
      router.push(redirectTo)
    }
  }, [])

  // 如果未登入且不在加載中，返回 null，不顯示任何內容
  if (!isAuthenticated && !isLoading) {
    return null
  }

  // 如果正在加載，顯示加載指示器
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[80vh]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  // 已登入，顯示子組件
  return <>{children}</>
}
