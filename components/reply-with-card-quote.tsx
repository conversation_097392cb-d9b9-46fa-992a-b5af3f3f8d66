"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { CardQuote } from "@/components/card-quote"
import { CardSelector } from "@/components/card-selector"
import { CardMention } from "@/components/card-mention"
import { X } from "lucide-react"

interface ReplyWithCardQuoteProps {
  onSubmit: (content: string, quotedCardId?: number) => void
  onCancel?: () => void
  autoFocus?: boolean
  placeholder?: string
}

export function ReplyWithCardQuote({
  onSubmit,
  onCancel,
  autoFocus = false,
  placeholder = "寫下你的回覆...",
}: ReplyWithCardQuoteProps) {
  const [content, setContent] = useState("")
  const [isCardSelectorOpen, setIsCardSelectorOpen] = useState(false)
  const [quotedCard, setQuotedCard] = useState<any | null>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // 自動聚焦
  useEffect(() => {
    if (autoFocus && textareaRef.current) {
      textareaRef.current.focus()
    }
  }, [autoFocus])

  // 處理提交
  const handleSubmit = () => {
    if (content.trim()) {
      onSubmit(content, quotedCard?.id)
      setContent("")
      setQuotedCard(null)
    }
  }

  // 處理取消
  const handleCancel = () => {
    setContent("")
    setQuotedCard(null)
    if (onCancel) onCancel()
  }

  // 處理卡片選擇
  const handleSelectCard = (card: any) => {
    setQuotedCard(card)
    setIsCardSelectorOpen(false)

    // 選擇卡片後聚焦文本框
    if (textareaRef.current) {
      textareaRef.current.focus()
    }
  }

  // 處理移除引用卡片
  const handleRemoveQuotedCard = () => {
    setQuotedCard(null)
  }

  return (
    <div className="space-y-4">
      {/* 引用的卡片 */}
      {quotedCard && (
        <div className="relative">
          <CardQuote card={quotedCard} />
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 h-6 w-6 rounded-full bg-background/80 hover:bg-background"
            onClick={handleRemoveQuotedCard}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">移除引用卡片</span>
          </Button>
        </div>
      )}

      {/* 回覆輸入區 */}
      <div ref={containerRef} className="relative">
        <Textarea
          ref={textareaRef}
          value={content}
          onChange={(e) => setContent(e.target.value)}
          placeholder={placeholder}
          className="min-h-[100px] resize-y"
        />

        {/* 卡片提及選擇器 - 使用相對定位 */}
        <CardMention textareaRef={textareaRef} onSelectCard={handleSelectCard} />
      </div>

      {/* ��鈕區 */}
      <div className="flex items-center justify-between">
        <Button type="button" variant="outline" onClick={() => setIsCardSelectorOpen(true)}>
          引用卡片
        </Button>
        <div className="flex items-center space-x-2">
          {onCancel && (
            <Button type="button" variant="ghost" onClick={handleCancel}>
              取消
            </Button>
          )}
          <Button type="button" onClick={handleSubmit} disabled={!content.trim()}>
            發送
          </Button>
        </div>
      </div>

      {/* 卡片選擇器 */}
      {isCardSelectorOpen && <CardSelector onSelect={handleSelectCard} onClose={() => setIsCardSelectorOpen(false)} />}
    </div>
  )
}
