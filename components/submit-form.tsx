"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import {
  AlertTriangle,
  BookOpen,
  FlaskConical,
  HelpCircle,
  LightbulbIcon,
  Wrench,
  X,
  Zap,
  MessageSquare,
  Loader2,
} from "lucide-react"
import { ContentCard } from "@/components/content-card"
import { cn } from "@/lib/utils"
import { useToast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"
import { createBrowserClient } from "@/lib/supabase/client"

// 在文件頂部添加缺少的 import
import {
  Dialog,
  DialogContent,
  <PERSON>alogDescription,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// First, add the imports for our new components at the top of the file
import { TiptapEditor, htmlToPlainText } from "@/components/tiptap-editor"
import { getSemanticTypesByContentType } from "@/lib/constants"

// 更新 interface 添加 discussion 類型
interface SubmitFormProps {
  defaultTopic?: string
  defaultTag?: string
  contentType: "original" | "others" | "thread"
  onBack: () => void
  draftId?: string // 新增草稿 ID 參數
}

// 添加型別定義
interface Topic {
  id: string
  name: string
  description: string
  slug: string
  subtopics?: Subtopic[]
}

interface Subtopic {
  id: string
  name: string
  description: string
  slug: string
}



// 在 SubmitForm 組件中添加對 discussion 類型的處理
export function SubmitForm({ defaultTopic, defaultTag, contentType, onBack, draftId }: SubmitFormProps) {
  const [activeTab, setActiveTab] = useState<string>("write")
  const [selectedType, setSelectedType] = useState<string>(contentType === "thread" ? "question" : "insight")
  const [selectedTopic, setSelectedTopic] = useState<string>(defaultTopic || "")
  const [tags, setTags] = useState<string[]>(defaultTag ? [defaultTag] : [])
  const [tagInput, setTagInput] = useState<string>("")
  const [title, setTitle] = useState<string>("")
  const [content, setContent] = useState<string>("")
  const [source, setSource] = useState<string>("")
  const [author, setAuthor] = useState<string>(contentType === "others" ? "" : "")

  // 添加 Supabase 相關狀態
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoadingTopics, setIsLoadingTopics] = useState(false)
  const [isLoadingDraft, setIsLoadingDraft] = useState(false)
  const [topics, setTopics] = useState<Topic[]>([])
  const [currentUser, setCurrentUser] = useState<any>(null)
  const [editingDraftId, setEditingDraftId] = useState<string | null>(draftId || null)

  // 在 SubmitForm 組件中添加表單驗狀態
  const [formErrors, setFormErrors] = useState<{
    title?: string
    content?: string
    topic?: string
    source?: string
    author?: string
  }>({})

  // 在 SubmitForm 組件中添加 toast
  const { toast } = useToast()
  const router = useRouter()
  const supabase = createBrowserClient()

  // 載入主題數據和草稿
  useEffect(() => {
    loadTopics()
    loadCurrentUser()

    // 如果有 draftId，載入草稿
    if (draftId) {
      loadDraftFromServer(draftId)
    }
  }, [draftId])

  const loadCurrentUser = async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()
      if (error) {
        console.error("載入用戶資訊錯誤:", error)
        return
      }

      if (user) {
        // 獲取用戶 profile
        const { data: profile } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", user.id)
          .single()

        setCurrentUser(profile || { name: user.email?.split("@")[0] || "用戶" })
        if (contentType !== "others") {
          setAuthor(profile?.name || user.email?.split("@")[0] || "用戶")
        }
      }
    } catch (error) {
      console.error("載入用戶資訊錯誤:", error)
    }
  }

  const loadTopics = async () => {
    setIsLoadingTopics(true)
    try {
      const response = await fetch("/api/topics?includeSubtopics=true")
      const result = await response.json()

      if (result.success) {
        setTopics(result.data)
      } else {
        toast({
          title: "載入主題失敗",
          description: result.error || "無法載入主題列表",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("載入主題錯誤:", error)
      toast({
        title: "載入主題失敗",
        description: "網路連線錯誤",
        variant: "destructive",
      })
    } finally {
      setIsLoadingTopics(false)
    }
  }

  // 從伺服器載入草稿
  const loadDraftFromServer = async (id: string) => {
    setIsLoadingDraft(true)
    try {
      const type = contentType === "thread" ? "thread" : "card"
      const response = await fetch(`/api/my-content/drafts?type=${type}&id=${id}`)
      const result = await response.json()

      if (result.success) {
        const draft = result.data
        setTitle(draft.title || "")
        setContent(draft.content || "")
        setSelectedType(draft.semanticType || (contentType === "thread" ? "question" : "insight"))
        setSelectedTopic(draft.topicIds?.[0] || "")
        setTags(draft.tags || [])

        if (contentType === "others") {
          setAuthor(draft.originalAuthor || "")
          setSource(draft.originalUrl || "")
        }

        toast({
          title: "草稿已載入",
          description: "您的草稿內容已成功載入",
          variant: "default",
        })
      } else {
        toast({
          title: "載入草稿失敗",
          description: result.error || "無法載入草稿內容",
          variant: "destructive",
        })
        // 如果載入失敗，清除 URL 中的 draft 參數
        router.replace("/submit?type=" + (contentType === "thread" ? "thread" : contentType))
      }
    } catch (error) {
      console.error("載入草稿錯誤:", error)
      toast({
        title: "載入草稿失敗",
        description: "網路連線錯誤",
        variant: "destructive",
      })
    } finally {
      setIsLoadingDraft(false)
    }
  }

  // 處理標籤添加
  const handleAddTag = () => {
    if (tagInput && !tags.includes(tagInput)) {
      setTags([...tags, tagInput])
      setTagInput("")
    }
  }

  // 處理標籤刪除
  const handleRemoveTag = (tag: string) => {
    setTags(tags.filter((t) => t !== tag))
  }

  // 處理標籤輸入按鍵
  const handleTagKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault()
      handleAddTag()
    }
  }

  // 在 SubmitForm 組件中添加草稿儲存相關狀態和函數
  const [isDraftSaved, setIsDraftSaved] = useState(false)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)

  // 添加自動儲存草稿的函數
  const saveDraft = () => {
    // 檢查是否有足夠的內容值得保存
    if (!title && !content && !selectedTopic && tags.length === 0) {
      return
    }

    const draftData = {
      type: selectedType,
      topic: selectedTopic,
      tags,
      title,
      content,
      source,
      author,
      contentType,
      lastSaved: new Date().toISOString(),
    }

    // 儲存到 localStorage
    localStorage.setItem("viewpoint-card-draft", JSON.stringify(draftData))
    setIsDraftSaved(true)

    // 顯示儲存成功訊息
    toast({
      title: "草稿已儲存",
      description: "您的草稿已自動儲存到本地",
      variant: "default",
    })
  }

  // 添加載入草稿的函數
  const loadDraft = () => {
    const savedDraft = localStorage.getItem("viewpoint-card-draft")
    if (savedDraft) {
      try {
        const draftData = JSON.parse(savedDraft)
        setSelectedType(draftData.type || (contentType === "thread" ? "question" : "insight"))
        setSelectedTopic(draftData.topic || "")
        setTags(draftData.tags || [])
        setTitle(draftData.title || "")
        setContent(draftData.content || "")
        setSource(draftData.source || "")
        setAuthor(draftData.author || (contentType === "others" ? "" : ""))

        toast({
          title: "草稿已載入",
          description: `上次儲存時間: ${new Date(draftData.lastSaved).toLocaleString()}`,
          variant: "default",
        })

        setIsDraftSaved(true)
      } catch (error) {
        console.error("Error loading draft:", error)
      }
    }
  }

  // 添加清除草稿的函數
  const clearDraft = () => {
    localStorage.removeItem("viewpoint-card-draft")
    setIsDraftSaved(false)

    // 重置表單
    setSelectedType(contentType === "thread" ? "question" : "insight")
    setSelectedTopic(defaultTopic || "")
    setTags(defaultTag ? [defaultTag] : [])
    setTagInput("")
    setTitle("")
    setContent("")
    setSource("")
    setAuthor(contentType === "others" ? "" : "")

    toast({
      title: "草稿已清除",
      description: "表單已重置",
      variant: "default",
    })
  }

  // 在 useEffect 中檢查是否有草稿
  useEffect(() => {
    const savedDraft = localStorage.getItem("viewpoint-card-draft")
    if (savedDraft) {
      setIsDraftSaved(true)
    }
  }, [])

  // 修改 handleSubmit 函數以實際連動 Supabase
  const handleSubmit = async (e: React.FormEvent, isDraft = false) => {
    e.preventDefault()

    if (isSubmitting) return

    // 重置錯誤
    setFormErrors({})

    // 表單驗證 - 草稿模式下只需要標題
    const errors: any = {}
    if (!title.trim()) errors.title = "請輸入標題"

    // 非草稿模式需要完整驗證
    if (!isDraft) {
      if (!content.trim()) errors.content = "請輸入內容"
      if (!selectedTopic) errors.topic = "請選擇主題"
      if (contentType === "others" && !source.trim()) errors.source = "請輸入連結網址"
      if (contentType === "others" && !author.trim()) errors.author = "請輸入原作者"
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors)
      toast({
        title: "表單資料不完整",
        description: "請檢查並填寫所有必要欄位",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // 準備提交資料
      const submitData: any = {
        title: title.trim(),
        content: content.trim(),
        semanticType: selectedType,
        contributionType: contentType === "others" ? "others" : "original",
        originalAuthor: contentType === "others" ? author.trim() : undefined,
        originalUrl: contentType === "others" ? source.trim() : undefined,
        topicIds: selectedTopic ? [selectedTopic] : [],
        subtopicIds: [], // 這裡可以根據 tags 來設定 subtopicIds，但需要先建立 tags 與 subtopics 的對應關係
        status: isDraft ? "draft" : (contentType === "thread" ? "published" : "pending"), // 修復狀態設定
      }

      // 如果是編輯草稿，添加 ID 到請求體
      if (editingDraftId) {
        submitData.id = parseInt(editingDraftId)
      }

      // 根據內容類型選擇 API 端點
      const apiEndpoint = contentType === "thread" ? "/api/threads" : "/api/cards"

      // 如果是編輯草稿，使用 PUT 方法，否則使用 POST 方法
      const method = editingDraftId ? "PUT" : "POST"

      const response = await fetch(apiEndpoint, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submitData),
      })

      const result = await response.json()

      if (result.success) {
        // 提交成功
        const successMessage = isDraft
          ? "草稿已儲存"
          : (contentType === "thread" ? "討論已發起" : "提交成功")

        const successDescription = isDraft
          ? "您的草稿已成功儲存，可在「我的發表」中查看"
          : (result.message || (contentType === "thread" ? "您的討論已成功發起" : "您的觀點卡已成功提交"))

        toast({
          title: successMessage,
          description: successDescription,
          variant: "default",
        })

        // 清除草稿
        localStorage.removeItem("viewpoint-card-draft")
        setIsDraftSaved(false)

        // 導航到相應頁面
        if (isDraft) {
          router.push("/my-posts?tab=drafts")
        } else if (contentType === "thread") {
          router.push("/my-posts?tab=published&subTab=threads")
        } else {
          router.push("/my-posts?tab=pending")
        }
      } else {
        // 提交失敗
        toast({
          title: "提交失敗",
          description: result.error || "提交時發生錯誤，請稍後再試",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("提交錯誤:", error)
      toast({
        title: "提交失敗",
        description: "網路連線錯誤，請檢查網路連線後再試",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // 預覽卡片數據
  const previewCard = contentType === "thread" ? {
    id: -1, // 使用負數表示預覽卡片，避免與真實 ID 衝突
    contentType: "thread" as const,
    semanticType: selectedType as any,
    title: title || "討論標題",
    content: content || "討論內容將顯示在這裡...",
    topics: selectedTopic ? [topics.find(t => t.id === selectedTopic)?.name || "未知主題"] : ["LLM"],
    tags: tags.length > 0 ? tags : ["標籤1", "標籤2"],
    author: {
      id: "user1",
      name: currentUser?.name || "用戶",
    },
    timestamp: "剛剛",
    stats: {
      likes: 0,
      dislikes: 0,
      comments: 0,
      bookmarks: 0,
    },
    variant: "card" as const,
    features: [] as ("like" | "dislike" | "reply" | "bookmark" | "share")[], // 預覽時不顯示互動功能
    isPreview: true,
    isInteractive: false, // 禁用互動功能
    className: "h-auto max-h-none overflow-visible",
  } : {
    id: -1, // 使用負數表示預覽卡片，避免與真實 ID 衝突
    contentType: "viewpoint" as const,
    semanticType: selectedType as any,
    title: title || "觀點卡標題",
    content: content || "觀點卡內容將顯示在這裡...",
    topics: selectedTopic ? [topics.find(t => t.id === selectedTopic)?.name || "未知主題"] : ["LLM"],
    // 觀點卡使用 subtopics 來顯示子主題標籤，而不是 tags
    subtopics: tags.length > 0 ? tags : ["標籤1", "標籤2"],
    author: {
      id: "user1",
      name: currentUser?.name || "用戶",
    },
    contribution_type: (contentType === "others" ? "others" : "original") as "others" | "original",
    originalSource: contentType === "others" ? source : undefined,
    originalAuthor: contentType === "others" ? author : undefined,
    timestamp: "剛剛",
    stats: {
      likes: 0,
      dislikes: 0,
      comments: 0,
      bookmarks: 0,
    },
    variant: "card" as const,
    features: [] as ("like" | "dislike" | "reply" | "bookmark" | "share")[], // 預覽時不顯示互動功能
    isPreview: true,
    isInteractive: false, // 禁用互動功能
    className: "h-auto max-h-none overflow-visible",
  }

  // 在 SubmitForm 組件中添加標籤建議相關狀態和函數
  const [suggestedTags, setSuggestedTags] = useState<string[]>([])

  // 更新 useEffect 以根據選定的主題更新建議標籤
  useEffect(() => {
    const selectedTopicData = topics.find(topic => topic.id === selectedTopic)
    if (selectedTopicData && selectedTopicData.subtopics) {
      setSuggestedTags(selectedTopicData.subtopics.map(subtopic => subtopic.name))
    } else {
      setSuggestedTags([])
    }
  }, [selectedTopic, topics])

  // 添加自動預覽切換功能
  // 在 useEffect 中監聽表單變化，自動更新預覽
  useEffect(() => {
    // 當表單有足夠的內容時，自動更新預覽
    if (title && content && selectedType && selectedTopic) {
      // 如果用戶正在寫作標籤，不要自動切換
      if (activeTab === "write") {
        // 可以在這裡添加一個提示，告訴用戶可以切換到預覽標籤
      }
    }
  }, [title, content, selectedType, selectedTopic, activeTab])

  // 獲取當前內容類型對應的語義類型配置
  const getSemanticTypeConfig = () => {
    return getSemanticTypesByContentType(contentType === "thread" ? "thread" : "viewpoint")
  }

  // 添加一個函數來截斷內容，用於確認對話框
  const truncateContent = (htmlContent: string, maxLength = 300) => {
    const plainText = htmlToPlainText(htmlContent)
    if (plainText.length <= maxLength) return htmlContent

    // 如果是純文本，直接截斷
    if (htmlContent === plainText) {
      return plainText.substring(0, maxLength) + "..."
    }

    // 如果是HTML，我們需要更複雜的處理
    // 這裡採用簡單方案：只顯示前300個字符的純文本，並添加"..."
    return `<p>${plainText.substring(0, maxLength)}...</p><p class="text-xs text-muted-foreground mt-2">(內容已截斷，發布後可查看完整內容)</p>`
  }

  // 如果正在載入草稿，顯示載入狀態
  if (isLoadingDraft) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin mr-2" />
        <span>載入草稿中...</span>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* 左側 - 撰寫區 */}
      <div className="lg:col-span-2">
        <Card>
          <CardHeader className="pb-2 border-b flex flex-row items-start justify-between">
            <div>
              <CardTitle className="text-lg">{contentType === "thread" ? "發起討論" : "撰寫觀點卡"}</CardTitle>
              <CardDescription>
                {contentType === "original"
                  ? "分享你的觀點、經驗或實作案例，幫助社群成長"
                  : contentType === "others"
                    ? "整理分享他人的優質內容，請務必註明來源"
                    : "提出問題或議題，邀請社群成員一起討論和交流想法"}
              </CardDescription>
            </div>
            <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground" onClick={onBack}>
              <X className="h-4 w-4 mr-1" />
              重新選擇
            </Button>
          </CardHeader>
          <CardContent className="pt-4 space-y-4">
            {isDraftSaved && (
              <div className="mb-4 p-3 bg-muted rounded-md flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">發現未完成的草稿</p>
                  <p className="text-xs text-muted-foreground">您可以載入上次儲存的草稿繼續編輯</p>
                </div>
                <Button variant="outline" size="sm" onClick={loadDraft}>
                  載入草稿
                </Button>
              </div>
            )}
            <Tabs defaultValue="write" value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="w-full grid grid-cols-2 mb-2">
                <TabsTrigger value="write" className="rounded-md">
                  撰寫
                </TabsTrigger>
                <TabsTrigger value="preview" className="rounded-md">
                  預覽
                </TabsTrigger>
              </TabsList>

              <TabsContent value="write" className="mt-4 space-y-6">
                <form onSubmit={(e) => handleSubmit(e, false)} className="space-y-6">
                  {/* 連結網址 - 僅對於他人觀點 */}
                  {contentType === "others" && (
                    <div className="space-y-2">
                      <Label htmlFor="source" className="text-sm font-medium">
                        連結網址
                      </Label>
                      <Input
                        id="source"
                        placeholder="https://example.com"
                        value={source}
                        onChange={(e) => setSource(e.target.value)}
                        className={cn(
                          "border-border/40 focus-visible:ring-1",
                          formErrors.source && "border-destructive",
                        )}
                      />
                      {formErrors.source && <p className="text-xs text-destructive">{formErrors.source}</p>}
                    </div>
                  )}

                  {/* 作者 - 僅對於他人觀點 */}
                  {contentType === "others" && (
                    <div className="space-y-2">
                      <Label htmlFor="author" className="text-sm font-medium">
                        原作者
                      </Label>
                      <Input
                        id="author"
                        placeholder="原作者名稱"
                        value={author}
                        onChange={(e) => setAuthor(e.target.value)}
                        className={cn(
                          "border-border/40 focus-visible:ring-1",
                          formErrors.author && "border-destructive",
                        )}
                        required
                      />
                      {formErrors.author && <p className="text-xs text-destructive">{formErrors.author}</p>}
                    </div>
                  )}

                  {/* 具體主題 */}
                  <div className="space-y-2">
                    <Label htmlFor="topic" className="text-sm font-medium">
                      核心主題
                    </Label>
                    <Select value={selectedTopic} onValueChange={setSelectedTopic}>
                      <SelectTrigger
                        className={cn(
                          "border-border/40 focus-visible:ring-1 bg-background",
                          formErrors.topic && "border-destructive",
                        )}
                      >
                        <SelectValue placeholder="選擇主題" />
                      </SelectTrigger>
                      <SelectContent>
                        <div className="space-y-1 p-2">
                          {topics.map((topic) => (
                            <SelectItem key={topic.id} value={topic.id}>
                              {topic.name}
                            </SelectItem>
                          ))}
                        </div>
                      </SelectContent>
                    </Select>
                    {formErrors.topic && <p className="text-xs text-destructive">{formErrors.topic}</p>}

                    {/* 熱門主題快速選擇 */}
                    <div className="mt-2">
                      <p className="text-xs text-muted-foreground mb-1">熱門主題:</p>
                      <div className="flex flex-wrap gap-2">
                        {topics.map((topic) => (
                          <Badge
                            key={topic.id}
                            variant="outline"
                            className={cn(
                              "cursor-pointer hover:bg-secondary",
                              selectedTopic === topic.id && "bg-secondary",
                            )}
                            onClick={() => setSelectedTopic(topic.id)}
                          >
                            {topic.name}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* 類型 */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">類型</Label>
                    <div className="grid grid-cols-3 gap-2">
                      {Object.entries(getSemanticTypeConfig()).map(([type, config]) => (
                        <Button
                          key={type}
                          variant={selectedType === type ? "default" : "outline"}
                          className={cn(
                            "justify-start h-9 px-3 text-sm",
                            selectedType === type ? "bg-primary/10 text-primary border-primary/30" : "border-border/40",
                          )}
                          onClick={() => setSelectedType(type)}
                          type="button"
                        >
                          {config.icon}
                          <span className="ml-2">{config.label}</span>
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* 標題 */}
                  <div className="space-y-2">
                    <Label htmlFor="title" className="text-sm font-medium">
                      標題
                    </Label>
                    <Input
                      id="title"
                      placeholder={
                        contentType === "thread" ? "輸入討論標題（40字以內）" : "輸入觀點卡標題（40字以內）"
                      }
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                      required
                      className={cn("border-border/40 focus-visible:ring-1", formErrors.title && "border-destructive")}
                    />
                    {formErrors.title && <p className="text-xs text-destructive">{formErrors.title}</p>}
                    <div className="text-xs text-muted-foreground text-right">{title.length}/40</div>
                  </div>

                  {/* 內容 */}
                  <div className="space-y-2">
                    <Label htmlFor="content" className="text-sm font-medium">
                      內容
                    </Label>
                    {/* Replace the Textarea component in the content section with our TiptapEditor */}
                    {/* Find the "內容" section and replace the Textarea with: */}
                    <TiptapEditor
                      content={content}
                      onChange={setContent}
                      placeholder={
                        contentType === "thread" ? "描述你想討論的問題或議題..." : "分享你的觀點和見解..."
                      }
                      className={formErrors.content ? "border-destructive" : ""}
                      error={!!formErrors.content}
                      minHeight="200px"
                    />
                    {formErrors.content && <p className="text-xs text-destructive">{formErrors.content}</p>}
                    <div className="text-xs text-muted-foreground text-right">{htmlToPlainText(content).length} 字</div>
                  </div>

                  {/* 標籤 */}
                  <div className="space-y-2">
                    <Label htmlFor="tags" className="text-sm font-medium">
                      標籤
                    </Label>
                    <div className="flex items-center space-x-2">
                      <Input
                        id="tags"
                        placeholder="輸入標籤，按 Enter 添加"
                        value={tagInput}
                        onChange={(e) => setTagInput(e.target.value)}
                        onKeyDown={handleTagKeyDown}
                        className="border-border/40 focus-visible:ring-1"
                      />
                      <Button
                        type="button"
                        onClick={handleAddTag}
                        disabled={!tagInput}
                        variant="outline"
                        className="border-border/40"
                      >
                        添加
                      </Button>
                    </div>
                    {tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mt-2">
                        {tags.map((tag) => (
                          <Badge key={tag} variant="secondary" className="flex items-center gap-1 bg-secondary/50">
                            {tag}
                            <button
                              type="button"
                              onClick={() => handleRemoveTag(tag)}
                              className="ml-1 hover:text-destructive"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </Badge>
                        ))}
                      </div>
                    )}

                    {/* 建議標籤 */}
                    {suggestedTags.length > 0 && (
                      <div className="mt-2">
                        <p className="text-xs text-muted-foreground mb-1">建議標籤:</p>
                        <div className="flex flex-wrap gap-2">
                          {suggestedTags.map((tag) => (
                            <Badge
                              key={tag}
                              variant="outline"
                              className={cn("cursor-pointer hover:bg-secondary", tags.includes(tag) && "bg-secondary")}
                              onClick={() => {
                                if (!tags.includes(tag)) {
                                  setTags([...tags, tag])
                                } else {
                                  handleRemoveTag(tag)
                                }
                              }}
                            >
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </form>
                {/* 添加預覽提示 */}
                {activeTab === "write" && title && content && selectedType && selectedTopic && (
                  <div className="mt-4 p-2 bg-muted rounded-md text-center">
                    <p className="text-sm text-muted-foreground">
                      內容已準備好，可以
                      <Button
                        variant="link"
                        className="p-0 h-auto text-primary"
                        onClick={() => setActiveTab("preview")}
                      >
                        切換到預覽
                      </Button>
                      查看效果
                    </p>
                  </div>
                )}
              </TabsContent>
              <TabsContent value="preview" className="mt-4">
                <div className="border rounded-md p-4 overflow-visible">
                  <ContentCard {...previewCard} variant="card" className="h-auto max-h-none overflow-visible" />
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter className="flex justify-between border-t pt-4">
            <div className="flex gap-2">
              {/* 移除本地儲存的草稿按鈕，只保留 API 草稿按鈕 */}
              <Button
                variant="outline"
                type="button"
                className="border-border/40"
                onClick={(e) => handleSubmit(e, true)}
                disabled={!title.trim() || isSubmitting}
              >
                {isSubmitting ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                儲存為草稿
              </Button>
            </div>
            <Button
              type="button"
              onClick={(e) => {
                // 先驗證表單
                const errors: {
                  title?: string
                  content?: string
                  topic?: string
                  source?: string
                  author?: string
                } = {}

                if (!title.trim()) {
                  errors.title = "請輸入標題"
                } else if (title.length > 40) {
                  errors.title = "標題不能超過40個字"
                }

                if (!content.trim()) {
                  errors.content = "請輸入內容"
                } else if (htmlToPlainText(content).length < 50) {
                  errors.content = "內容至少需要50個字"
                }

                if (!selectedTopic) {
                  errors.topic = "請選擇主題"
                }

                if (contentType === "others" && !source.trim()) {
                  errors.source = "請輸入原始來源"
                }

                if (contentType === "others" && !author.trim()) {
                  errors.author = "請輸入原作者名稱"
                }

                // 如果有錯誤，顯示錯誤並返回
                if (Object.keys(errors).length > 0) {
                  setFormErrors(errors)
                  setActiveTab("write") // 切換到寫作標籤顯示錯誤
                  return
                }

                // 如果驗證通過，顯示確認對話框
                setShowConfirmDialog(true)
              }}
              className="bg-primary hover:bg-primary/90"
              disabled={isSubmitting}
            >
              {isSubmitting ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              {contentType === "thread" ? "發起討論" : "發布觀點卡"}
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* 右側 - 指南 */}
      <div className="lg:col-span-1">
        {/* 投稿指南 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">{contentType === "thread" ? "討論指南" : "投稿指南"}</CardTitle>
            <CardDescription className="!mt-0">
              {contentType === "thread" ? "發起高質量討論的建議" : "撰寫高質量觀點卡的建議"}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 text-sm text-muted-foreground">
            {contentType === "thread" ? (
              <>
                <div>
                  <h3 className="font-medium mb-1 text-foreground/80">什麼是好的討論？</h3>
                  <ul className="list-disc pl-5 space-y-1">
                    <li>提出明確、具體的問題或議題</li>
                    <li>提供足夠的背景資訊和上下文</li>
                    <li>鼓勵不同觀點的交流</li>
                    <li>使用中立、尊重的語言</li>
                  </ul>
                </div>

                <div>
                  <h3 className="font-medium mb-1 text-foreground/80">類型說明</h3>
                  <ul className="space-y-1">
                    {Object.entries(getSemanticTypeConfig())
                      .map(([type, config]) => (
                        <li key={type} className="flex items-start">
                          <span className="mr-2">{config.emoji}</span>
                          <div>
                            <span className="font-medium text-foreground/70">{config.label}:</span> {config.description}
                          </div>
                        </li>
                      ))}
                  </ul>
                </div>

                <div>
                  <h3 className="font-medium mb-1 text-foreground/80">格式建議</h3>
                  <ul className="list-disc pl-5 space-y-1">
                    <li>標題：簡潔明了，表明討論主題</li>
                    <li>內容：清楚說明問題或議題</li>
                    <li>結構：背景-問題-期望</li>
                    <li>可使用小標題、列表等增強可讀性</li>
                  </ul>
                </div>

                <div>
                  <h3 className="font-medium mb-1 text-foreground/80">社群準則</h3>
                  <ul className="list-disc pl-5 space-y-1">
                    <li>尊重他人觀點，即使不同意</li>
                    <li>避免人身攻擊和冒犯性言論</li>
                    <li>提供有建設性的回應</li>
                    <li>引用資料時提供來源</li>
                  </ul>
                </div>
              </>
            ) : (
              <>
                <div>
                  <h3 className="font-medium mb-1 text-foreground/80">什麼是好的觀點卡？</h3>
                  <ul className="list-disc pl-5 space-y-1">
                    <li>聚焦特定AI主題，有明確觀點</li>
                    <li>基於事實和數據，而非純主觀感受</li>
                    <li>提供獨特視角或深入分析</li>
                    <li>語言清晰，結構合理</li>
                  </ul>
                </div>

                <div>
                  <h3 className="font-medium mb-1 text-foreground/80">類型說明</h3>
                  <ul className="space-y-1">
                    {Object.entries(getSemanticTypeConfig())
                      .slice(0, 6)
                      .map(([type, config]) => (
                        <li key={type} className="flex items-start">
                          <span className="mr-2">{config.emoji}</span>
                          <div>
                            <span className="font-medium text-foreground/70">{config.label}:</span> {config.description}
                          </div>
                        </li>
                      ))}
                  </ul>
                </div>

                <div>
                  <h3 className="font-medium mb-1 text-foreground/80">格式建議</h3>
                  <ul className="list-disc pl-5 space-y-1">
                    <li>標題：簡潔明了，40字以內</li>
                    <li>內容：500-2000字為宜</li>
                    <li>結構：問題-分析-觀點-結論</li>
                    <li>可使用小標題、列表等增強可讀性</li>
                  </ul>
                </div>

                <div>
                  <h3 className="font-medium mb-1 text-foreground/80">審核標準</h3>
                  <ul className="list-disc pl-5 space-y-1">
                    <li>內容真實，無明顯事實錯誤</li>
                    <li>原創內容，非抄襲或大量引用</li>
                    <li>符合社區規範，無歧視他人權益</li>
                    <li>與所選主題相關</li>
                  </ul>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 添加確認對話框 */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent className="max-w-lg sm:max-w-xl">
          <DialogHeader>
            <DialogTitle>{contentType === "thread" ? "確認發起討論" : "確認發布觀點卡"}</DialogTitle>
            <DialogDescription>
              {contentType === "thread"
                ? "發布後，您的討論將立即公開，社群成員可以參與回應。"
                : "發布後，您的觀點卡將進入審核流程，審核通過後將公開顯示。"}
            </DialogDescription>
          </DialogHeader>

          <div className="py-4 max-h-[60vh] overflow-y-auto">
            <ContentCard
              {...{
                ...previewCard,
                content: truncateContent(content, 300),
                // 對於觀點卡，確保 subtopics 正確設置，對於討論，確保 tags 正確設置
                ...(contentType === "thread"
                  ? { tags: tags.length > 2 ? tags.slice(0, 2) : tags }
                  : { subtopics: tags.length > 2 ? tags.slice(0, 2) : tags }
                ),
                topics: selectedTopic ? [topics.find((t) => t.id === selectedTopic)?.name || selectedTopic] : [],
              }}
              variant="card"
              isInteractive={false}
              className="h-auto max-h-none overflow-visible"
            />
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowConfirmDialog(false)}>
              返回編輯
            </Button>
            <Button onClick={(e) => handleSubmit(e, false)}>{contentType === "thread" ? "確認發起" : "確認發布"}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
