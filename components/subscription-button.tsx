"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Plus, Check, Loader2, <PERSON>, BellOff } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

interface SubscriptionButtonProps {
    itemType: 'topic' | 'subtopic'
    itemId: string
    itemName: string
    className?: string
    size?: 'sm' | 'lg' | 'default' | 'icon'
    variant?: 'default' | 'outline' | 'secondary' | 'ghost'
    showCount?: boolean
    onSubscriptionChange?: (subscribed: boolean) => void
    preloadedSubscriptionStatus?: boolean
    preloadedSubscriberCount?: number
    skipInitialLoad?: boolean
}

export function SubscriptionButton({
    itemType,
    itemId,
    itemName,
    className,
    size = 'sm',
    variant = 'default',
    showCount = false,
    onSubscriptionChange,
    preloadedSubscriptionStatus,
    preloadedSubscriberCount,
    skipInitialLoad = false,
}: SubscriptionButtonProps) {
    const [isSubscribed, setIsSubscribed] = useState<boolean>(preloadedSubscriptionStatus ?? false)
    const [isLoading, setIsLoading] = useState<boolean>(false)
    const [subscriberCount, setSubscriberCount] = useState<number>(preloadedSubscriberCount ?? 0)
    const [isInitialLoading, setIsInitialLoading] = useState<boolean>(!skipInitialLoad && preloadedSubscriptionStatus === undefined)
    const { toast } = useToast()

    // 載入初始訂閱狀態和統計 - 只在沒有預載數據時執行
    useEffect(() => {
        if (skipInitialLoad || (preloadedSubscriptionStatus !== undefined && (!showCount || preloadedSubscriberCount !== undefined))) {
            setIsInitialLoading(false)
            return
        }

        const loadData = async () => {
            const tasks = []

            if (preloadedSubscriptionStatus === undefined) {
                tasks.push(loadSubscriptionStatus())
            }

            if (showCount && preloadedSubscriberCount === undefined) {
                tasks.push(loadSubscriptionStats())
            }

            if (tasks.length > 0) {
                await Promise.all(tasks)
            }

            setIsInitialLoading(false)
        }

        loadData()
    }, [itemType, itemId, preloadedSubscriptionStatus, preloadedSubscriberCount, showCount, skipInitialLoad])

    // 更新狀態當預載數據改變時
    useEffect(() => {
        if (preloadedSubscriptionStatus !== undefined) {
            setIsSubscribed(preloadedSubscriptionStatus)
        }
    }, [preloadedSubscriptionStatus])

    useEffect(() => {
        if (preloadedSubscriberCount !== undefined) {
            setSubscriberCount(preloadedSubscriberCount)
        }
    }, [preloadedSubscriberCount])

    const loadSubscriptionStatus = async () => {
        try {
            const response = await fetch(
                `/api/subscriptions/status?itemType=${itemType}&itemId=${itemId}`
            )
            const result = await response.json()

            if (result.success) {
                setIsSubscribed(result.data.subscribed)
            } else {
                console.error("載入訂閱狀態失敗:", result.error)
            }
        } catch (error) {
            console.error("載入訂閱狀態錯誤:", error)
        }
    }

    const loadSubscriptionStats = async () => {
        try {
            const response = await fetch(
                `/api/subscriptions/stats?itemType=${itemType}&itemId=${itemId}`
            )
            const result = await response.json()

            if (result.success) {
                setSubscriberCount(result.data.subscriber_count)
            } else {
                console.error("載入訂閱統計失敗:", result.error)
            }
        } catch (error) {
            console.error("載入訂閱統計錯誤:", error)
        }
    }

    const handleSubscriptionToggle = async () => {
        if (isLoading) return

        setIsLoading(true)
        try {
            const response = await fetch('/api/subscriptions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    itemType,
                    itemId,
                    action: 'toggle',
                }),
            })

            const result = await response.json()

            if (result.success) {
                const newSubscribedState = result.data.subscribed
                setIsSubscribed(newSubscribedState)

                // 更新訂閱數量
                if (showCount) {
                    setSubscriberCount(prev =>
                        newSubscribedState ? prev + 1 : Math.max(0, prev - 1)
                    )
                }

                // 回調父組件
                onSubscriptionChange?.(newSubscribedState)

                // 顯示成功訊息
                toast({
                    title: newSubscribedState ? "訂閱成功" : "取消訂閱成功",
                    description: newSubscribedState
                        ? `您已成功關注 ${itemName}`
                        : `您已取消關注 ${itemName}`,
                })
            } else {
                throw new Error(result.error || "操作失敗")
            }
        } catch (error) {
            console.error("訂閱操作錯誤:", error)
            toast({
                title: "操作失敗",
                description: error instanceof Error ? error.message : "請稍後再試",
                variant: "destructive",
            })
        } finally {
            setIsLoading(false)
        }
    }

    // 如果正在載入初始狀態，顯示載入狀態
    if (isInitialLoading) {
        return (
            <Button
                size={size}
                variant={variant}
                disabled
                className={className}
            >
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                載入中...
            </Button>
        )
    }

    return (
        <Button
            size={size}
            variant={isSubscribed ? 'outline' : variant}
            onClick={handleSubscriptionToggle}
            disabled={isLoading}
            className={className}
        >
            {isLoading ? (
                <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    處理中...
                </>
            ) : isSubscribed ? (
                <>
                    <Check className="h-4 w-4 mr-2" />
                    已關注
                    {showCount && subscriberCount > 0 && (
                        <span className="ml-2 text-sm">({subscriberCount})</span>
                    )}
                </>
            ) : (
                <>
                    <Plus className="h-4 w-4 mr-2" />
                    關注此{itemType === 'topic' ? '主題' : '子主題'}
                    {showCount && subscriberCount > 0 && (
                        <span className="ml-2 text-sm">({subscriberCount})</span>
                    )}
                </>
            )}
        </Button>
    )
}

// 簡化版本的訂閱按鈕，只顯示圖標
export function SubscriptionToggleButton({
    itemType,
    itemId,
    itemName,
    className,
    size = 'sm',
    onSubscriptionChange,
    preloadedSubscriptionStatus,
    skipInitialLoad = false,
}: {
    itemType: 'topic' | 'subtopic'
    itemId: string
    itemName: string
    className?: string
    size?: 'sm' | 'lg' | 'default' | 'icon'
    onSubscriptionChange?: (subscribed: boolean) => void
    preloadedSubscriptionStatus?: boolean
    skipInitialLoad?: boolean
}) {
    const [isSubscribed, setIsSubscribed] = useState<boolean>(preloadedSubscriptionStatus ?? false)
    const [isLoading, setIsLoading] = useState<boolean>(false)
    const [isInitialLoading, setIsInitialLoading] = useState<boolean>(!skipInitialLoad && preloadedSubscriptionStatus === undefined)
    const { toast } = useToast()

    // 載入初始訂閱狀態 - 只在沒有預載數據時執行
    useEffect(() => {
        if (skipInitialLoad || preloadedSubscriptionStatus !== undefined) {
            setIsInitialLoading(false)
            return
        }

        loadSubscriptionStatus().finally(() => {
            setIsInitialLoading(false)
        })
    }, [itemType, itemId, preloadedSubscriptionStatus, skipInitialLoad])

    // 更新狀態當預載數據改變時
    useEffect(() => {
        if (preloadedSubscriptionStatus !== undefined) {
            setIsSubscribed(preloadedSubscriptionStatus)
        }
    }, [preloadedSubscriptionStatus])

    const loadSubscriptionStatus = async () => {
        try {
            const response = await fetch(
                `/api/subscriptions/status?itemType=${itemType}&itemId=${itemId}`
            )
            const result = await response.json()

            if (result.success) {
                setIsSubscribed(result.data.subscribed)
            } else {
                console.error("載入訂閱狀態失敗:", result.error)
            }
        } catch (error) {
            console.error("載入訂閱狀態錯誤:", error)
        }
    }

    const handleSubscriptionToggle = async () => {
        if (isLoading) return

        setIsLoading(true)
        try {
            const response = await fetch('/api/subscriptions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    itemType,
                    itemId,
                    action: 'toggle',
                }),
            })

            const result = await response.json()

            if (result.success) {
                const newSubscribedState = result.data.subscribed
                setIsSubscribed(newSubscribedState)

                // 回調父組件
                onSubscriptionChange?.(newSubscribedState)

                // 顯示成功訊息
                toast({
                    title: newSubscribedState ? "訂閱成功" : "取消訂閱成功",
                    description: newSubscribedState
                        ? `您已成功關注 ${itemName}`
                        : `您已取消關注 ${itemName}`,
                })
            } else {
                throw new Error(result.error || "操作失敗")
            }
        } catch (error) {
            console.error("訂閱操作錯誤:", error)
            toast({
                title: "操作失敗",
                description: error instanceof Error ? error.message : "請稍後再試",
                variant: "destructive",
            })
        } finally {
            setIsLoading(false)
        }
    }

    // 如果正在載入初始狀態，顯示載入狀態
    if (isInitialLoading) {
        return (
            <Button
                size={size}
                variant="outline"
                disabled
                className={className}
            >
                <Loader2 className="h-4 w-4 animate-spin" />
            </Button>
        )
    }

    return (
        <Button
            size={size}
            variant="outline"
            onClick={handleSubscriptionToggle}
            disabled={isLoading}
            className={className}
        >
            {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
            ) : isSubscribed ? (
                <BellOff className="h-4 w-4" />
            ) : (
                <Bell className="h-4 w-4" />
            )}
        </Button>
    )
} 