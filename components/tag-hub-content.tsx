"use client"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Bookmark, ChevronRight, Flame, Home, MessageSquare, Plus, Tag } from "lucide-react"
import Link from "next/link"
import { ViewpointCard } from "@/components/viewpoint-card"

interface TagHubContentProps {
  tag: {
    name: string
    description: string
    parentTopic: string
    parentSubtopic: string
  }
}

export function TagHubContent({ tag }: TagHubContentProps) {
  // 模擬數據 - 標籤下的觀點卡
  const tagCards = [
    {
      id: 301,
      type: "concept",
      title: `${tag.name} 技術原理與應用場景`,
      content: `${tag.name} 作為 ${tag.parentTopic} 領域的重要技術，其核心原理是通過特定的算法和架構設計，解決傳統方法面臨的效率和性能挑戰。在實際應用中，它特別適用於需要高效處理大規模數據的場景，以及對實時性有較高要求的業務環境。\n\n關鍵特點：\n- 高效的計算資源利用\n- 靈活的擴展性設計\n- 豐富的應用場景適配性`,
      tags: [tag.name, tag.parentTopic, tag.parentSubtopic],
      author: "技術研究員",
      isLeader: true,
    },
    {
      id: 302,
      type: "implementation",
      title: `實作案例：${tag.name} 在企業級應用中的落地經驗`,
      content: `在將 ${tag.name} 技術應用於企業級項目時，我們採用了分階段實施策略，從概念驗證到小規模試點，再到全面部署。過程中發現，技術選型和架構設計是成功的關鍵，而持續優化和監控則確保了系統的穩定性和性能。\n\n實踐要點：\n- 明確的評估指標和基準測試\n- 階段性實施和反饋優化\n- 全面的監控和告警機制`,
      tags: [tag.name, "企業應用", "最佳實踐"],
      author: "社群用戶",
      isLeader: false,
    },
    {
      id: 303,
      type: "warning",
      title: `${tag.name} 實踐中的常見陷阱與解決方案`,
      content: `在 ${tag.name} 的實際應用過程中，我們遇到了幾個常見的技術陷阱：配置參數不當導致性能下降、擴展性設計不足導致系統瓶頸、監控不完善導致問題難以排查。通過系統性的方法和工具，我們成功解決了這些挑戰。\n\n避坑指南：\n- 參數優化的系統方法\n- 可擴展架構的設計原則\n- 全面監控和日誌系統的建立`,
      tags: [tag.name, "踩坑經驗", "問題解決"],
      author: "社群用戶",
      isLeader: false,
    },
    {
      id: 304,
      type: "experience",
      title: `${tag.name} 與其他技術的對比分析與選型指南`,
      content: `通過對 ${tag.name} 與同類技術的系統性對比，我們發現不同技術在特定場景下各有優勢。選型時需考慮多方面因素，包括性能需求、資源限制、團隊熟悉度等。本文提供了一個結構化的決策框架，幫助團隊做出最適合自身情況的技術選擇。\n\n選型考量：\n- 性能與資源需求平衡\n- 技術成熟度與社區活躍度\n- 團隊能力與學習曲線`,
      tags: [tag.name, "技術選型", "對比分析"],
      author: "社群用戶",
      isLeader: false,
    },
  ]

  // 模擬數據 - 討論串
  const discussionThreads = [
    {
      id: 401,
      title: `${tag.name} 的最新進展與未來發展方向`,
      replies: 24,
      lastActive: "3小時前",
      participants: 15,
    },
    {
      id: 402,
      title: `如何優化 ${tag.name} 在大規模應用中的性能`,
      replies: 18,
      lastActive: "6小時前",
      participants: 12,
    },
    {
      id: 403,
      title: `${tag.name} vs 傳統方法：何時選擇哪種技術？`,
      replies: 32,
      lastActive: "1天前",
      participants: 20,
    },
  ]

  // 模擬數據 - 熱度指標
  const heatMetrics = {
    newCards: 12,
    discussions: 48,
    bookmarks: 86,
  }

  const relatedTags = [
    {
      parentTopic: tag.parentTopic,
      parentSubtopic: tag.parentSubtopic,
    },
  ]

  return (
    <div className="space-y-8">
      {/* Breadcrumb 導航 */}
      <nav className="flex items-center text-sm text-muted-foreground">
        <Link href="/" className="hover:text-foreground flex items-center">
          <Home className="h-4 w-4 mr-1" />
          首頁
        </Link>
        <ChevronRight className="h-4 w-4 mx-1" />
        <Link href={`/topic/${tag.parentTopic.toLowerCase()}`} className="hover:text-foreground">
          {tag.parentTopic}
        </Link>
        <ChevronRight className="h-4 w-4 mx-1" />
        <span className="font-medium text-foreground">#{tag.name}</span>
      </nav>

      {/* Tag Header */}
      <div className="bg-muted rounded-lg p-6">
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold flex items-center">
              <Tag className="h-6 w-6 mr-2" />#{tag.name}
            </h1>
            <p className="text-muted-foreground max-w-2xl">{tag.description}</p>
            <div className="flex items-center space-x-2 pt-1">
              <Badge variant="outline">隸屬主題: {tag.parentTopic}</Badge>
              <Badge variant="outline">隸屬子題: {tag.parentSubtopic}</Badge>
            </div>
          </div>

          <div className="flex flex-col items-end space-y-2">
            <Button asChild>
              <Link href={`/submit?tag=${tag.name}`}>
                <Plus className="h-4 w-4 mr-1" />
                快速投稿
              </Link>
            </Button>
            <Button variant="outline" size="sm">
              <Bookmark className="h-4 w-4 mr-1" />
              關注標籤
            </Button>
          </div>
        </div>
      </div>

      {/* 主要內容區 */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* 左側：觀點卡區 - 增加列跨度 */}
        <div className="lg:col-span-3 space-y-6">
          <h2 className="text-xl font-bold">觀點卡</h2>

          <div className="space-y-4">
            {tagCards.map((card) => (
              <ViewpointCard key={card.id} card={card} />
            ))}
          </div>

          {/* Tag 討論區 */}
          <div className="pt-4">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold flex items-center">
                <MessageSquare className="h-5 w-5 mr-2" />
                Tag 討論區
              </h2>
              <Button variant="outline" size="sm" asChild>
                <Link href={`/tag/${tag.name.toLowerCase()}/discussions/new`}>
                  <Plus className="h-4 w-4 mr-1" />
                  新增討論
                </Link>
              </Button>
            </div>

            <div className="space-y-3">
              {discussionThreads.map((thread) => (
                <Card
                  key={thread.id}
                  className="overflow-hidden border-border/40 hover:border-border/80 transition-colors hover:shadow-md"
                >
                  <CardContent className="p-4">
                    <Link href={`/thread/${thread.id}`} className="block group">
                      <h3 className="text-lg font-medium group-hover:text-primary transition-colors">{thread.title}</h3>
                      <div className="flex items-center text-sm text-muted-foreground mt-2">
                        <div className="flex items-center mr-4">
                          <MessageSquare className="h-4 w-4 mr-1" />
                          {thread.replies} 則回覆
                        </div>
                        <div className="mr-4">{thread.participants} 位參與者</div>
                        <div>最後更新：{thread.lastActive}</div>
                      </div>
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* 右側：熱度指標和相關標籤 */}
        <div className="space-y-6">
          {/* 熱度指標 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <Flame className="h-5 w-5 mr-2" />
                熱度指標
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-3 gap-2 text-center">
              <div className="space-y-1">
                <div className="text-2xl font-bold">{heatMetrics.newCards}</div>
                <div className="text-xs text-muted-foreground">本週新卡</div>
              </div>
              <div className="space-y-1">
                <div className="text-2xl font-bold">{heatMetrics.discussions}</div>
                <div className="text-xs text-muted-foreground">本週討論</div>
              </div>
              <div className="space-y-1">
                <div className="text-2xl font-bold">{heatMetrics.bookmarks}</div>
                <div className="text-xs text-muted-foreground">本週收藏</div>
              </div>
            </CardContent>
          </Card>

          {/* 相關標籤 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <Tag className="h-5 w-5 mr-2" />
                相關標籤
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                <Link
                  href={`/topic/${relatedTags[0].parentTopic.toLowerCase()}/${relatedTags[0].parentSubtopic.toLowerCase()}`}
                >
                  <Badge variant="outline" className="cursor-pointer hover:bg-secondary">
                    #{relatedTags[0].parentSubtopic}
                  </Badge>
                </Link>
                <Link href={`/tag/${tag.parentTopic.toLowerCase()}`}>
                  <Badge variant="outline" className="cursor-pointer hover:bg-secondary">
                    #{tag.parentTopic}
                  </Badge>
                </Link>
                <Link href={`/tag/${tag.parentSubtopic.toLowerCase()}`}>
                  <Badge variant="outline" className="cursor-pointer hover:bg-secondary">
                    #{tag.parentSubtopic}
                  </Badge>
                </Link>
                <Link href="/tag/最佳實踐">
                  <Badge variant="outline" className="cursor-pointer hover:bg-secondary">
                    #最佳實踐
                  </Badge>
                </Link>
                <Link href="/tag/技術選型">
                  <Badge variant="outline" className="cursor-pointer hover:bg-secondary">
                    #技術選型
                  </Badge>
                </Link>
                <Link href="/tag/實戰經驗">
                  <Badge variant="outline" className="cursor-pointer hover:bg-secondary">
                    #實戰經驗
                  </Badge>
                </Link>
              </div>
            </CardContent>
          </Card>

          {/* 熱門串接推薦 */}
          <Card className="overflow-hidden border-border/40 hover:border-border/80 transition-colors hover:shadow-md">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <Flame className="h-5 w-5 mr-2" />
                熱門串接推薦
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link href="/thread/501" className="block group">
                <div className="text-sm font-medium group-hover:text-primary transition-colors">
                  {tag.name} 與其他技術的協同應用策略
                </div>
                <div className="text-xs text-muted-foreground flex items-center mt-1">
                  <MessageSquare className="h-3 w-3 mr-1" />
                  36 則回覆
                </div>
              </Link>
              <Link href="/thread/502" className="block group">
                <div className="text-sm font-medium group-hover:text-primary transition-colors">
                  {tag.name} 在大規模生產環境中的優化技巧
                </div>
                <div className="text-xs text-muted-foreground flex items-center mt-1">
                  <MessageSquare className="h-3 w-3 mr-1" />
                  28 則回覆
                </div>
              </Link>
              <Link href="/thread/503" className="block group">
                <div className="text-sm font-medium group-hover:text-primary transition-colors">
                  初學者如何快速掌握 {tag.name} 技術
                </div>
                <div className="text-xs text-muted-foreground flex items-center mt-1">
                  <MessageSquare className="h-3 w-3 mr-1" />
                  42 則回覆
                </div>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
