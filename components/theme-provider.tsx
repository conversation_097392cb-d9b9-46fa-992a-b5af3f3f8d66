"use client"
import { Theme<PERSON>rovider as NextThemesProvider } from "next-themes"
import type { ThemeProviderProps } from "next-themes"

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme="light"
      enableSystem={false}
      disableTransitionOnChange
      forcedTheme="light" // 強制使用淺色主題
      themes={["light", "dark"]}
      {...props}
    >
      {children}
    </NextThemesProvider>
  )
}
