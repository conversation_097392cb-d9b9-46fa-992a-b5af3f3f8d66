"use client"
import { <PERSON>, Sun } from "lucide-react"
import { useTheme } from "next-themes"
import { useEffect, useState } from "react"

import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface ThemeToggleProps {
  variant?: "default" | "outline" | "ghost" | "pill"
  size?: "default" | "sm" | "lg" | "icon"
  showTooltip?: boolean
  tooltipSide?: "top" | "right" | "bottom" | "left"
  showLabel?: boolean
  compact?: boolean
}

export function ThemeToggle({
  variant = "ghost",
  size = "icon",
  showTooltip = false,
  tooltipSide = "right",
  showLabel = false,
  compact = false,
}: ThemeToggleProps) {
  const { setTheme, theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  // After mounting, we can safely show the UI that depends on the theme
  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null
  }

  // Pill variant custom styling
  if (variant === "pill") {
    // If we want to hide the theme toggle, return null
    if (process.env.NEXT_PUBLIC_HIDE_THEME_TOGGLE === "true") {
      return null
    }

    return (
      <div className="flex items-center rounded-full border p-1 shadow-sm bg-background">
        <Button
          variant={theme === "light" ? "default" : "ghost"}
          size="sm"
          className={`rounded-full px-3 ${
            theme === "light" ? "text-primary-foreground" : "text-muted-foreground"
          } transition-all duration-200`}
          onClick={() => setTheme("light")}
        >
          <Sun className="h-4 w-4 mr-1" />
          {!compact && <span>淺色</span>}
        </Button>
        <Button
          variant={theme === "dark" ? "default" : "ghost"}
          size="sm"
          className={`rounded-full px-3 ${
            theme === "dark" ? "text-primary-foreground" : "text-muted-foreground"
          } transition-all duration-200`}
          onClick={() => setTheme("dark")}
        >
          <Moon className="h-4 w-4 mr-1" />
          {!compact && <span>深色</span>}
        </Button>
      </div>
    )
  }

  // Standard dropdown toggle
  const toggle = (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant={variant} size={size} className="rounded-full relative overflow-hidden group">
          <Sun
            className={`h-[1.2rem] w-[1.2rem] transition-all duration-500 ease-in-out ${
              theme === "dark" ? "rotate-90 scale-0 opacity-0" : "rotate-0 scale-100 opacity-100"
            }`}
          />
          <Moon
            className={`absolute h-[1.2rem] w-[1.2rem] transition-all duration-500 ease-in-out ${
              theme === "light" ? "-rotate-90 scale-0 opacity-0" : "rotate-0 scale-100 opacity-100"
            }`}
          />
          {showLabel && <span className="ml-2 hidden md:inline-block">{theme === "light" ? "淺色" : "深色"}</span>}
          <span className="sr-only">切換主題</span>
          <span className="absolute inset-0 rounded-full bg-primary/10 opacity-0 group-hover:opacity-100 transition-opacity" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="animate-in fade-in-50 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95"
      >
        <DropdownMenuItem onClick={() => setTheme("light")} className="flex items-center gap-2 cursor-pointer">
          <Sun className="h-4 w-4" />
          <span>淺色</span>
          {theme === "light" && <span className="ml-auto h-1.5 w-1.5 rounded-full bg-primary"></span>}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("dark")} className="flex items-center gap-2 cursor-pointer">
          <Moon className="h-4 w-4" />
          <span>深色</span>
          {theme === "dark" && <span className="ml-auto h-1.5 w-1.5 rounded-full bg-primary"></span>}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )

  if (showTooltip) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>{toggle}</TooltipTrigger>
          <TooltipContent
            side={tooltipSide}
            className="bg-popover text-popover-foreground animate-in fade-in-50 zoom-in-95"
          >
            <div className="flex items-center gap-2">
              <span>切換主題</span>
              <span className="text-xs text-muted-foreground">({theme === "light" ? "淺色" : "深色"})</span>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  return toggle
}
