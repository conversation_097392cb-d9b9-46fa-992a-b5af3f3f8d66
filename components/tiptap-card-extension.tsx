"use client"

import type React from "react"

import { Node, mergeAttributes } from "@tiptap/core"
import { ReactNodeViewRenderer, NodeViewWrapper } from "@tiptap/react"
import { Badge } from "@/components/ui/badge"
import { RichTextContent } from "@/components/rich-text-content"
import { cn } from "@/lib/utils"
import {
  getSemanticTypeConfig,
  getContentTypeConfig,
  getContributionTypeConfig,
  type ContentType,
} from "@/lib/constants"

// 卡片引用组件
const CardQuoteComponent = (props: any) => {
  const {
    node: {
      attrs: { cardId, cardTitle, cardContent, cardAuthor, cardType, cardTags, isLeader, contentType = "viewpoint" },
    },
    updateAttributes,
    deleteNode,
  } = props

  const typeConfig = getSemanticTypeConfig(cardType)
  const sourceConfig = getContributionTypeConfig(isLeader ? "leader" : "community")
  const cTypeConfig = getContentTypeConfig(contentType as ContentType)

  // 截断内容
  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + "..."
  }

  return (
    <NodeViewWrapper className="card-quote-wrapper">
      <div className="not-prose my-4 p-3 border rounded-md bg-muted/20 hover:bg-muted/30 transition-colors">
        <div className="flex flex-col gap-2">
          {/* 卡片头部 - 类型徽章和标签 */}
          <div className="flex flex-wrap items-center gap-2">
            {/* 内容类型徽章 */}
            <Badge variant="outline" className={cn("flex items-center gap-1", cTypeConfig.color)}>
              {cTypeConfig.icon}
              <span>{cTypeConfig.label}</span>
            </Badge>

            {/* 语义类型徽章 */}
            <Badge variant="outline" className={cn("flex items-center gap-1", typeConfig.color)}>
              {typeConfig.icon}
              <span>{typeConfig.label}</span>
            </Badge>

            {/* 主题徽章 */}
            {cardTags && cardTags.length > 0 && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <span>{cardTags[0]}</span>
              </Badge>
            )}

            {/* 来源类型徽章 */}
            {isLeader && sourceConfig.show && (
              <Badge variant="default" className={cn("flex items-center gap-1", sourceConfig.color)}>
                <span>{sourceConfig.badge}</span>
              </Badge>
            )}
          </div>

          {/* 作者信息 */}
          <div className="text-xs text-muted-foreground">作者：{cardAuthor}</div>

          {/* 标题 */}
          <h4 className="text-sm font-bold text-foreground">{cardTitle}</h4>

          {/* 内容 */}
          <div className="text-xs text-muted-foreground line-clamp-2 overflow-hidden">
            <RichTextContent content={cardContent} className="prose-sm" />
          </div>

          {/* 标签 */}
          <div className="flex flex-wrap gap-1 mt-1">
            {cardTags &&
              cardTags.map((tag: string, tagIndex: number) => (
                <Badge key={tagIndex} variant="outline" className="text-xs py-0">
                  #{tag}
                </Badge>
              ))}
          </div>

          {/* 底部链接 */}
          <div className="text-xs text-primary mt-1">
            <a href={contentType === "viewpoint" ? `/card/${cardId}` : `/thread/${cardId}`} className="hover:underline">
              查看完整{contentType === "viewpoint" ? "卡片" : "討論"}
            </a>
          </div>
        </div>
      </div>
    </NodeViewWrapper>
  )
}

// TipTap 卡片引用扩展
export const CardExtension = Node.create({
  name: "cardQuote",
  group: "block",
  atom: true,

  addAttributes() {
    return {
      cardId: {
        default: null,
      },
      cardTitle: {
        default: "",
      },
      cardContent: {
        default: "",
      },
      cardAuthor: {
        default: "",
      },
      cardType: {
        default: "concept",
      },
      cardTags: {
        default: [],
      },
      isLeader: {
        default: false,
      },
      contentType: {
        default: "viewpoint",
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="card-quote"]',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ["div", mergeAttributes({ "data-type": "card-quote" }, HTMLAttributes)]
  },

  addNodeView() {
    return ReactNodeViewRenderer(CardQuoteComponent)
  },
})
