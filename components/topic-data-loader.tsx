"use client"

import { useEffect, useState, useMemo } from "react"
import { ContentCard } from "@/components/content-card"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { MessageSquare, Eye, LightbulbIcon, AlertTriangle, Wrench, BookOpen, FlaskConical, HelpCircle } from "lucide-react"
import Link from "next/link"
import { Skeleton } from "@/components/ui/skeleton"
import { FilterState } from "@/components/content-filter"
import { cn } from "@/lib/utils"
import { getSemanticTypeConfig } from "@/lib/constants"

interface TopicDataLoaderProps {
  topicId: string
  activeSubtopic: string | null
  activeSubtopicId: string | null
  activeTab: string
  filterState: FilterState
}



// 語義類型標籤組件
function SemanticTypeBadge({ type }: { type?: string }) {
  if (!type) return null

  const typeConfig = getSemanticTypeConfig(type)
  if (!typeConfig) return null

  return (
    <Badge
      variant="outline"
      className={cn("flex items-center gap-1 text-xs py-0", typeConfig.color)}
    >
      {typeConfig.icon}
      <span>{typeConfig.label}</span>
    </Badge>
  )
}

// 主題標籤組件 - 使用與 content-card.tsx 一致的樣式
function TopicBadges({ topics, subtopics }: { topics?: any[]; subtopics?: any[] }) {
  if (!topics && !subtopics) return null

  return (
    <div className="flex flex-wrap gap-2">
      {topics?.map((topic, index) => (
        <Badge
          key={`topic-${index}`}
          variant="secondary"
          className="flex items-center gap-1 text-xs py-0"
        >
          <span>{topic.name}</span>
        </Badge>
      ))}
      {subtopics?.map((subtopic, index) => (
        <Badge
          key={`subtopic-${index}`}
          variant="secondary"
          className="flex items-center gap-1 text-xs py-0"
        >
          <span>#{subtopic.name}</span>
        </Badge>
      ))}
    </div>
  )
}

export function TopicDataLoader({ topicId, activeSubtopic, activeSubtopicId, activeTab, filterState }: TopicDataLoaderProps) {
  const [cards, setCards] = useState<any[]>([])
  const [threads, setThreads] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function loadData() {
      setLoading(true)

      if (activeTab === "viewpoints") {
        try {
          // 第一步：使用 API 獲取基本卡片數據
          let apiUrl = `/api/topics/${topicId}/cards?limit=20`
          if (activeSubtopicId) {
            apiUrl = `/api/subtopics/${activeSubtopicId}/cards?limit=20`
          }

          const response = await fetch(apiUrl)
          const result = await response.json()

          if (result.success && result.data) {
            const cards = result.data

            // 第二步：使用批處理API獲取所有卡片的統計數據和用戶狀態
            let batchStatsData: {
              stats: Record<string, any>,
              userStates: Record<string, any>
            } = { stats: {}, userStates: {} }

            if (cards.length > 0) {
              const batchItems = cards.map((card: any) => ({
                id: card.id,
                type: "card"
              }))

              try {
                const batchResponse = await fetch('/api/stats/batch', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ items: batchItems })
                })

                if (batchResponse.ok) {
                  const batchResult = await batchResponse.json()
                  if (batchResult.success) {
                    batchStatsData = batchResult.data
                  }
                }
              } catch (batchError) {
                console.error("Error fetching batch stats:", batchError)
              }
            }

            // 第三步：合併數據格式以符合前端組件需求
            const formattedCards = cards.map((card: any) => ({
              id: card.id,
              title: card.title,
              content: card.content,
              author: {
                id: card.author?.id || "",
                name: card.author?.name || "匿名用戶",
                avatar: card.author?.avatar || "",
              },
              stats: {
                likes: batchStatsData.stats[card.id]?.likes || card.stats?.likes || 0,
                dislikes: batchStatsData.stats[card.id]?.dislikes || card.stats?.dislikes || 0,
                comments: batchStatsData.stats[card.id]?.comments || card.stats?.comments || 0,
                bookmarks: batchStatsData.stats[card.id]?.bookmarks || card.stats?.bookmarks || 0,
                // 添加用戶狀態數據
                hasLiked: batchStatsData.userStates[card.id]?.liked || false,
                hasDisliked: batchStatsData.userStates[card.id]?.disliked || false,
                hasBookmarked: batchStatsData.userStates[card.id]?.bookmarked || false,
              },
              contentType: "viewpoint",
              semanticType: card.semantic_type || "concept",
              variant: "grid",
              topics: card.topics?.map((t: any) => t.name) || [],
              subtopics: card.subtopics?.map((s: any) => s.name) || [],
              sourceType: card.contribution_type || "original",
              contribution_type: card.contribution_type || "community",
              originalAuthor: card.original_author || "",
              originalSource: card.original_url || "",
              timestamp: formatTimeAgo(card.created_at),
              features: { truncate: true },
            }))

            setCards(formattedCards)
          } else {
            setCards([])
          }
        } catch (error) {
          console.error("Error loading cards:", error)
          setCards([])
        }
      } else if (activeTab === "discussions") {
        try {
          // 第一步：加載討論串的基本資料
          const response = await fetch(`/api/topics/${topicId}/threads?limit=20`)
          const result = await response.json()

          if (result.success && result.data) {
            const threads = result.data

            // 第二步：使用批處理API獲取所有討論串的統計數據和用戶狀態
            let batchStatsData: {
              stats: Record<string, any>,
              userStates: Record<string, any>
            } = { stats: {}, userStates: {} }

            if (threads.length > 0) {
              const batchItems = threads.map((thread: any) => ({
                id: thread.id,
                type: "thread"
              }))

              try {
                const batchResponse = await fetch('/api/stats/batch', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ items: batchItems })
                })

                if (batchResponse.ok) {
                  const batchResult = await batchResponse.json()
                  if (batchResult.success) {
                    batchStatsData = batchResult.data
                  }
                }
              } catch (batchError) {
                console.error("Error fetching batch stats for threads:", batchError)
              }
            }

            // 第三步：合併數據格式
            const formattedThreads = threads.map((thread: any) => ({
              id: thread.id,
              contentType: "thread",
              variant: "default",
              title: thread.title,
              author: thread.author?.name || "匿名",
              semantic_type: thread.semantic_type || "question",
              topics: thread.topics || [],
              subtopics: thread.subtopics || [],
              stats: {
                replies: batchStatsData.stats[thread.id]?.comments || thread.stats?.replies || 0,
                views: thread.stats?.views || Math.floor(Math.random() * 1000),
                participants: thread.stats?.participants || [],
                // 添加用戶狀態
                hasLiked: batchStatsData.userStates[thread.id]?.liked || false,
                hasDisliked: batchStatsData.userStates[thread.id]?.disliked || false,
                hasBookmarked: batchStatsData.userStates[thread.id]?.bookmarked || false,
              },
              lastActive: formatTimeAgo(thread.last_activity_at || thread.created_at),
            }))

            setThreads(formattedThreads)
          } else {
            setThreads([])
          }
        } catch (error) {
          console.error("Error loading threads:", error)
          setThreads([])
        }
      }

      setLoading(false)
    }

    loadData()
  }, [topicId, activeSubtopic, activeSubtopicId, activeTab])

  // 格式化相對時間的函數
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return `${diffInSeconds}秒前`
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}分鐘前`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}小時前`
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}天前`
    if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)}個月前`
    return `${Math.floor(diffInSeconds / 31536000)}年前`
  }

  // 應用過濾器
  const filteredCards = useMemo(() => {
    if (!cards.length) return cards

    return cards.filter((card) => {
      // 語義類型過濾
      if (filterState.selectedSemanticTypes.length > 0) {
        if (!card.semanticType || !filterState.selectedSemanticTypes.includes(card.semanticType)) {
          return false
        }
      }

      // 主題過濾
      if (filterState.selectedTopics.length > 0) {
        const hasTopicMatch = filterState.selectedTopics.some(selectedTopic =>
          card.topics?.includes(selectedTopic)
        )
        if (!hasTopicMatch) {
          return false
        }
      }

      // 子主題過濾
      if (filterState.selectedSubtopics.length > 0) {
        const hasSubtopicMatch = filterState.selectedSubtopics.some(selectedSubtopic =>
          card.subtopics?.includes(selectedSubtopic)
        )
        if (!hasSubtopicMatch) {
          return false
        }
      }

      return true
    })
  }, [cards, filterState])

  const filteredThreads = useMemo(() => {
    if (!threads.length) return threads

    return threads.filter((thread) => {
      // 語義類型過濾
      if (filterState.selectedSemanticTypes.length > 0) {
        if (!thread.semanticType || !filterState.selectedSemanticTypes.includes(thread.semanticType)) {
          return false
        }
      }

      // 主題過濾
      if (filterState.selectedTopics.length > 0) {
        const hasTopicMatch = filterState.selectedTopics.some(selectedTopic =>
          thread.topics?.includes(selectedTopic)
        )
        if (!hasTopicMatch) {
          return false
        }
      }

      // 子主題過濾
      if (filterState.selectedSubtopics.length > 0) {
        const hasSubtopicMatch = filterState.selectedSubtopics.some(selectedSubtopic =>
          thread.subtopics?.includes(selectedSubtopic)
        )
        if (!hasSubtopicMatch) {
          return false
        }
      }

      return true
    })
  }, [threads, filterState])

  if (loading) {
    return activeTab === "viewpoints" ? (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <Card key={i} className="overflow-hidden h-full">
            <CardContent className="p-0">
              <div className="p-4">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-full mb-1" />
                <Skeleton className="h-4 w-full mb-1" />
                <Skeleton className="h-4 w-2/3" />
              </div>
              <div className="border-t p-4 flex justify-between">
                <Skeleton className="h-8 w-8 rounded-full" />
                <Skeleton className="h-4 w-24" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    ) : (
      <div className="space-y-4">
        <div className="grid grid-cols-12 text-sm font-medium border-b pb-2">
          <div className="col-span-6">主題</div>
          <div className="col-span-2 text-center">參與者</div>
          <div className="col-span-1 text-center">回覆</div>
          <div className="col-span-2 text-center">瀏覽</div>
          <div className="col-span-1 text-center">活動</div>
        </div>

        {[1, 2, 3, 4, 5].map((i) => (
          <Card key={i} className="overflow-hidden">
            <CardContent className="p-4">
              <div className="grid grid-cols-12 items-center gap-4">
                <div className="col-span-6 space-y-2">
                  {/* 第一行：semantic_type 標籤 + 標題 */}
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-5 w-12" />
                    <Skeleton className="h-5 w-3/4" />
                  </div>
                  {/* 第二行：topic 標籤 */}
                  <div className="flex gap-1">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                </div>
                <div className="col-span-2 text-center flex justify-center">
                  {/* 參與者頭像組 */}
                  <div className="flex -space-x-1">
                    <Skeleton className="h-6 w-6 rounded-full" />
                    <Skeleton className="h-6 w-6 rounded-full" />
                    <Skeleton className="h-6 w-6 rounded-full" />
                  </div>
                </div>
                <div className="col-span-1 text-center">
                  <Skeleton className="h-6 w-12 mx-auto" />
                </div>
                <div className="col-span-2 text-center">
                  <Skeleton className="h-6 w-12 mx-auto" />
                </div>
                <div className="col-span-1 text-center">
                  <Skeleton className="h-6 w-16 mx-auto" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (activeTab === "viewpoints") {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {filteredCards.length > 0 ? (
          filteredCards.map((card) => (
            <div key={card.id} className="relative group h-full">
              <ContentCard {...card} />
            </div>
          ))
        ) : (
          <div className="col-span-2 text-center py-12">
            <p className="text-muted-foreground">
              {cards.length > 0 ? "無符合過濾條件的觀點卡" : "暫無相關觀點卡"}
            </p>
          </div>
        )}
      </div>
    )
  } else {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-12 text-sm font-medium border-b pb-2">
          <div className="col-span-6">主題</div>
          <div className="col-span-2 text-center">參與者</div>
          <div className="col-span-1 text-center">回覆</div>
          <div className="col-span-2 text-center">瀏覽</div>
          <div className="col-span-1 text-center">活動</div>
        </div>

        {filteredThreads.length > 0 ? (
          filteredThreads.map((thread) => (
            <Link key={thread.id} href={`/thread/${thread.id}`} className="block">
              <Card className="overflow-hidden border-border/40 hover:border-border/80 transition-colors hover:shadow-md cursor-pointer">
                <CardContent className="p-4">
                  <div className="grid grid-cols-12 items-center gap-4">
                    <div className="col-span-6 space-y-2">
                      {/* 第一行：semantic_type 標籤 + 標題 */}
                      <div className="flex items-center gap-2">
                        <SemanticTypeBadge type={thread.semantic_type} />
                        <span className="font-medium hover:text-primary transition-colors line-clamp-1 flex-1">
                          {thread.title}
                        </span>
                      </div>

                      {/* 第二行：topic 標籤 + subtopic 標籤 */}
                      <TopicBadges topics={thread.topics} subtopics={thread.subtopics} />
                    </div>

                    <div className="col-span-2 text-center flex justify-center">
                      {/* 參與者頭像組 */}
                      <div className="flex -space-x-1">
                        {thread.stats?.participants?.slice(0, 4).map((participant: any, index: number) => (
                          <Avatar key={index} className="h-6 w-6 border-2 border-white">
                            <AvatarFallback className="bg-primary/10 text-primary text-xs">
                              {participant.name?.charAt(0).toUpperCase() || "A"}
                            </AvatarFallback>
                          </Avatar>
                        ))}
                        {thread.stats?.participants && thread.stats.participants.length > 4 && (
                          <div className="h-6 w-6 bg-gray-100 border-2 border-white rounded-full flex items-center justify-center text-xs text-gray-600">
                            +{thread.stats.participants.length - 4}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="col-span-1 text-center flex justify-center">
                      <Badge variant="outline" className="flex items-center text-xs">
                        <MessageSquare className="h-3 w-3 mr-1" />
                        <span>{thread.stats?.replies || 0}</span>
                      </Badge>
                    </div>

                    <div className="col-span-2 text-center flex justify-center">
                      <Badge variant="outline" className="flex items-center">
                        <Eye className="h-3 w-3 mr-1" />
                        <span>{thread.stats?.views >= 1000 ? `${(thread.stats.views / 1000).toFixed(1)}k` : thread.stats?.views}</span>
                      </Badge>
                    </div>

                    <div className="col-span-1 text-center">
                      <Badge variant="secondary">{thread.lastActive}</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">
              {threads.length > 0 ? "無符合過濾條件的討論" : "暫無相關討論"}
            </p>
          </div>
        )}
      </div>
    )
  }
}
