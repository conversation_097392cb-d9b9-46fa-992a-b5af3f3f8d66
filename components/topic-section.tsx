import type React from "react"
import { ArrowRight } from "lucide-react"
import Link from "next/link"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"

interface TopicSectionProps {
  id: string
  name: string
  description: string
  cardCount: number
  icon: React.ComponentType<{ className?: string }>
  className?: string
  slug?: string // 添加這行
}

export function TopicSection({
  id,
  name,
  description,
  cardCount,
  icon: Icon,
  className,
  slug, // 添加這行
}: TopicSectionProps) {
  return (
    <div className={cn("border rounded-lg shadow-sm bg-card text-card-foreground", className)}>
      <div className="flex items-center space-x-4 p-5">
        <Icon className="h-10 w-10" />
        <div>
          <h3 className="text-lg font-semibold">{name}</h3>
          <p className="text-sm text-muted-foreground">{description}</p>
        </div>
      </div>
      <div className="p-5">
        <Button variant="ghost" className="w-full justify-between group">
          <Link
            href={`/topic/${slug || encodeURIComponent(name)}`} // 使用 slug 優先
            className="flex items-center justify-between w-full"
          >
            瀏覽主題
            <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
          </Link>
        </Button>
      </div>
    </div>
  )
}
