"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { usePathname } from "next/navigation"
import Link from "next/link"
import { ChevronDown, ChevronRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Skeleton } from "@/components/ui/skeleton"
import { cn } from "@/lib/utils"

interface Topic {
  id: string
  name: string
  description: string
  slug: string
  subtopics: Subtopic[]
}

interface Subtopic {
  id: string
  name: string
  topic_id: string
  description: string
  slug: string
}

export function TopicSidebar() {
  const [topics, setTopics] = useState<Topic[]>([])
  const [expandedTopics, setExpandedTopics] = useState<Record<string, boolean>>({})
  const [loading, setLoading] = useState(true)
  const pathname = usePathname()

  // 獲取當前活動的主題和子主題
  const getActiveFromPath = () => {
    const parts = pathname.split("/")
    if (parts.length >= 3 && parts[1] === "topic") {
      return {
        topic: decodeURIComponent(parts[2]),
        subtopic: parts.length >= 4 ? decodeURIComponent(parts[3]) : null,
      }
    }
    return { topic: null, subtopic: null }
  }

  const { topic: activeTopic, subtopic: activeSubtopic } = getActiveFromPath()

  // 獲取主題和子主題數據
  useEffect(() => {
    const fetchTopics = async () => {
      try {
        setLoading(true)
        const response = await fetch("/api/topics/with-subtopics")

        if (!response.ok) {
          throw new Error("Failed to fetch topics")
        }

        const data = await response.json()
        setTopics(data)

        // 初始化展開狀態，將當前活動的主題設置為展開
        const initialExpandedState: Record<string, boolean> = {}
        data.forEach((topic: Topic) => {
          initialExpandedState[topic.name] = topic.slug.toLowerCase() === activeTopic?.toLowerCase()
        })
        setExpandedTopics(initialExpandedState)
      } catch (error) {
        console.error("Error fetching topics:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchTopics()
  }, [activeTopic])

  // 切換主題的展開/收起狀態
  const toggleTopic = (topicName: string, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setExpandedTopics((prev) => ({
      ...prev,
      [topicName]: !prev[topicName],
    }))
  }

  // 渲染加載中的骨架屏
  if (loading) {
    return (
      <div className="w-64 border-r bg-background h-screen sticky top-0 z-30 p-4">
        <div className="mb-6">
          <Skeleton className="h-6 w-32 mb-2" />
        </div>
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="h-5 w-40" />
              <div className="pl-4 space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-28" />
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="w-64 border-r bg-background h-screen sticky top-0 z-30">
      <div className="p-4">
        <h2 className="text-lg font-semibold mb-6">主題瀏覽</h2>
      </div>
      <ScrollArea className="h-[calc(100vh-5rem)]">
        <div className="px-4 pb-8">
          {topics.length === 0 ? (
            <div className="text-center text-muted-foreground py-4">暫無主題</div>
          ) : (
            <div className="space-y-1">
              {topics.map((topic) => (
                <div key={topic.id} className="space-y-1">
                  <div className="flex items-center">
                    <button
                      className="p-1 mr-1 rounded-sm hover:bg-muted focus:outline-none focus:ring-2 focus:ring-primary"
                      onClick={(e) => toggleTopic(topic.name, e)}
                      aria-label={expandedTopics[topic.name] ? "收起主題" : "展開主題"}
                    >
                      {expandedTopics[topic.name] ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </button>
                    <Button
                      variant={
                        activeTopic?.toLowerCase() === topic.slug.toLowerCase() && !activeSubtopic
                          ? "secondary"
                          : "ghost"
                      }
                      size="sm"
                      className="flex-1 justify-start"
                      asChild
                    >
                      <Link href={`/topic/${encodeURIComponent(topic.slug)}`}>
                        {topic.name}
                        <span className="ml-auto text-xs text-muted-foreground">{topic.subtopics?.length || 0}</span>
                      </Link>
                    </Button>
                  </div>

                  {expandedTopics[topic.name] && topic.subtopics && topic.subtopics.length > 0 && (
                    <div className="ml-6 space-y-1">
                      {topic.subtopics.map((subtopic) => (
                        <Button
                          key={subtopic.id}
                          variant={
                            activeTopic?.toLowerCase() === topic.slug.toLowerCase() &&
                            activeSubtopic?.toLowerCase() === subtopic.slug.toLowerCase()
                              ? "secondary"
                              : "ghost"
                          }
                          size="sm"
                          className={cn(
                            "w-full justify-start text-sm",
                            activeTopic?.toLowerCase() === topic.slug.toLowerCase() &&
                              activeSubtopic?.toLowerCase() === subtopic.slug.toLowerCase() &&
                              "font-medium",
                          )}
                          asChild
                        >
                          <Link href={`/topic/${encodeURIComponent(topic.slug)}/${encodeURIComponent(subtopic.slug)}`}>
                            <div className="h-2 w-2 rounded-full bg-primary mr-2" />
                            {subtopic.name}
                          </Link>
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}
