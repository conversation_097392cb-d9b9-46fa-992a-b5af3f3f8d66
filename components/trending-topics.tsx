"use client"

import { useQuery } from "@tanstack/react-query"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"

interface TrendingTopic {
  id: string
  name: string
  count: number
}

interface TrendingTopicsProps {
  onTopicSelect: (topicId: string) => void
  selectedTopics: string[]
}

export function TrendingTopics({ onTopicSelect, selectedTopics }: TrendingTopicsProps) {
  const { data, isLoading } = useQuery({
    queryKey: ["trending-topics"],
    queryFn: async () => {
      const response = await fetch("/api/trending-topics")
      if (!response.ok) throw new Error("Failed to fetch trending topics")
      const data = await response.json()
      return data.topics as TrendingTopic[]
    },
    staleTime: 1000 * 60 * 5, // 5分鐘緩存
  })

  if (isLoading) {
    return (
      <div className="space-y-2">
        <h3 className="font-medium mb-2">熱門主題</h3>
        {Array(5)
          .fill(0)
          .map((_, i) => (
            <Skeleton key={i} className="h-8 w-full mb-2" />
          ))}
      </div>
    )
  }

  return (
    <div className="space-y-2">
      <h3 className="font-medium mb-2">熱門主題</h3>
      {data?.map((topic) => (
        <Button
          key={topic.id}
          variant={selectedTopics.includes(topic.id) ? "default" : "ghost"}
          className="w-full justify-start"
          onClick={() => onTopicSelect(topic.id)}
        >
          #{topic.name}
          <span className="ml-auto text-xs text-muted-foreground">{topic.count}</span>
        </Button>
      ))}
    </div>
  )
}
