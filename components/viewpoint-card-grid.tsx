"use client"
import { ContentCard, type ContentCardProps } from "@/components/content-card"
import { cn } from "@/lib/utils"

interface ViewpointCardGridProps {
  cards: ContentCardProps[]
  columns?: 1 | 2 | 3 | 4
  gap?: "sm" | "md" | "lg"
  isCompact?: boolean
  className?: string
}

export function ViewpointCardGrid({
  cards,
  columns = 3,
  gap = "md",
  isCompact = false,
  className,
}: ViewpointCardGridProps) {
  // Map gap size to Tailwind classes
  const gapClasses = {
    sm: "gap-2",
    md: "gap-4",
    lg: "gap-6",
  }

  // Map columns to Tailwind grid classes
  const columnClasses = {
    1: "grid-cols-1",
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
  }

  return (
    <div className={cn("grid", columnClasses[columns], gapClasses[gap], className)}>
      {cards.map((card) => (
        <ContentCard key={card.id} {...card} variant="grid" isCompact={isCompact} />
      ))}
    </div>
  )
}
