"use client"

import { createContext, useContext, useEffect, useState, ReactNode } from "react"
import { useBatchStats } from "@/hooks/use-batch-stats"

interface BatchItem {
    id: string
    type: "card" | "thread"
}

interface BatchStats {
    likes: number
    dislikes: number
    comments: number
    bookmarks: number
}

interface BatchUserStates {
    liked: boolean
    disliked: boolean
    bookmarked: boolean
}

interface BatchStatsContextValue {
    getItemStats: (id: string) => BatchStats
    getItemUserStates: (id: string) => BatchUserStates
    registerItems: (items: BatchItem[]) => void
    unregisterItems: (items: BatchItem[]) => void
    refetchItem: (id: string) => Promise<void>
    isLoading: boolean
    error: string | null
}

const BatchStatsContext = createContext<BatchStatsContextValue | undefined>(undefined)

interface BatchStatsProviderProps {
    children: ReactNode
    initialItems?: BatchItem[]
}

export function BatchStatsProvider({ children, initialItems = [] }: BatchStatsProviderProps) {
    const [registeredItems, setRegisteredItems] = useState<BatchItem[]>(initialItems)
    const [itemRegistry, setItemRegistry] = useState<Set<string>>(new Set())

    // 使用批量統計 Hook
    const { stats, userStates, isLoading, error, refetch } = useBatchStats({
        items: registeredItems,
        enabled: registeredItems.length > 0
    })

    // 註冊項目
    const registerItems = (items: BatchItem[]) => {
        const newItems: BatchItem[] = []
        const newRegistry = new Set(itemRegistry)

        items.forEach(item => {
            const key = `${item.type}:${item.id}`
            if (!newRegistry.has(key)) {
                newRegistry.add(key)
                newItems.push(item)
            }
        })

        if (newItems.length > 0) {
            setItemRegistry(newRegistry)
            setRegisteredItems(prev => [...prev, ...newItems])
        }
    }

    // 取消註冊項目
    const unregisterItems = (items: BatchItem[]) => {
        const keysToRemove = new Set(items.map(item => `${item.type}:${item.id}`))
        const newRegistry = new Set([...itemRegistry].filter(key => !keysToRemove.has(key)))
        const newItems = registeredItems.filter(item => !keysToRemove.has(`${item.type}:${item.id}`))

        setItemRegistry(newRegistry)
        setRegisteredItems(newItems)
    }

    // 獲取項目統計
    const getItemStats = (id: string): BatchStats => {
        return stats[id] || { likes: 0, dislikes: 0, comments: 0, bookmarks: 0 }
    }

    // 獲取項目用戶狀態
    const getItemUserStates = (id: string): BatchUserStates => {
        return userStates[id] || { liked: false, disliked: false, bookmarked: false }
    }

    // 重新獲取特定項目
    const refetchItem = async (id: string) => {
        // 這裡可以針對單個項目優化，目前先重新獲取所有數據
        await refetch()
    }

    const contextValue: BatchStatsContextValue = {
        getItemStats,
        getItemUserStates,
        registerItems,
        unregisterItems,
        refetchItem,
        isLoading,
        error
    }

    return (
        <BatchStatsContext.Provider value={contextValue}>
            {children}
        </BatchStatsContext.Provider>
    )
}

// Hook 來使用 BatchStats Context
export function useBatchStatsContext(): BatchStatsContextValue {
    const context = useContext(BatchStatsContext)
    if (context === undefined) {
        throw new Error("useBatchStatsContext must be used within a BatchStatsProvider")
    }
    return context
}

// Hook 來為單個項目註冊和獲取統計
export function useItemStatsContext(id: string, type: "card" | "thread") {
    const {
        getItemStats,
        getItemUserStates,
        registerItems,
        unregisterItems,
        refetchItem,
        isLoading,
        error
    } = useBatchStatsContext()

    // 註冊和取消註冊項目
    useEffect(() => {
        const items = [{ id, type }]
        registerItems(items)

        return () => {
            unregisterItems(items)
        }
    }, [id, type, registerItems, unregisterItems])

    return {
        stats: getItemStats(id),
        userStates: getItemUserStates(id),
        refetch: () => refetchItem(id),
        isLoading,
        error
    }
} 