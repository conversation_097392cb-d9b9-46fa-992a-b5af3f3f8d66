# AILogora User Flow Design - Confluence 文件

## 📋 專案概述

**AILogora** 是基於 Next.js 15.2.4 + React 19.1.0 + Bun v1.2.13 的 AI 知識分享平台，採用 Supabase 作為後端服務。

### 🏗️ 技術架構
- **前端**: Next.js 15.2.4 (App Router), React 19.1.0, Tailwind CSS
- **後端**: Next.js API Routes + Supabase
- **資料庫**: PostgreSQL (Supabase)
- **認證**: Supabase Auth
- **執行環境**: Bun v1.2.13

---

## 🗺️ 頁面詳細分析

### 1. 首頁 `/`

**路由**: `app/page.tsx`
**渲染方式**: Server-Side Rendering (SSR)

#### 使用的組件:
- `HomeContent` (Server Component)
- `TopicsSection` (Server Component)
- `PopularCardsSection` (Server Component) 
- `LatestCardsSection` (Server Component)
- `PopularThreadsSection` (Server Component)
- `TopicSection`
- `ContentCard`

#### 使用的 API/服務:
- `getAllTopics()` - 獲取所有主題和子主題
- `getPopularCards({ limit: 2 })` - 獲取熱門觀點卡
- `getLatestCards({ limit: 2 })` - 獲取最新觀點卡
- `getPopularThreads({ limit: 2 })` - 獲取熱門討論串

#### 用戶可執行操作:
- 瀏覽熱門主題卡片 → 跳轉至 `/topic/[slug]/[subtopic]`
- 瀏覽核心主題牆 → 跳轉至 `/topic/[slug]`
- 查看熱門觀點卡 → 跳轉至 `/card/[id]`
- 查看最新觀點卡 → 跳轉至 `/card/[id]`
- 查看熱門討論串 → 跳轉至 `/thread/[id]`
- 點擊「查看全部」→ 跳轉至 `/explore`
- 點擊「投稿」按鈕 → 跳轉至 `/submit`

#### 資料流:
```typescript
Server Component → lib/topic-service → Supabase
Server Component → lib/card-service → Supabase  
Server Component → lib/thread-service → Supabase
```

---

### 2. 探索頁面 `/explore`

**路由**: `app/explore/page.tsx`
**渲染方式**: Client-Side Rendering (CSR)

#### 使用的組件:
- `ContentCard`
- `Tabs`, `TabsContent`, `TabsList`, `TabsTrigger`
- `Badge`, `Select`, `Input`, `Button`
- `Popover`, `Checkbox`

#### 使用的 API:
- `GET /api/explore` - 主要內容獲取
- `GET /api/topics?limit=50` - 載入篩選用主題
- `GET /api/subtopics?limit=200` - 載入篩選用子主題

#### 用戶可執行操作:
- **內容類型篩選**: 全部/觀點卡/討論串
- **排序選擇**: 趨勢/最新/最多讚
- **語義類型篩選**: 看法/實測經驗/工具教學/討論/問題等
- **主題篩選**: 選擇主題和子主題
- **搜尋功能**: 關鍵字搜尋
- **無限滾動**: 載入更多內容
- **清除篩選**: 重置所有篩選條件
- 點擊內容卡片 → 跳轉至 `/card/[id]` 或 `/thread/[id]`

#### 狀態管理:
```typescript
// 主要狀態
- results: ContentItem[]
- isLoading: boolean
- hasMore: boolean
- selectedSemanticTypes: string[]
- selectedTopics: string[]
- selectedSubtopics: string[]
- searchQuery: string
- availableTopics: Topic[]
- availableSubtopics: Subtopic[]
```

#### 資料流:
```typescript
useEffect → /api/topics + /api/subtopics → 篩選選項
useEffect → /api/explore → 內容列表
IntersectionObserver → 無限滾動載入
```

---

### 3. 投稿頁面 `/submit`

**路由**: `app/submit/page.tsx`
**渲染方式**: Client-Side Rendering (CSR)

#### 使用的組件:
- `SubmitTypeSelection`
- `SubmitForm`
- `ClientSubmitPage`

#### 使用的 API:
- `GET /api/topics` - 載入主題選項
- `GET /api/subtopics` - 載入子主題選項
- `POST /api/cards` - 創建觀點卡
- `POST /api/threads` - 創建討論串
- `POST /api/drafts` - 保存草稿 (如果實作)

#### URL 參數支援:
- `?type=original|discussion` - 預設內容類型
- `?topic=topicId` - 預設主題
- `?tag=tagName` - 預設標籤
- `?draft=draftId` - 編輯草稿

#### 用戶可執行操作:
- **選擇內容類型**: 觀點卡(原創/實測經驗/工具教學) / 討論串(討論/問題/集思廣益)
- **填寫標題**: 文字輸入
- **編輯內容**: 富文本編輯器
- **選擇主題**: 下拉選單多選
- **選擇子主題**: 下拉選單多選
- **設定貢獻類型**: 原創/策展/翻譯
- **填寫原作者資訊**: (如果是轉載)
- **預覽內容**: 查看發布效果
- **保存草稿**: 暫存至草稿箱
- **立即發布**: 提交審核(觀點卡) / 立即發布(討論串)

#### 流程跳轉:
- 觀點卡發布成功 → 跳轉至 `/card/[id]`
- 討論串發布成功 → 跳轉至 `/thread/[id]`
- 保存草稿成功 → 跳轉至 `/my-posts?tab=drafts`

---

### 4. 觀點卡詳細頁 `/card/[id]`

**路由**: `app/card/[id]/page.tsx`
**渲染方式**: Server-Side Rendering (SSR) + generateMetadata

#### 使用的組件:
- `CardDetailContent`
- `Comments` (評論系統)
- `Reactions` (反應系統)

#### 使用的 API/服務:
- `getCardById(id)` - 載入卡片詳細資料
- Supabase direct query - 載入點讚/倒讚數
- `GET /api/comments?item_id=[id]&item_type=card` - 載入評論
- `POST /api/reactions` - 點讚/倒讚
- `POST /api/bookmarks` - 收藏/取消收藏
- `POST /api/comments` - 新增評論

#### 用戶可執行操作:
- **閱讀完整內容**: 觀點卡詳細內容
- **查看作者資訊**: 點擊作者名稱
- **查看相關主題**: 點擊主題標籤 → 跳轉至 `/topic/[slug]`
- **互動反應**: 點讚/倒讚/收藏
- **評論互動**: 新增評論/回覆評論/對評論按讚
- **分享功能**: 複製連結/社群分享
- **回到列表**: 麵包屑導航回到來源頁面

#### 權限控制:
- **遊客**: 只能閱讀內容
- **登入用戶**: 可進行所有互動操作
- **作者**: 可編輯/刪除自己的內容

---

### 5. 討論串詳細頁 `/thread/[id]`

**路由**: `app/thread/[id]/page.tsx`
**渲染方式**: Server-Side Rendering (SSR) + generateMetadata

#### 使用的組件:
- `ThreadDetailContent`
- `Comments` (回覆系統)
- `Reactions` (反應系統)

#### 使用的 API/服務:
- `getThreadById(id)` - 載入討論串詳細資料
- `GET /api/comments?item_id=[id]&item_type=thread` - 載入回覆
- `POST /api/reactions` - 點讚/倒讚
- `POST /api/bookmarks` - 收藏/取消收藏
- `POST /api/comments` - 新增回覆

#### 用戶可執行操作:
- **閱讀討論主題**: 完整討論內容
- **瀏覽回覆串**: 多層級回覆查看
- **參與討論**: 新增回覆/回覆特定用戶
- **互動反應**: 對討論和回覆進行點讚/倒讚
- **收藏討論**: 加入個人收藏
- **關注討論**: 訂閱回覆通知

---

### 6. 主題頁面 `/topic/[...slug]`

**路由**: `app/topic/[...slug]/page.tsx`
**渲染方式**: Server-Side Rendering (SSR)

#### 使用的組件:
- `TopicHeader`
- `ContentCard`
- `Filter` (篩選器)
- `Pagination`

#### 使用的 API/服務:
- `GET /api/topics/[slug]` - 載入主題詳細資料
- `GET /api/cards?topicId=[id]` - 載入主題相關觀點卡
- `GET /api/threads?topicId=[id]` - 載入主題相關討論串

#### 用戶可執行操作:
- **瀏覽主題介紹**: 主題描述和統計資訊
- **查看子主題**: 相關子主題列表
- **篩選內容**: 按類型/語義/排序篩選
- **瀏覽內容列表**: 相關觀點卡和討論串
- **快速投稿**: 在該主題下投稿 → `/submit?topic=[topicId]`

---

### 7. 主題瀏覽 (已整合至首頁和側邊欄)

**功能**: 主題瀏覽功能已整合至首頁和側邊欄，提供更直觀的用戶體驗
- **首頁**: 顯示熱門主題和核心主題牆
- **側邊欄**: 完整的主題樹狀瀏覽

#### 用戶可執行操作:
- **首頁瀏覽**: 查看熱門主題和核心主題
- **側邊欄導航**: 完整主題樹狀瀏覽
- **直接跳轉**: 點擊主題 → 跳轉至 `/topic/[slug]`

---

### 8. 我的內容頁 `/my-posts`

**路由**: `app/my-posts/page.tsx`
**渲染方式**: Client-Side Rendering (CSR) + 認證保護

#### 使用的組件:
- `Tabs` (待審核/草稿/已發布)
- `ContentCard`
- `Dialog` (刪除確認)

#### 使用的 API:
- `GET /api/my-content?type=cards&status=pending` - 待審核觀點卡
- `GET /api/my-content?type=cards&status=draft` - 草稿觀點卡
- `GET /api/my-content?type=cards&status=published` - 已發布觀點卡
- `GET /api/my-content?type=threads&status=draft` - 草稿討論串
- `GET /api/my-content?type=threads&status=published` - 已發布討論串
- `DELETE /api/my-content?type=[type]&id=[id]` - 刪除草稿

#### 用戶可執行操作:
- **管理待審核內容**: 查看審核狀態/編輯/撤回
- **管理草稿**: 繼續編輯/刪除/發布
- **管理已發布內容**: 查看/編輯/刪除/查看統計
- **創建新內容**: 跳轉至 `/submit`
- **編輯草稿**: 跳轉至 `/submit?draft=[id]&type=[type]`

#### URL 參數支援:
- `?tab=pending|drafts|published` - 預設標籤頁
- `?subTab=viewpoints|discussions` - 預設子標籤頁

---

### 9. 收藏庫頁 `/library`

**路由**: `app/library/page.tsx`
**渲染方式**: Client-Side Rendering (CSR) + 認證保護

#### 使用的組件:
- `LibrarySidebar`
- `CollectionCard`
- `BookmarkedItemCard`
- `Dialog` (創建收藏集/分類)

#### 使用的 API:
- `GET /api/collections` - 載入收藏集列表
- `GET /api/collections/categories` - 載入收藏集分類
- `GET /api/bookmarks` - 載入收藏的內容
- `GET /api/library/tags` - 載入標籤列表
- `POST /api/collections` - 創建收藏集
- `POST /api/collections/categories` - 創建分類
- `DELETE /api/bookmarks` - 移除收藏

#### 用戶可執行操作:
- **管理收藏集**: 創建/編輯/刪除收藏集
- **管理分類**: 創建/編輯收藏集分類
- **組織收藏**: 將內容加入/移出收藏集
- **搜尋收藏**: 在收藏中搜尋內容
- **標籤篩選**: 按主題標籤篩選收藏
- **批量操作**: 批量移動/刪除收藏

#### 側邊欄功能:
- **分類導航**: 按分類瀏覽收藏集
- **收藏集列表**: 快速切換收藏集
- **標籤雲**: 按標籤篩選收藏

---

### 10. 認證頁面 `/auth/login`

**路由**: `app/auth/login/page.tsx`
**渲染方式**: Client-Side Rendering (CSR)

#### 使用的組件:
- `Card`, `Input`, `Button`
- `AuthContext`

#### 使用的 API/服務:
- Supabase Auth API - 用戶登入
- `signIn(email, password)` - AuthContext 方法
- `signOut()` - 登出方法

#### 用戶可執行操作:
- **用戶登入**: 輸入 email/密碼登入
- **記住登入狀態**: 自動維持會話
- **登出**: 如果已登入，可以登出
- **註冊跳轉**: 前往註冊頁面
- **忘記密碼**: 密碼重設功能

#### URL 參數支援:
- `?redirectTo=[path]` - 登入後重定向路徑

#### 狀態處理:
- **未登入**: 顯示登入表單
- **已登入**: 顯示已登入狀態 + 繼續/登出選項
- **載入中**: 顯示載入指示器

---

### 11. 管理後台 `/admin/seed-topics` & `/admin/seed-cards`

**路由**: `app/admin/seed-topics/page.tsx`, `app/admin/seed-cards/page.tsx`
**渲染方式**: Client-Side Rendering (CSR) + 管理員權限

#### 使用的 API:
- 用於開發環境的資料種子功能
- 直接操作 Supabase 資料庫

#### 用戶可執行操作:
- **初始化主題資料**: 載入預設主題和子主題
- **初始化卡片資料**: 載入測試觀點卡資料

---

## 🔄 API 端點完整列表

### 內容相關 API

| API Endpoint | Method | 用途 | 使用頁面 |
|-------------|--------|------|----------|
| `/api/cards` | GET | 獲取觀點卡列表 | 首頁, 探索頁, 主題頁 |
| `/api/cards` | POST | 創建觀點卡 | 投稿頁 |
| `/api/cards` | PUT | 更新觀點卡 | 編輯頁 |
| `/api/threads` | GET | 獲取討論串列表 | 首頁, 探索頁, 主題頁 |
| `/api/threads` | POST | 創建討論串 | 投稿頁 |
| `/api/threads` | PUT | 更新討論串 | 編輯頁 |
| `/api/explore` | GET | 混合內容探索 | 探索頁 |
| `/api/my-content` | GET | 用戶內容管理 | 我的內容頁 |
| `/api/my-content` | DELETE | 刪除用戶內容 | 我的內容頁 |

### 主題相關 API

| API Endpoint | Method | 用途 | 使用頁面 |
|-------------|--------|------|----------|
| `/api/topics` | GET | 獲取主題列表 | 首頁, 探索頁, 投稿頁 |
| `/api/topics/[slug]` | GET | 獲取單一主題 | 主題頁 |
| `/api/subtopics` | GET | 獲取子主題列表 | 探索頁, 投稿頁 |
| `/api/trending-topics` | GET | 獲取熱門主題 | 首頁側欄 |

### 互動相關 API

| API Endpoint | Method | 用途 | 使用頁面 |
|-------------|--------|------|----------|
| `/api/reactions` | POST | 添加反應(讚/倒讚) | 內容詳細頁 |
| `/api/reactions` | DELETE | 移除反應 | 內容詳細頁 |
| `/api/reactions/count` | GET | 獲取反應統計 | 內容詳細頁 |
| `/api/reactions/check` | GET | 檢查用戶反應狀態 | 內容詳細頁 |
| `/api/comments` | GET | 獲取評論列表 | 內容詳細頁 |
| `/api/comments` | POST | 新增評論 | 內容詳細頁 |
| `/api/comments/reactions` | POST | 對評論反應 | 內容詳細頁 |

### 收藏相關 API

| API Endpoint | Method | 用途 | 使用頁面 |
|-------------|--------|------|----------|
| `/api/bookmarks` | GET | 獲取收藏列表 | 收藏庫頁 |
| `/api/bookmarks` | POST | 添加收藏 | 任何內容頁 |
| `/api/bookmarks` | DELETE | 移除收藏 | 收藏庫頁, 內容頁 |
| `/api/collections` | GET | 獲取收藏集列表 | 收藏庫頁 |
| `/api/collections` | POST | 創建收藏集 | 收藏庫頁 |
| `/api/collections/categories` | GET | 獲取收藏集分類 | 收藏庫頁 |
| `/api/library/tags` | GET | 獲取收藏標籤 | 收藏庫頁 |

### 認證相關 API

| API Endpoint | Method | 用途 | 使用頁面 |
|-------------|--------|------|----------|
| `/api/auth/session` | GET | 檢查會話狀態 | 全域 AuthContext |
| Supabase Auth API | POST | 用戶登入/註冊 | 認證頁面 |

---

## 🎯 用戶流程與頁面跳轉

### 新用戶首次訪問流程
```
1. 訪問首頁 (/) 
   → 瀏覽熱門內容
   → 點擊內容 → 內容詳細頁 (/card/[id] 或 /thread/[id])
   → 想要互動 → 提示登入 → 登入頁 (/auth/login)
   → 登入成功 → 返回原頁面
   → 開始互動 (點讚/收藏/評論)
```

### 內容創作流程
```
1. 首頁或任意頁面點擊「投稿」
   → 投稿頁 (/submit)
   → 選擇內容類型 (觀點卡/討論串)
   → 填寫內容、選擇主題
   → 發布成功 → 跳轉至內容詳細頁 (/card/[id] 或 /thread/[id])
```

### 內容管理流程
```
1. 我的內容頁 (/my-posts)
   → 查看待審核/草稿/已發布內容
   → 編輯草稿 → 投稿頁 (/submit?draft=[id])
   → 查看內容 → 內容詳細頁 (/card/[id] 或 /thread/[id])
```

### 內容探索流程
```
1. 探索頁 (/explore)
   → 設定篩選條件 (類型/主題/語義)
   → 搜尋關鍵字
   → 瀏覽結果列表
   → 點擊內容 → 內容詳細頁
   → 點擊主題標籤 → 主題頁 (/topic/[slug])
```

### 收藏管理流程
```
1. 任意內容頁點擊收藏
   → 內容加入收藏
   → 收藏庫頁 (/library)
   → 創建收藏集/分類
   → 組織收藏內容
   → 分享收藏集
```

---

## 🛠️ 技術實作細節

### 認證狀態管理
```typescript
// contexts/auth-context.tsx
- 全域認證狀態管理
- 自動會話檢查
- 保護路由中介軟體 (middleware.ts)
- 受保護頁面: /submit, /my-posts, /library, /admin/*
```

### 資料載入策略
- **SSR頁面**: 首頁, 內容詳細頁, 主題頁, 主題列表頁
- **CSR頁面**: 探索頁, 投稿頁, 我的內容頁, 收藏庫頁, 認證頁面
- **混合策略**: generateMetadata (動態 SEO) + SSR

### 快取機制
- **Server-side**: API 路由記憶體快取 (5分鐘)
- **Client-side**: React Query / SWR (未實作)
- **Database**: Supabase 內建快取

### 錯誤處理
- **Server Error**: NextResponse 包裝的標準化錯誤回應
- **Client Error**: Toast 通知系統
- **Authentication Error**: 自動重定向至登入頁面

---

*此文件詳細描述了 AILogora 平台每個頁面的具體實作，適用於前後端開發者協作和系統維護參考。* 