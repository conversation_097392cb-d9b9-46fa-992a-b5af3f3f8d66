# AILogora User Flow Design 文件

## 📋 目錄
1. [專案概述](#專案概述)
2. [核心用戶角色](#核心用戶角色)
3. [主要功能模組](#主要功能模組)
4. [詳細用戶流程圖](#詳細用戶流程圖)
5. [關鍵頁面流程](#關鍵頁面流程)
6. [異常流程處理](#異常流程處理)
7. [用戶體驗優化建議](#用戶體驗優化建議)

---

## 🎯 專案概述

**AILogora** 是一個專注於 AI 技術的中文知識分享與討論平台，採用 Next.js 15.2.4 + React 19.1.0 技術棧開發。平台通過**觀點卡**和**討論串**兩種核心內容形式，促進 AI 領域的深度知識交流與學習。

### 🏗️ 技術架構
- **前端**: Next.js 15.2.4, React 19.1.0, Tailwind CSS
- **後端**: Next.js API Routes, Supabase
- **資料庫**: PostgreSQL (透過 Supabase)
- **認證**: Supabase Auth
- **執行環境**: Bun v1.2.13

### 🎨 設計原則
- **結構化知識分享**: 通過觀點卡片化提升內容品質
- **社群驅動**: 促進用戶間的深度互動和討論
- **主題分類**: 基於 AI 技術領域的系統性內容組織
- **用戶友好**: 直觀的操作介面和流暢的使用體驗

---

## 👥 核心用戶角色

### 1. 🆕 新用戶 (Anonymous Visitor)
- **特徵**: 首次訪問，對平台功能不熟悉
- **需求**: 了解平台價值，快速上手
- **行為**: 瀏覽內容、探索功能、考慮註冊

### 2. 📖 內容消費者 (Content Consumer)
- **特徵**: 已註冊，主要瀏覽和學習
- **需求**: 高品質內容發現、便捷收藏管理
- **行為**: 閱讀觀點卡、參與討論、收藏內容

### 3. ✍️ 內容創作者 (Content Creator)
- **特徵**: 活躍用戶，經常分享觀點和經驗
- **需求**: 便捷的內容創作工具、良好的展示效果
- **行為**: 發布觀點卡、發起討論、回應社群

### 4. 🔧 管理員 (Administrator)
- **特徵**: 平台管理者，維護內容品質
- **需求**: 內容審核工具、用戶管理功能
- **行為**: 審核內容、管理用戶、維護平台秩序

---

## 🧩 主要功能模組

### 1. 🏠 首頁探索 (Home & Discovery)
- **熱門主題展示**: 顯示當前最活躍的 AI 技術主題
- **核心主題牆**: 展示 AI 領域的核心技術分類
- **精選內容**: 熱門觀點卡和最新討論串
- **社群統計**: 活躍用戶數、今日新發布內容等

### 2. 🔍 內容探索 (Content Exploration)
- **主題瀏覽**: 按 AI 技術主題分類瀏覽
- **標籤篩選**: 透過子主題和標籤精確篩選
- **搜尋功能**: 全文搜尋觀點卡和討論內容
- **排序選項**: 按熱度、時間、相關性排序

### 3. ✨ 內容創作 (Content Creation)
- **觀點卡投稿**: 
  - 支援原創觀點、實測經驗、工具教學三種類型
  - 富文本編輯器支援
  - 主題和標籤分類
  - 草稿保存功能
- **討論串發起**:
  - 支援討論、問題、集思廣益三種類型
  - 內容引用和卡片提及
  - 即時發布機制

### 4. 💬 社群互動 (Social Interaction)
- **反應系統**: 點讚、反對等情感反應
- **評論討論**: 多層級評論和回覆
- **內容收藏**: 個人收藏和收藏集管理
- **用戶關注**: 關注感興趣的創作者（規劃中）

### 5. 📚 個人管理 (Personal Management)
- **我的內容**: 管理已發布和草稿內容
- **收藏庫**: 組織和管理收藏的內容
- **個人資料**: 設定和展示個人信息
- **創作統計**: 查看內容表現和互動數據

### 6. 🔐 用戶認證 (Authentication)
- **註冊登入**: 透過 Supabase Auth 實現
- **會話管理**: 自動登入狀態維護
- **權限控制**: 基於用戶角色的功能訪問

---

## 📊 詳細用戶流程圖

### 🎯 新用戶首次訪問流程

```mermaid
graph TD
    A[用戶訪問 AILogora] --> B{是否已登入?}
    B -->|否| C[顯示首頁內容]
    B -->|是| D[顯示個人化首頁]
    
    C --> E[瀏覽熱門主題]
    C --> F[查看精選觀點卡]
    C --> G[探索討論串]
    
    E --> H{對內容感興趣?}
    F --> H
    G --> H
    
    H -->|是| I[點擊查看詳細內容]
    H -->|否| J[繼續瀏覽或離開]
    
    I --> K{想要互動?}
    K -->|是| L[提示需要登入]
    K -->|否| M[繼續瀏覽]
    
    L --> N[前往註冊/登入頁面]
    N --> O[完成註冊]
    O --> P[返回原內容頁面]
    P --> Q[開始互動 - 點讚/收藏/評論]
```

### 📝 內容創作流程

```mermaid
graph TD
    A[用戶點擊「投稿」按鈕] --> B{選擇內容類型}
    B -->|觀點卡| C[選擇語義類型]
    B -->|討論串| D[選擇討論類型]
    
    C --> E[看法/實測經驗/工具教學]
    D --> F[討論/問題/集思廣益]
    
    E --> G[填寫觀點卡表單]
    F --> H[填寫討論串表單]
    
    G --> I[編輯標題]
    H --> I
    
    I --> J[使用富文本編輯器]
    J --> K[選擇主題分類]
    K --> L[添加相關標籤]
    L --> M[預覽內容效果]
    
    M --> N{內容滿意?}
    N -->|否| O[返回編輯]
    N -->|是| P{選擇操作}
    
    O --> I
    P -->|保存草稿| Q[保存至草稿箱]
    P -->|立即發布| R[確認發布對話框]
    
    R --> S{確認發布?}
    S -->|否| T[返回編輯]
    S -->|是| U[提交內容]
    
    T --> I
    U --> V{內容類型?}
    V -->|觀點卡| W[進入審核流程]
    V -->|討論串| X[立即發布]
    
    W --> Y[等待審核結果]
    Y --> Z[審核通過 - 正式發布]
    X --> AA[跳轉至討論頁面]
    Z --> BB[跳轉至觀點卡頁面]
```

### 🔍 內容發現與瀏覽流程

```mermaid
graph TD
    A[用戶進入探索頁面] --> B[載入預設內容列表]
    B --> C{選擇瀏覽方式}
    
    C -->|主題瀏覽| D[選擇感興趣主題]
    C -->|搜尋| E[輸入搜尋關鍵字]
    C -->|標籤篩選| F[選擇相關標籤]
    
    D --> G[載入主題相關內容]
    E --> H[執行搜尋並顯示結果]
    F --> I[篩選符合標籤的內容]
    
    G --> J[內容列表顯示]
    H --> J
    I --> J
    
    J --> K[用戶瀏覽內容卡片]
    K --> L{點擊感興趣內容?}
    
    L -->|是| M[進入內容詳細頁面]
    L -->|否| N[繼續瀏覽或調整篩選]
    
    M --> O[閱讀完整內容]
    O --> P{想要互動?}
    
    P -->|是| Q{用戶登入狀態?}
    P -->|否| R[查看相關推薦]
    
    Q -->|已登入| S[執行互動操作]
    Q -->|未登入| T[提示登入]
    
    S --> U[點讚/收藏/評論]
    T --> V[跳轉登入頁面]
    R --> W[瀏覽推薦內容]
    
    N --> X[調整篩選條件]
    X --> B
```

### 📱 用戶個人中心流程

```mermaid
graph TD
    A[用戶訪問個人中心] --> B{選擇功能模組}
    
    B -->|我的內容| C[查看發布內容統計]
    B -->|收藏庫| D[管理收藏內容]
    B -->|個人設定| E[編輯個人資料]
    
    C --> F{選擇內容類型}
    F -->|草稿| G[查看未發布草稿]
    F -->|已發布| H[查看已發布內容]
    
    G --> I{草稿操作}
    I -->|繼續編輯| J[跳轉編輯頁面]
    I -->|刪除草稿| K[確認刪除對話框]
    
    H --> L{內容管理}
    L -->|查看統計| M[顯示互動數據]
    L -->|編輯內容| N[跳轉編輯頁面]
    
    D --> O[顯示收藏列表]
    O --> P{收藏管理}
    P -->|移除收藏| Q[從收藏中移除]
    P -->|創建收藏集| R[新增收藏集]
    P -->|整理收藏| S[分類管理收藏]
    
    E --> T[編輯個人資料表單]
    T --> U[更新頭像/暱稱/簡介]
    U --> V[保存變更]
```

---

## 🗂️ 關鍵頁面流程

### 1. 🏠 首頁 (Home Page)
**路由**: `/`

**主要組件流程**:
1. **頁面載入** → 獲取社群統計數據
2. **熱門主題區塊** → 顯示最活躍的主題和子主題
3. **核心主題牆** → 展示 AI 領域核心分類
4. **精選內容** → 載入熱門觀點卡和最新討論
5. **用戶互動** → 點擊進入詳細頁面或主題頁面

**API 調用**:
- `GET /api/trending-topics` - 獲取熱門主題
- `GET /api/cards?popular=true&limit=2` - 獲取熱門觀點卡
- `GET /api/threads?latest=true&limit=2` - 獲取最新討論

### 2. 📝 投稿頁面 (Submit Page)
**路由**: `/submit`

**主要流程**:
1. **內容類型選擇** → 觀點卡 vs 討論串
2. **語義類型選擇** → 根據內容類型顯示不同選項
3. **表單填寫**:
   - 標題輸入 (必填)
   - 富文本內容編輯 (必填)
   - 主題分類選擇 (必填)
   - 子主題/標籤添加 (選填)
4. **預覽確認** → 顯示發布前預覽
5. **發布選項**:
   - 保存草稿
   - 立即發布 (討論串) / 提交審核 (觀點卡)

**API 調用**:
- `GET /api/topics` - 獲取主題列表
- `GET /api/subtopics` - 獲取子主題列表
- `POST /api/cards` - 創建觀點卡
- `POST /api/threads` - 創建討論串

### 3. 🔍 探索頁面 (Explore Page)
**路由**: `/explore`

**功能模組**:
1. **篩選控制**:
   - 內容類型切換 (觀點卡/討論串/全部)
   - 主題篩選下拉選單
   - 子主題/標籤多選
   - 排序方式選擇 (熱度/時間/相關性)
2. **內容展示**:
   - 響應式網格布局
   - 內容卡片預覽
   - 分頁或無限滾動載入
3. **快速操作**:
   - 收藏按鈕
   - 點讚/反應
   - 分享功能

**API 調用**:
- `GET /api/explore` - 獲取篩選後的內容列表
- `POST /api/reactions` - 處理用戶反應
- `POST /api/bookmarks` - 管理收藏

### 4. 📄 內容詳細頁面
**路由**: `/card/[id]` (觀點卡) / `/thread/[id]` (討論串)

**頁面結構**:
1. **內容主體**:
   - 完整標題和內容
   - 作者信息和發布時間
   - 主題和標籤顯示
   - 統計數據 (瀏覽數、點讚數等)
2. **互動區域**:
   - 反應按鈕 (點讚/反對)
   - 收藏按鈕
   - 分享選項
3. **評論系統**:
   - 評論列表顯示
   - 新增評論表單
   - 回覆和多層級評論
4. **相關推薦**:
   - 相同主題的其他內容
   - 作者的其他內容

### 5. 📚 收藏庫頁面 (Library Page)
**路由**: `/library`

**功能組織**:
1. **收藏總覽**:
   - 收藏統計 (總數、分類數量)
   - 最近收藏的內容
2. **收藏集管理**:
   - 創建新收藏集
   - 編輯收藏集資訊
   - 收藏集內容管理
3. **標籤系統**:
   - 自動生成標籤
   - 手動添加標籤
   - 標籤篩選功能
4. **批量操作**:
   - 多選收藏項目
   - 批量移動到收藏集
   - 批量刪除收藏

### 6. 👤 個人資料頁面 (Profile Page)
**路由**: `/profile` (自己) / `/profile/[id]` (他人)

**自己的資料頁面**:
1. **基本資訊編輯**:
   - 頭像上傳
   - 暱稱修改
   - 個人簡介編輯
2. **統計資訊**:
   - 發布內容數量
   - 獲得互動統計
   - 加入平台時間
3. **內容管理**:
   - 已發布觀點卡
   - 已發布討論串
   - 草稿管理

**他人的資料頁面**:
1. **公開資訊顯示**
2. **發布內容列表**
3. **關注/追蹤功能** (規劃中)

---

## ⚠️ 異常流程處理

### 1. 🔐 認證相關異常
```mermaid
graph TD
    A[用戶執行需要認證的操作] --> B{檢查登入狀態}
    B -->|未登入| C[顯示登入提示]
    B -->|已登入但會話過期| D[自動重新整理 Token]
    B -->|已登入且有效| E[繼續執行操作]
    
    C --> F[跳轉登入頁面]
    D --> G{Token 重新整理成功?}
    G -->|是| E
    G -->|否| H[清除本地會話]
    H --> F
    
    F --> I[用戶完成登入]
    I --> J[返回原頁面並執行操作]
```

### 2. 📡 網路請求異常
```mermaid
graph TD
    A[發起 API 請求] --> B{請求是否成功?}
    B -->|成功| C[處理回應數據]
    B -->|網路錯誤| D[顯示網路錯誤訊息]
    B -->|伺服器錯誤 5xx| E[顯示伺服器錯誤訊息]
    B -->|客戶端錯誤 4xx| F[根據錯誤碼處理]
    
    D --> G[提供重試按鈕]
    E --> G
    F --> H{錯誤類型}
    H -->|401 未授權| I[跳轉登入頁面]
    H -->|403 禁止訪問| J[顯示權限錯誤]
    H -->|404 未找到| K[顯示內容不存在]
    H -->|其他| L[顯示一般錯誤訊息]
    
    G --> M[用戶點擊重試]
    M --> A
```

### 3. 📝 內容提交異常
```mermaid
graph TD
    A[用戶提交內容] --> B[前端表單驗證]
    B --> C{驗證通過?}
    C -->|否| D[顯示驗證錯誤訊息]
    C -->|是| E[發送到伺服器]
    
    D --> F[用戶修正表單]
    F --> B
    
    E --> G{伺服器處理結果}
    G -->|成功| H[顯示成功訊息並跳轉]
    G -->|業務邏輯錯誤| I[顯示具體錯誤原因]
    G -->|系統錯誤| J[顯示系統錯誤並保存草稿]
    
    I --> K[用戶修正後重新提交]
    K --> E
    
    J --> L[自動保存草稿]
    L --> M[提示用戶稍後重試]
```

### 4. 🗂️ 資料載入異常
```mermaid
graph TD
    A[頁面開始載入資料] --> B[顯示載入狀態]
    B --> C[發起資料請求]
    C --> D{請求結果}
    
    D -->|成功且有資料| E[顯示資料內容]
    D -->|成功但無資料| F[顯示空狀態頁面]
    D -->|請求失敗| G[顯示錯誤狀態]
    
    F --> H[提供相關操作建議]
    G --> I[提供重試選項]
    I --> J[用戶點擊重試]
    J --> C
```

---

## 🎨 用戶體驗優化建議

### 1. 🚀 效能優化
- **圖片懶載入**: 內容卡片中的圖片採用懶載入
- **分頁載入**: 大列表採用分頁或無限滾動
- **快取策略**: API 回應採用適當的快取機制
- **骨架屏**: 資料載入時顯示內容骨架

### 2. 📱 響應式設計
- **行動優先**: 優先考慮行動裝置的使用體驗
- **觸控友好**: 按鈕和互動元件大小適合觸控操作
- **橫屏適配**: 平板橫屏模式的布局優化

### 3. ♿ 無障礙設計
- **鍵盤操作**: 所有功能都能透過鍵盤操作
- **螢幕閱讀器**: 提供適當的 ARIA 標籤和語義化標記
- **色彩對比**: 確保足夠的色彩對比度
- **字體大小**: 支援使用者自訂字體大小

### 4. 🔍 搜尋體驗優化
- **智能建議**: 輸入時提供搜尋建議
- **搜尋歷史**: 保存用戶的搜尋歷史
- **高亮顯示**: 搜尋結果中高亮關鍵字
- **相關搜尋**: 提供相關搜尋建議

### 5. 🎯 個人化推薦
- **基於興趣**: 根據用戶互動記錄推薦相關內容
- **主題偏好**: 學習用戶的主題偏好
- **時間模式**: 根據用戶活躍時間推送內容
- **社交推薦**: 基於關注和互動網路的推薦

### 6. 📊 資料視覺化
- **統計圖表**: 個人資料頁面的數據視覺化
- **趨勢展示**: 主題熱度和討論趨勢圖
- **進度追蹤**: 內容創作和參與度進度
- **成就系統**: 用戶參與度的成就勳章

### 7. 🔔 通知系統
- **即時通知**: 評論、點讚等即時通知
- **郵件摘要**: 定期的平台動態郵件
- **推播通知**: 重要活動的推播提醒
- **通知設定**: 用戶可自訂通知偏好

### 8. 🌐 國際化支援
- **多語言**: 支援繁體中文、簡體中文、英文
- **地區設定**: 時間格式和文化適配
- **內容本地化**: 根據地區推薦相關內容

---

## 📈 追蹤與分析

### 1. 用戶行為追蹤
- 頁面瀏覽路徑分析
- 內容互動熱點圖
- 搜尋關鍵字統計
- 用戶留存率分析

### 2. 內容效能指標
- 觀點卡閱讀完成率
- 討論串參與度
- 內容分享率
- 主題熱度變化

### 3. 功能使用統計
- 各功能模組使用頻率
- 創作工具使用偏好
- 篩選和搜尋行為
- 收藏和管理習慣

---

## 🚀 未來發展規劃

### 短期目標 (1-3 個月)
- 完善內容審核機制
- 優化行動裝置體驗
- 增強搜尋功能
- 建立通知系統

### 中期目標 (3-6 個月)
- 實現用戶關注系統
- 添加內容推薦算法
- 開發行動應用程式
- 建立社群管理工具

### 長期目標 (6-12 個月)
- AI 驅動的內容推薦
- 多媒體內容支援
- 國際化和多語言
- 開放 API 和第三方整合

---

*本文件版本: v1.0*  
*最後更新: 2024年12月*  
*負責人: AILogora 產品團隊* 