# 訂閱功能實作說明

## 概述

本專案實作了完整的訂閱功能，讓使用者可以訂閱主題 (topic) 和子主題 (subtopic)。這個功能包含前端組件、後端 API 和資料庫設計。

## 功能特性

### 🎯 核心功能
- 用戶可以訂閱/取消訂閱主題和子主題
- 顯示訂閱狀態和訂閱人數統計
- 支援批量訂閱狀態檢查
- 提供用戶訂閱列表管理

### 🔐 安全性
- 使用 Supabase RLS (Row Level Security)
- 用戶只能管理自己的訂閱
- 支援匿名訪問統計資料

### 📊 統計功能
- 實時訂閱人數統計
- 用戶訂閱歷史記錄
- 訂閱趨勢分析支援

## 資料庫設計

### 主要表結構

```sql
-- 訂閱表
CREATE TABLE public.content_subscriptions (
  id         UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  item_type  TEXT NOT NULL CHECK (item_type IN ('topic', 'subtopic')),
  item_id    UUID NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE (profile_id, item_type, item_id)
);
```

### 索引優化
- `idx_content_subscriptions_profile_id`: 用戶訂閱查詢
- `idx_content_subscriptions_item`: 項目統計查詢
- `idx_content_subscriptions_profile_item`: 複合查詢優化

### 視圖和函數
- `subscription_details`: 訂閱詳情視圖
- `subscription_stats`: 訂閱統計視圖
- `get_user_subscription_count()`: 用戶訂閱統計函數
- `is_user_subscribed()`: 訂閱狀態檢查函數

## API 端點

### 1. 訂閱管理 `/api/subscriptions`

#### GET - 獲取用戶訂閱列表
```bash
GET /api/subscriptions?itemType=topic
```

#### POST - 訂閱/取消訂閱
```bash
POST /api/subscriptions
Content-Type: application/json

{
  "itemType": "topic",
  "itemId": "uuid-here",
  "action": "toggle"  // subscribe | unsubscribe | toggle
}
```

### 2. 訂閱狀態檢查 `/api/subscriptions/status`

```bash
GET /api/subscriptions/status?itemType=topic&itemId=uuid-here
```

### 3. 訂閱統計 `/api/subscriptions/stats`

```bash
GET /api/subscriptions/stats?itemType=topic&itemId=uuid-here
```

## 前端組件

### 1. SubscriptionButton
主要的訂閱按鈕組件，支援多種樣式和配置：

```tsx
<SubscriptionButton
  itemType="topic"
  itemId={topic.id}
  itemName={topic.name}
  showCount={true}
  size="sm"
  onSubscriptionChange={(subscribed) => {
    console.log('訂閱狀態:', subscribed)
  }}
/>
```

### 2. SubscriptionToggleButton
簡化版本，只顯示圖標：

```tsx
<SubscriptionToggleButton
  itemType="subtopic"
  itemId={subtopic.id}
  itemName={subtopic.name}
/>
```

### 3. useSubscription Hook
自定義 Hook 提供訂閱狀態管理：

```tsx
const {
  subscribed,
  subscriberCount,
  isLoading,
  toggle,
  subscribe,
  unsubscribe
} = useSubscription({
  itemType: 'topic',
  itemId: topic.id,
  itemName: topic.name,
  onSubscriptionChange: (subscribed) => {
    // 處理訂閱狀態變化
  }
})
```

## 整合示例

### 在主題頁面中使用

```tsx
// 在 topic-page-content.tsx 中
<SubscriptionButton
  itemType={activeSubtopic ? "subtopic" : "topic"}
  itemId={activeSubtopic 
    ? topic.subtopics?.find(sub => sub.name === activeSubtopic)?.id || ""
    : topic.id
  }
  itemName={activeSubtopic || topic.name}
  showCount={true}
  size="sm"
/>
```

## 部署步驟

### 1. 資料庫遷移
```bash
# 在 Supabase SQL Editor 中執行
psql -h your-supabase-host -U postgres -d postgres -f subscription_migration.sql
```

### 2. 環境設定
確保 Supabase 配置正確：
```env
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
```

### 3. 啟動開發服務器
```bash
bun run dev
```

## 測試

### 功能測試清單

#### ✅ 基本功能
- [ ] 用戶可以訂閱主題
- [ ] 用戶可以訂閱子主題
- [ ] 用戶可以取消訂閱
- [ ] 訂閱狀態正確顯示
- [ ] 訂閱人數統計正確

#### ✅ 安全性測試
- [ ] 用戶只能管理自己的訂閱
- [ ] 未登入用戶無法訂閱
- [ ] RLS 政策生效

#### ✅ 性能測試
- [ ] 大量訂閱時性能正常
- [ ] 統計查詢效率良好
- [ ] 索引使用正確

## 故障排除

### 常見問題

#### 1. 訂閱按鈕無法點擊
- 檢查用戶是否已登入
- 確認 itemId 不為空
- 檢查 API 回應狀態

#### 2. 訂閱統計不正確
- 確認 RLS 政策設定正確
- 檢查資料庫連接
- 驗證 SQL 查詢語法

#### 3. 性能問題
- 檢查索引是否正確建立
- 監控資料庫查詢計劃
- 考慮添加快取機制

## 未來擴展

### 計劃中的功能
1. **通知系統**: 訂閱項目有新內容時推送通知
2. **訂閱分類**: 支援訂閱不同類型的通知
3. **批量操作**: 支援批量訂閱/取消訂閱
4. **訂閱推薦**: 基於用戶行為推薦訂閱
5. **導出功能**: 支援訂閱列表導出

### 技術改進
1. **快取策略**: 添加 Redis 快取提升性能
2. **即時更新**: 使用 WebSocket 實現即時訂閱統計
3. **分析儀表板**: 管理員可查看訂閱趨勢分析
4. **A/B 測試**: 支援不同訂閱按鈕樣式測試

## 維護指南

### 定期維護任務
1. 清理過期訂閱記錄
2. 優化資料庫索引
3. 監控 API 性能
4. 更新統計視圖

### 監控指標
- 訂閱/取消訂閱比率
- API 回應時間
- 資料庫查詢性能
- 用戶參與度

---

**注意**: 本功能需要用戶已登入才能使用訂閱功能。未登入用戶仍可查看訂閱統計資料。 