# 內容卡片組件遷移指南

本文檔提供從舊的 `ViewpointCard` 和 `DiscussionPreview` 組件遷移到新的統一 `ContentCard` 組件的指南。

## 為什麼要統一？

- **一致的用戶體驗**：所有內容卡片具有一致的視覺風格和交互模式
- **減少重複代碼**：避免維護兩套類似的組件
- **更好的可維護性**：集中管理所有內容卡片的樣式和行為
- **更靈活的擴展性**：可以輕鬆添加新的內容類型或變體

## 遷移步驟

### 1. 從 ViewpointCard 遷移

**舊的用法**:
\`\`\`tsx
import { ViewpointCard } from "@/components/viewpoint-card"

<ViewpointCard
  id={1}
  semanticType="insight"
  title="標題"
  content="內容"
  topics={["主題"]}
  subtopics={["子主題"]}
  author={{ id: "user1", name: "作者", avatar: "/avatar.png" }}
  sourceType="community"
  timestamp="2小時前"
  stats={{ likes: 10, dislikes: 2, comments: 5, bookmarks: 3 }}
  isCompact={false}
/>
\`\`\`

**新的用法**:
\`\`\`tsx
import { ContentCard } from "@/components/content-card"

<ContentCard
  contentType="viewpoint"
  id={1}
  semanticType="insight"
  title="標題"
  content="內容"
  topics={["主題"]}
  subtopics={["子主題"]}
  author={{ id: "user1", name: "作者", avatar: "/avatar.png" }}
  sourceType="community"
  timestamp="2小時前"
  stats={{ likes: 10, dislikes: 2, comments: 5, bookmarks: 3 }}
  isCompact={false}
  variant="card" // 可選：card, compact, grid, quote
/>
\`\`\`

### 2. 從 DiscussionPreview 遷移

**舊的用法**:
\`\`\`tsx
import { DiscussionPreview } from "@/components/discussion-preview"

<DiscussionPreview
  id={1}
  title="標題"
  content="內容"
  author={{ id: "user1", name: "作者", avatar: "/avatar.png" }}
  timestamp="2小時前"
  tags={["標籤"]}
  topics={["主題"]}
  semanticType="discussion"
  stats={{ replies: 5, views: 100, likes: 10, dislikes: 2 }}
  variant="card"
/>
\`\`\`

**新的用法**:
\`\`\`tsx
import { ContentCard } from "@/components/content-card"

<ContentCard
  contentType="discussion"
  id={1}
  title="標題"
  content="內容"
  author={{ id: "user1", name: "作者", avatar: "/avatar.png" }}
  timestamp="2小時前"
  tags={["標籤"]}
  topics={["主題"]}
  semanticType="discussion"
  stats={{ replies: 5, views: 100, likes: 10, dislikes: 2 }}
  variant="card" // 可選：card, compact, grid, quote
/>
\`\`\`

### 3. 從 ViewpointCardGrid 遷移

**舊的用法**:
\`\`\`tsx
import { ViewpointCardGrid } from "@/components/viewpoint-card-grid"

<ViewpointCardGrid
  cards={cards}
  columns={3}
  gap="md"
  isCompact={false}
/>
\`\`\`

**新的用法**:
\`\`\`tsx
import { ContentCardGrid } from "@/components/content-card-grid"

<ContentCardGrid
  cards={cards.map(card => ({ ...card, contentType: "viewpoint" }))}
  columns={3}
  gap="md"
  isCompact={false}
/>
\`\`\`

## 變體對照表

| 舊變體 (ViewpointCard) | 舊變體 (DiscussionPreview) | 新變體 (ContentCard) |
|----------------------|--------------------------|-------------------|
| default              | card                     | card              |
| compact              | compact                  | compact           |
| grid                 | grid                     | grid              |
| -                    | quote                    | quote             |

## 語義類型

### 觀點卡語義類型
- insight (看法)
- experience (實測經驗)
- guide (工具教學)
- trap (踩坑警示)
- debate (爭議論點)
- concept (概念整理)

### 討論語義類型
- discussion (討論)
- question (問題)
- brainstorm (集思廣益)
- chat (閒聊)

## 示例頁面

查看 `/content-card-demo` 頁面，了解新組件的所有功能和變體。
