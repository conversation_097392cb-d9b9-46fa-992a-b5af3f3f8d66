 # Content Filter 模組使用指南

## 概述

本專案已將 filter 功能模組化，提供了可重複使用的元件和 hook，讓您可以在多個頁面中輕鬆實現內容過濾功能。

## 核心元件

### 1. `useContentFilter` Hook

自定義 hook，提供完整的過濾器狀態管理和邏輯。

```typescript
import { useContentFilter } from "@/hooks/use-content-filter"

const {
  filterState,
  filterActions,
  availableTopics,
  availableSubtopics,
  isLoadingFilters,
  hasActiveFilters,
  filterCount,
  applyFiltersToItems
} = useContentFilter()
```

#### 配置選項

```typescript
const config = {
  enableSemanticTypes: true,  // 啟用語義類型過濾
  enableTopics: true,         // 啟用主題過濾
  enableSubtopics: true,      // 啟用子主題過濾
  loadTopicsOnMount: true,    // 組件掛載時載入主題
  loadSubtopicsOnMount: true  // 組件掛載時載入子主題
}

const filter = useContentFilter(config)
```

### 2. `ContentFilter` 元件

過濾器 UI 元件，提供 Popover 介面讓用戶選擇過濾條件。

```tsx
import { ContentFilter } from "@/components/content-filter"

<ContentFilter
  filterState={filterState}
  filterActions={filterActions}
  availableTopics={availableTopics}
  availableSubtopics={availableSubtopics}
  isLoadingFilters={isLoadingFilters}
  showSemanticTypes={true}
  showTopics={true}
  showSubtopics={true}
  buttonText="過濾器"
  buttonVariant="outline"
  buttonSize="sm"
  showFilterCount={true}
  popoverAlign="end"
  popoverWidth="w-80"
/>
```

### 3. `FilterTags` 元件

顯示已選擇過濾器標籤的元件。

```tsx
import { FilterTags } from "@/components/content-filter"

<FilterTags
  filterState={filterState}
  filterActions={filterActions}
  availableTopics={availableTopics}
  availableSubtopics={availableSubtopics}
  showClearAll={true}
  onClearAll={() => {
    // 額外的清除邏輯
  }}
/>
```

## 使用範例

### 基本使用 (Library 頁面模式)

```tsx
"use client"

import { useState } from "react"
import { ContentFilter, FilterTags } from "@/components/content-filter"
import { useContentFilter } from "@/hooks/use-content-filter"

export default function MyPage() {
  const [searchQuery, setSearchQuery] = useState("")
  
  // 使用過濾器 hook
  const {
    filterState,
    filterActions,
    availableTopics,
    availableSubtopics,
    isLoadingFilters,
    hasActiveFilters,
    applyFiltersToItems
  } = useContentFilter()

  // 假設的資料項目
  const [items, setItems] = useState([
    {
      id: "1",
      title: "範例項目",
      semanticType: "insight",
      topics: ["AI", "機器學習"],
      subtopics: ["深度學習", "神經網路"]
    }
    // ... 更多項目
  ])

  // 應用過濾器
  const filteredItems = (() => {
    let result = items
    
    // 先應用模組化過濾器
    result = applyFiltersToItems(result)
    
    // 再應用搜尋過濾
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      result = result.filter(item => 
        item.title.toLowerCase().includes(query)
      )
    }
    
    return result
  })()

  return (
    <div className="container py-6">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-bold">內容列表</h1>
        
        <div className="flex items-center gap-2">
          <input
            type="search"
            placeholder="搜尋..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="px-3 py-2 border rounded"
          />
          
          <ContentFilter
            filterState={filterState}
            filterActions={filterActions}
            availableTopics={availableTopics}
            availableSubtopics={availableSubtopics}
            isLoadingFilters={isLoadingFilters}
          />
        </div>
      </div>

      {/* 顯示已選擇的過濾器 */}
      {(hasActiveFilters || searchQuery) && (
        <div className="mb-4">
          <FilterTags
            filterState={filterState}
            filterActions={filterActions}
            availableTopics={availableTopics}
            availableSubtopics={availableSubtopics}
            onClearAll={() => setSearchQuery("")}
          />
        </div>
      )}

      {/* 顯示過濾後的項目 */}
      <div className="grid gap-4">
        {filteredItems.map(item => (
          <div key={item.id} className="p-4 border rounded">
            <h3 className="font-semibold">{item.title}</h3>
            <p>語義類型: {item.semanticType}</p>
            <p>主題: {item.topics?.join(", ")}</p>
            <p>子主題: {item.subtopics?.join(", ")}</p>
          </div>
        ))}
      </div>
    </div>
  )
}
```

### 自定義配置使用

```tsx
// 僅啟用語義類型過濾
const semanticOnlyFilter = useContentFilter({
  enableSemanticTypes: true,
  enableTopics: false,
  enableSubtopics: false,
  loadTopicsOnMount: false,
  loadSubtopicsOnMount: false
})

// 僅顯示語義類型過濾器
<ContentFilter
  filterState={semanticOnlyFilter.filterState}
  filterActions={semanticOnlyFilter.filterActions}
  availableTopics={[]}
  availableSubtopics={[]}
  isLoadingFilters={false}
  showSemanticTypes={true}
  showTopics={false}
  showSubtopics={false}
  buttonText="類型"
  buttonVariant="secondary"
/>
```

### 與其他過濾邏輯整合

```tsx
const filteredItems = (() => {
  let result = items
  
  // 1. 應用模組化過濾器
  result = applyFiltersToItems(result)
  
  // 2. 應用自定義過濾邏輯
  if (activeCategory) {
    result = result.filter(item => item.categoryId === activeCategory)
  }
  
  // 3. 應用搜尋過濾
  if (searchQuery) {
    result = result.filter(item => 
      item.title.toLowerCase().includes(searchQuery.toLowerCase())
    )
  }
  
  return result
})()
```

## 介面定義

### FilterableItem

任何需要被過濾的項目都應該實現這個介面：

```typescript
interface FilterableItem {
  semanticType?: string    // 語義類型
  topics?: string[]        // 主題陣列
  subtopics?: string[]     // 子主題陣列
}
```

### 語義類型

支援的語義類型包括：

```typescript
const SEMANTIC_TYPES = [
  // 觀點卡語義類型
  { value: "insight", label: "看法" },
  { value: "experience", label: "實測經驗" },
  { value: "guide", label: "工具教學" },
  { value: "trap", label: "踩坑警示" },
  { value: "debate", label: "爭議論點" },
  { value: "concept", label: "概念整理" },
  // 討論語義類型
  { value: "discussion", label: "討論" },
  { value: "question", label: "問題" },
  { value: "brainstorm", label: "集思廣益" },
  { value: "chat", label: "閒聊" },
]
```

## 最佳實踐

1. **性能優化**: 使用 `useMemo` 或 `useCallback` 來優化過濾邏輯
2. **狀態管理**: 考慮將過濾器狀態持久化到 URL 參數中
3. **用戶體驗**: 提供清楚的視覺反饋，顯示當前活動的過濾器
4. **擴展性**: 透過配置選項來適應不同頁面的需求
5. **測試**: 確保過濾邏輯有適當的單元測試

## 已實現的頁面

- ✅ `/library` - 收藏庫頁面
- 🚧 `/explore` - 探索頁面 (正在遷移中)

## 未來規劃

- 支援日期範圍過濾
- 支援排序功能整合
- 支援自定義過濾器擴展
- 支援過濾器狀態的 URL 同步