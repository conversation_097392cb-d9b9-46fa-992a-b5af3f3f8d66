import { useState, useEffect, useCallback, useRef } from "react"

interface BatchItem {
    id: string
    type: "card" | "thread"
}

interface BatchStats {
    likes: number
    dislikes: number
    comments: number
    bookmarks: number
}

interface BatchUserStates {
    liked: boolean
    disliked: boolean
    bookmarked: boolean
}

interface UseBatchStatsOptions {
    items: BatchItem[]
    enabled: boolean
}

interface UseBatchStatsReturn {
    stats: Record<string, BatchStats>
    userStates: Record<string, BatchUserStates>
    isLoading: boolean
    error: string | null
    refetch: () => Promise<void>
}

// 全域快取和請求管理
const globalCache = new Map<string, { stats: BatchStats; userStates: BatchUserStates; timestamp: number }>()
const pendingRequests = new Map<string, Promise<any>>()
const CACHE_DURATION = 5 * 60 * 1000 // 5分鐘快取

// 生成快取鍵
function getCacheKey(items: BatchItem[]): string {
    return items.map(item => `${item.type}:${item.id}`).sort().join(',')
}

// 檢查快取是否有效
function isCacheValid(timestamp: number): boolean {
    return Date.now() - timestamp < CACHE_DURATION
}

// 批量請求函數
async function fetchBatchStats(items: BatchItem[]): Promise<{
    stats: Record<string, BatchStats>
    userStates: Record<string, BatchUserStates>
}> {
    if (items.length === 0) {
        return { stats: {}, userStates: {} }
    }

    const cacheKey = getCacheKey(items)

    // 如果有正在進行的請求，等待它完成
    if (pendingRequests.has(cacheKey)) {
        return pendingRequests.get(cacheKey)!
    }

    // 檢查快取
    const cachedData = globalCache.get(cacheKey)
    if (cachedData && isCacheValid(cachedData.timestamp)) {
        const result: { stats: Record<string, BatchStats>; userStates: Record<string, BatchUserStates> } = {
            stats: {},
            userStates: {}
        }
        items.forEach(item => {
            result.stats[item.id] = cachedData.stats
            result.userStates[item.id] = cachedData.userStates
        })
        return result
    }

    // 發起新請求
    const requestPromise = (async () => {
        try {
            const response = await fetch('/api/stats/batch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ items }),
            })

            if (!response.ok) {
                const errorData = await response.json()
                throw new Error(errorData.error || `HTTP ${response.status}`)
            }

            const data = await response.json()

            if (!data.success) {
                throw new Error(data.error || '獲取統計數據失敗')
            }

            // 更新快取
            const timestamp = Date.now()
            items.forEach(item => {
                const itemCacheKey = `${item.type}:${item.id}`
                globalCache.set(itemCacheKey, {
                    stats: data.data.stats[item.id] || { likes: 0, dislikes: 0, comments: 0, bookmarks: 0 },
                    userStates: data.data.userStates[item.id] || { liked: false, disliked: false, bookmarked: false },
                    timestamp
                })
            })

            return data.data
        } finally {
            pendingRequests.delete(cacheKey)
        }
    })()

    pendingRequests.set(cacheKey, requestPromise)
    return requestPromise
}

export function useBatchStats({ items, enabled = true }: UseBatchStatsOptions): UseBatchStatsReturn {
    const [stats, setStats] = useState<Record<string, BatchStats>>({})
    const [userStates, setUserStates] = useState<Record<string, BatchUserStates>>({})
    const [isLoading, setIsLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)

    // 使用 ref 來追踪最新的 items，避免不必要的重新渲染
    const itemsRef = useRef<BatchItem[]>(items)
    const enabledRef = useRef(enabled)

    // 更新 refs
    useEffect(() => {
        itemsRef.current = items
        enabledRef.current = enabled
    }, [items, enabled])

    // 獲取數據的函數
    const fetchData = useCallback(async () => {
        if (!enabledRef.current || itemsRef.current.length === 0) {
            return
        }

        setIsLoading(true)
        setError(null)

        try {
            const result = await fetchBatchStats(itemsRef.current)
            setStats(result.stats)
            setUserStates(result.userStates)
        } catch (err) {
            console.error('獲取批量統計數據錯誤:', err)
            setError(err instanceof Error ? err.message : '獲取統計數據失敗')
        } finally {
            setIsLoading(false)
        }
    }, [])

    // 初始加載和依賴變化時重新加載
    useEffect(() => {
        fetchData()
    }, [fetchData, JSON.stringify(items), enabled])

    // 手動重新獲取數據
    const refetch = useCallback(async () => {
        // 清除相關快取
        const cacheKey = getCacheKey(itemsRef.current)
        globalCache.delete(cacheKey)
        itemsRef.current.forEach(item => {
            globalCache.delete(`${item.type}:${item.id}`)
        })

        await fetchData()
    }, [fetchData])

    return {
        stats,
        userStates,
        isLoading,
        error,
        refetch
    }
}

// 為單個項目提供的便捷 Hook
export function useItemStats(id: string, type: "card" | "thread", enabled = true) {
    const { stats, userStates, isLoading, error, refetch } = useBatchStats({
        items: [{ id, type }],
        enabled
    })

    return {
        stats: stats[id] || { likes: 0, dislikes: 0, comments: 0, bookmarks: 0 },
        userStates: userStates[id] || { liked: false, disliked: false, bookmarked: false },
        isLoading,
        error,
        refetch
    }
}

// 清除所有快取的工具函數
export function clearBatchStatsCache() {
    globalCache.clear()
    pendingRequests.clear()
} 