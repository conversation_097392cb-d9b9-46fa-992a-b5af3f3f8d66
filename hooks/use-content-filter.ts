"use client"

import { useState, useEffect, useMemo } from "react"
import type { FilterState, FilterActions, Topic, Subtopic } from "@/components/content-filter"

// Filter 配置選項
export interface FilterConfig {
    enableSemanticTypes?: boolean
    enableTopics?: boolean
    enableSubtopics?: boolean
    loadTopicsOnMount?: boolean
    loadSubtopicsOnMount?: boolean
}

// Hook 返回值類型
export interface UseContentFilterReturn {
    // 狀態
    filterState: FilterState
    filterActions: FilterActions
    availableTopics: Topic[]
    availableSubtopics: Subtopic[]
    isLoadingFilters: boolean

    // 計算屬性
    hasActiveFilters: boolean
    filterCount: number

    // 工具函數
    loadFilterOptions: () => Promise<void>
    applyFiltersToItems: <T extends FilterableItem>(items: T[]) => T[]
}

// 可過濾項目的基本介面
export interface FilterableItem {
    semanticType?: string
    topics?: string[]
    subtopics?: string[]
}

// 預設配置
const defaultConfig: FilterConfig = {
    enableSemanticTypes: true,
    enableTopics: true,
    enableSubtopics: true,
    loadTopicsOnMount: true,
    loadSubtopicsOnMount: true,
}

/**
 * 內容過濾 Hook
 * 
 * @param config - 過濾器配置選項
 * @returns 過濾器狀態、操作函數和工具方法
 */
export function useContentFilter(config: FilterConfig = {}): UseContentFilterReturn {
    const finalConfig = { ...defaultConfig, ...config }

    // 基本狀態
    const [selectedSemanticTypes, setSelectedSemanticTypes] = useState<string[]>([])
    const [selectedTopics, setSelectedTopics] = useState<string[]>([])
    const [selectedSubtopics, setSelectedSubtopics] = useState<string[]>([])

    // 資料狀態
    const [availableTopics, setAvailableTopics] = useState<Topic[]>([])
    const [availableSubtopics, setAvailableSubtopics] = useState<Subtopic[]>([])
    const [isLoadingFilters, setIsLoadingFilters] = useState(false)

    // 載入過濾器選項
    const loadFilterOptions = async () => {
        if (!finalConfig.enableTopics && !finalConfig.enableSubtopics) return

        setIsLoadingFilters(true)
        try {
            const requests: Promise<Response>[] = []

            if (finalConfig.enableTopics) {
                requests.push(fetch('/api/topics?limit=50'))
            }

            if (finalConfig.enableSubtopics) {
                requests.push(fetch('/api/subtopics?limit=200'))
            }

            const responses = await Promise.all(requests)
            let topicsIndex = 0
            let subtopicsIndex = finalConfig.enableTopics ? 1 : 0

            // 處理主題數據
            if (finalConfig.enableTopics && responses[topicsIndex]) {
                const topicsResponse = responses[topicsIndex]
                if (topicsResponse.ok) {
                    const topicsData = await topicsResponse.json()
                    if (topicsData.success) {
                        setAvailableTopics(topicsData.data || [])
                    }
                }
            }

            // 處理子主題數據
            if (finalConfig.enableSubtopics && responses[subtopicsIndex]) {
                const subtopicsResponse = responses[subtopicsIndex]
                if (subtopicsResponse.ok) {
                    const subtopicsData = await subtopicsResponse.json()
                    if (subtopicsData.success) {
                        setAvailableSubtopics(subtopicsData.data || [])
                    }
                }
            }
        } catch (error) {
            console.error('Error loading filter options:', error)
        } finally {
            setIsLoadingFilters(false)
        }
    }

    // 在組件掛載時載入過濾器選項
    useEffect(() => {
        if (finalConfig.loadTopicsOnMount || finalConfig.loadSubtopicsOnMount) {
            loadFilterOptions()
        }
    }, [])

    // 過濾器操作函數
    const toggleSemanticType = (semanticType: string) => {
        if (!finalConfig.enableSemanticTypes) return

        setSelectedSemanticTypes(prev =>
            prev.includes(semanticType)
                ? prev.filter(type => type !== semanticType)
                : [...prev, semanticType]
        )
    }

    const toggleTopic = (topic: string) => {
        if (!finalConfig.enableTopics) return

        setSelectedTopics(prev =>
            prev.includes(topic)
                ? prev.filter(t => t !== topic)
                : [...prev, topic]
        )
    }

    const toggleSubtopic = (subtopic: string) => {
        if (!finalConfig.enableSubtopics) return

        setSelectedSubtopics(prev =>
            prev.includes(subtopic)
                ? prev.filter(s => s !== subtopic)
                : [...prev, subtopic]
        )
    }

    const clearFilters = () => {
        setSelectedSemanticTypes([])
        setSelectedTopics([])
        setSelectedSubtopics([])
    }

    // 狀態和操作對象
    const filterState: FilterState = {
        selectedSemanticTypes,
        selectedTopics,
        selectedSubtopics,
    }

    const filterActions: FilterActions = {
        toggleSemanticType,
        toggleTopic,
        toggleSubtopic,
        clearFilters,
    }

    // 計算屬性
    const filterCount = useMemo(() =>
        selectedSemanticTypes.length + selectedTopics.length + selectedSubtopics.length,
        [selectedSemanticTypes.length, selectedTopics.length, selectedSubtopics.length]
    )

    const hasActiveFilters = filterCount > 0

    // 應用過濾器到項目列表
    const applyFiltersToItems = <T extends FilterableItem>(items: T[]): T[] => {
        return items.filter((item) => {
            // 語義類型過濾
            if (finalConfig.enableSemanticTypes && selectedSemanticTypes.length > 0) {
                if (!item.semanticType || !selectedSemanticTypes.includes(item.semanticType)) {
                    return false
                }
            }

            // 主題過濾
            if (finalConfig.enableTopics && selectedTopics.length > 0) {
                const hasTopicMatch = selectedTopics.some(selectedTopic =>
                    item.topics?.includes(selectedTopic)
                )
                if (!hasTopicMatch) {
                    return false
                }
            }

            // 子主題過濾
            if (finalConfig.enableSubtopics && selectedSubtopics.length > 0) {
                const hasSubtopicMatch = selectedSubtopics.some(selectedSubtopic =>
                    item.subtopics?.includes(selectedSubtopic)
                )
                if (!hasSubtopicMatch) {
                    return false
                }
            }

            return true
        })
    }

    return {
        filterState,
        filterActions,
        availableTopics,
        availableSubtopics,
        isLoadingFilters,
        hasActiveFilters,
        filterCount,
        loadFilterOptions,
        applyFiltersToItems,
    }
} 