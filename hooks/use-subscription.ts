"use client"

import { useState, useEffect, useCallback } from "react"
import { useToast } from "@/components/ui/use-toast"

export type SubscriptionItemType = 'topic' | 'subtopic'

export interface UseSubscriptionOptions {
    itemType: SubscriptionItemType
    itemId: string
    itemName: string
    autoLoad?: boolean
    onSubscriptionChange?: (subscribed: boolean) => void
}

export interface SubscriptionData {
    subscribed: boolean
    subscription?: any
    subscriberCount?: number
}

export function useSubscription({
    itemType,
    itemId,
    itemName,
    autoLoad = true,
    onSubscriptionChange
}: UseSubscriptionOptions) {
    const [data, setData] = useState<SubscriptionData>({
        subscribed: false,
        subscriberCount: 0
    })
    const [isLoading, setIsLoading] = useState(false)
    const [isInitialLoading, setIsInitialLoading] = useState(autoLoad)
    const { toast } = useToast()

    // 載入訂閱狀態
    const loadStatus = useCallback(async () => {
        try {
            const response = await fetch(
                `/api/subscriptions/status?itemType=${itemType}&itemId=${itemId}`
            )
            const result = await response.json()

            if (result.success) {
                setData(prev => ({
                    ...prev,
                    subscribed: result.data.subscribed,
                    subscription: result.data.subscription
                }))
            } else {
                console.error("載入訂閱狀態失敗:", result.error)
            }
        } catch (error) {
            console.error("載入訂閱狀態錯誤:", error)
        }
    }, [itemType, itemId])

    // 載入訂閱統計
    const loadStats = useCallback(async () => {
        try {
            const response = await fetch(
                `/api/subscriptions/stats?itemType=${itemType}&itemId=${itemId}`
            )
            const result = await response.json()

            if (result.success) {
                setData(prev => ({
                    ...prev,
                    subscriberCount: result.data.subscriber_count
                }))
            } else {
                console.error("載入訂閱統計失敗:", result.error)
            }
        } catch (error) {
            console.error("載入訂閱統計錯誤:", error)
        }
    }, [itemType, itemId])

    // 載入所有數據
    const loadAll = useCallback(async () => {
        if (isInitialLoading) {
            setIsInitialLoading(true)
        }

        try {
            await Promise.all([loadStatus(), loadStats()])
        } finally {
            setIsInitialLoading(false)
        }
    }, [loadStatus, loadStats, isInitialLoading])

    // 切換訂閱狀態
    const toggle = useCallback(async () => {
        if (isLoading) return

        setIsLoading(true)
        try {
            const response = await fetch('/api/subscriptions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    itemType,
                    itemId,
                    action: 'toggle',
                }),
            })

            const result = await response.json()

            if (result.success) {
                const newSubscribedState = result.data.subscribed

                setData(prev => ({
                    ...prev,
                    subscribed: newSubscribedState,
                    subscription: result.data.subscription,
                    subscriberCount: prev.subscriberCount
                        ? (newSubscribedState ? prev.subscriberCount + 1 : Math.max(0, prev.subscriberCount - 1))
                        : prev.subscriberCount
                }))

                // 回調父組件
                onSubscriptionChange?.(newSubscribedState)

                // 顯示成功訊息
                toast({
                    title: newSubscribedState ? "訂閱成功" : "取消訂閱成功",
                    description: newSubscribedState
                        ? `您已成功關注 ${itemName}`
                        : `您已取消關注 ${itemName}`,
                })

                return { success: true, subscribed: newSubscribedState }
            } else {
                throw new Error(result.error || "操作失敗")
            }
        } catch (error) {
            console.error("訂閱操作錯誤:", error)
            toast({
                title: "操作失敗",
                description: error instanceof Error ? error.message : "請稍後再試",
                variant: "destructive",
            })

            return { success: false, error: error instanceof Error ? error.message : "操作失敗" }
        } finally {
            setIsLoading(false)
        }
    }, [itemType, itemId, itemName, isLoading, onSubscriptionChange, toast])

    // 訂閱
    const subscribe = useCallback(async () => {
        if (isLoading || data.subscribed) return

        setIsLoading(true)
        try {
            const response = await fetch('/api/subscriptions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    itemType,
                    itemId,
                    action: 'subscribe',
                }),
            })

            const result = await response.json()

            if (result.success) {
                setData(prev => ({
                    ...prev,
                    subscribed: true,
                    subscription: result.data.subscription,
                    subscriberCount: prev.subscriberCount ? prev.subscriberCount + 1 : prev.subscriberCount
                }))

                onSubscriptionChange?.(true)

                toast({
                    title: "訂閱成功",
                    description: `您已成功關注 ${itemName}`,
                })

                return { success: true, subscribed: true }
            } else {
                throw new Error(result.error || "訂閱失敗")
            }
        } catch (error) {
            console.error("訂閱錯誤:", error)
            toast({
                title: "訂閱失敗",
                description: error instanceof Error ? error.message : "請稍後再試",
                variant: "destructive",
            })

            return { success: false, error: error instanceof Error ? error.message : "訂閱失敗" }
        } finally {
            setIsLoading(false)
        }
    }, [itemType, itemId, itemName, isLoading, data.subscribed, onSubscriptionChange, toast])

    // 取消訂閱
    const unsubscribe = useCallback(async () => {
        if (isLoading || !data.subscribed) return

        setIsLoading(true)
        try {
            const response = await fetch('/api/subscriptions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    itemType,
                    itemId,
                    action: 'unsubscribe',
                }),
            })

            const result = await response.json()

            if (result.success) {
                setData(prev => ({
                    ...prev,
                    subscribed: false,
                    subscription: undefined,
                    subscriberCount: prev.subscriberCount ? Math.max(0, prev.subscriberCount - 1) : prev.subscriberCount
                }))

                onSubscriptionChange?.(false)

                toast({
                    title: "取消訂閱成功",
                    description: `您已取消關注 ${itemName}`,
                })

                return { success: true, subscribed: false }
            } else {
                throw new Error(result.error || "取消訂閱失敗")
            }
        } catch (error) {
            console.error("取消訂閱錯誤:", error)
            toast({
                title: "取消訂閱失敗",
                description: error instanceof Error ? error.message : "請稍後再試",
                variant: "destructive",
            })

            return { success: false, error: error instanceof Error ? error.message : "取消訂閱失敗" }
        } finally {
            setIsLoading(false)
        }
    }, [itemType, itemId, itemName, isLoading, data.subscribed, onSubscriptionChange, toast])

    // 重新載入數據
    const refresh = useCallback(() => {
        loadAll()
    }, [loadAll])

    // 初始載入
    useEffect(() => {
        if (autoLoad) {
            loadAll()
        }
    }, [autoLoad, loadAll])

    return {
        // 狀態
        subscribed: data.subscribed,
        subscription: data.subscription,
        subscriberCount: data.subscriberCount,
        isLoading,
        isInitialLoading,

        // 操作方法
        toggle,
        subscribe,
        unsubscribe,
        refresh,
        loadStatus,
        loadStats,
    }
} 