import { createBrowserClient } from "@/lib/supabase/client"
import type { SupabaseClient } from "@supabase/supabase-js"

// 檢查是否為客戶端環境
export function isClient() {
  return typeof window !== "undefined"
}

// 獲取客戶端 Supabase 客戶端
export function getClientSupabase() {
  return createBrowserClient()
}

// 客戶端版本的 Supabase 獲取函數
export function getSupabase() {
  return createBrowserClient()
}

// API 響應類型
export type ApiResponse<T> = {
  success: boolean
  data: T | null
  error?: string
  message?: string
  count?: number
  meta?: {
    total?: number
    page?: number
    limit?: number
  }
}

// 分頁參數類型
export interface PaginationParams {
  page?: number
  limit?: number
}

// 計算分頁偏移量
export function calculateOffset(page: number, limit: number) {
  return (page - 1) * limit
}

// 創建成功響應
export function successResponse<T>(
  data: T,
  meta?: number | { total?: number; page?: number; limit?: number },
  message?: string
): ApiResponse<T> {
  const baseResponse = {
    success: true,
    data,
    message,
  }

  // 處理分頁元數據
  if (typeof meta === 'number') {
    return {
      ...baseResponse,
      count: meta,
    }
  } else if (meta && typeof meta === 'object') {
    return {
      ...baseResponse,
      count: meta.total,
      meta,
    }
  }

  return baseResponse as ApiResponse<T>
}

// 創建錯誤響應
export function errorResponse<T = null>(error: string, status = 400): ApiResponse<T> {
  return {
    success: false,
    data: null,
    error,
  } as ApiResponse<T>
}

// 處理錯誤
export function handleError<T = null>(error: any): ApiResponse<T> {
  console.error("API Error:", error)

  // 處理 Supabase 錯誤
  if (error?.code === "429" || (error?.message && error.message.includes("Too Many Requests"))) {
    return errorResponse("請求過於頻繁，請稍後再試", 429)
  }

  // 處理 JSON 解析錯誤
  if (error instanceof SyntaxError && error.message.includes("Unexpected token")) {
    return errorResponse("無效的響應格式，請稍後再試", 400)
  }

  // 處理網絡錯誤
  if (error?.code === "ECONNREFUSED" || error?.code === "ETIMEDOUT") {
    return errorResponse("無法連接到服務器，請檢查您的網絡連接", 503)
  }

  // 處理認證錯誤
  if (error?.code === "PGRST301" || error?.message?.includes("JWT")) {
    return errorResponse("認證已過期，請重新登入", 401)
  }

  return {
    success: false,
    data: null,
    error: error.message || "發生未知錯誤",
  } as ApiResponse<T>
}

// 安全獲取用戶函數 - 處理 AuthSessionMissingError
export async function safeGetUser(supabase: SupabaseClient) {
  try {
    console.log('safeGetUser: 開始獲取用戶...')
    const { data: { user }, error } = await supabase.auth.getUser()

    console.log('safeGetUser: 結果', {
      hasUser: !!user,
      userId: user?.id?.slice(0, 8),
      errorName: error?.name,
      errorMessage: error?.message
    })

    if (error) {
      // 檢查是否為 AuthSessionMissingError
      const isAuthError = error.name === 'AuthSessionMissingError' ||
        error.message?.includes('Auth session missing') ||
        error.message?.includes('JWT')

      console.log('safeGetUser: 認證錯誤', { isAuthError, errorName: error.name })

      return {
        user: null,
        error,
        isAuthError
      }
    }

    return {
      user,
      error: null,
      isAuthError: false
    }
  } catch (error: any) {
    // 捕獲任何拋出的錯誤
    const isAuthError = error.name === 'AuthSessionMissingError' ||
      error.message?.includes('Auth session missing') ||
      error.message?.includes('JWT')

    console.log('safeGetUser: 捕獲錯誤', { isAuthError, errorName: error.name, errorMessage: error.message })

    return {
      user: null,
      error,
      isAuthError
    }
  }
}
