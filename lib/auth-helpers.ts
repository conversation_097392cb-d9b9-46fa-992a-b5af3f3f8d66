import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import { checkUserPermission, Permission } from "@/lib/permission-service"

/**
 * 認證和權限檢查的輔助函數
 * 配合現有的 safeGetUser 模式
 */

export interface AuthResult {
  success: boolean
  user?: any
  response?: NextResponse
}

/**
 * 標準的用戶認證檢查
 * 使用與 my-posts API 相同的模式
 */
export async function authenticateUser(): Promise<AuthResult> {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)

    // 檢查用戶是否已登入 - 使用 safeGetUser 處理 AuthSessionMissingError
    const { safeGetUser } = await import("@/lib/api-utils")
    const { user, error: userError, isAuthError } = await safeGetUser(supabase)

    if (userError || isAuthError) {
      return {
        success: false,
        response: NextResponse.json(
          { success: false, error: "認證失敗，請重新登入" },
          { status: 401 }
        )
      }
    }

    if (!user) {
      return {
        success: false,
        response: NextResponse.json(
          { success: false, error: "未登入" },
          { status: 401 }
        )
      }
    }

    return {
      success: true,
      user
    }
  } catch (error: any) {
    console.error('認證檢查時出錯:', error)
    return {
      success: false,
      response: NextResponse.json(
        { success: false, error: "內部伺服器錯誤" },
        { status: 500 }
      )
    }
  }
}

/**
 * 認證 + 權限檢查的組合函數
 */
export async function authenticateAndCheckPermission(permission: Permission): Promise<AuthResult> {
  const authResult = await authenticateUser()
  
  if (!authResult.success) {
    return authResult
  }

  try {
    // 檢查權限
    const permissionCheck = await checkUserPermission(authResult.user!.id, permission)
    
    if (!permissionCheck.success) {
      return {
        success: false,
        response: NextResponse.json(
          { success: false, error: "權限檢查失敗" },
          { status: 500 }
        )
      }
    }

    if (!permissionCheck.hasPermission) {
      return {
        success: false,
        response: NextResponse.json(
          { success: false, error: "權限不足" },
          { status: 403 }
        )
      }
    }

    return {
      success: true,
      user: authResult.user
    }
  } catch (error: any) {
    console.error('權限檢查時出錯:', error)
    return {
      success: false,
      response: NextResponse.json(
        { success: false, error: "權限檢查失敗" },
        { status: 500 }
      )
    }
  }
}

/**
 * 檢查管理員權限（可以訪問管理面板）
 */
export async function requireAdmin(): Promise<AuthResult> {
  return await authenticateAndCheckPermission('view_admin_panel')
}

/**
 * 檢查站長權限
 */
export async function requireOwner(): Promise<AuthResult> {
  return await authenticateAndCheckPermission('manage_all_content')
}

/**
 * 檢查版主管理權限
 */
export async function requireModeratorManager(): Promise<AuthResult> {
  return await authenticateAndCheckPermission('manage_moderators')
}

/**
 * 檢查內容審核權限
 */
export async function requireContentModerator(): Promise<AuthResult> {
  return await authenticateAndCheckPermission('approve_content')
}

/**
 * 檢查用戶是否為內容作者或有管理權限
 */
export async function requireAuthorOrModerator(
  contentType: 'card' | 'thread',
  contentId: string
): Promise<AuthResult & { isAuthor?: boolean; isModerator?: boolean }> {
  const authResult = await authenticateUser()
  
  if (!authResult.success) {
    return authResult
  }

  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)
    const userId = authResult.user!.id

    // 檢查是否為內容作者
    const tableName = contentType === 'card' ? 'cards' : 'threads'
    const { data: content, error } = await supabase
      .from(tableName)
      .select('author_id')
      .eq('id', contentId)
      .single()

    if (error || !content) {
      return {
        success: false,
        response: NextResponse.json(
          { success: false, error: "內容不存在" },
          { status: 404 }
        )
      }
    }

    const isAuthor = content.author_id === userId

    // 檢查是否有管理權限
    const { checkContentManagementPermission } = await import("@/lib/permission-service")
    const moderatorResult = await checkContentManagementPermission(userId, contentType, contentId)
    const isModerator = moderatorResult.success && moderatorResult.hasPermission

    if (!isAuthor && !isModerator) {
      return {
        success: false,
        response: NextResponse.json(
          { success: false, error: "權限不足" },
          { status: 403 }
        )
      }
    }

    return {
      success: true,
      user: authResult.user,
      isAuthor,
      isModerator
    }
  } catch (error: any) {
    console.error('檢查作者或版主權限時出錯:', error)
    return {
      success: false,
      response: NextResponse.json(
        { success: false, error: "權限檢查失敗" },
        { status: 500 }
      )
    }
  }
}

/**
 * 使用範例：
 * 
 * // 基本認證
 * const authResult = await authenticateUser()
 * if (!authResult.success) return authResult.response
 * 
 * // 認證 + 權限檢查
 * const adminResult = await requireAdmin()
 * if (!adminResult.success) return adminResult.response
 * 
 * // 內容作者或版主檢查
 * const authorResult = await requireAuthorOrModerator('card', cardId)
 * if (!authorResult.success) return authorResult.response
 */
