import { createBrowserClient } from "./supabase/client"

/**
 * 強制同步身份驗證狀態到服務端
 * 主要用於解決生產環境下客戶端與服務端認證狀態不同步的問題
 */
export async function forceAuthSync(): Promise<{ success: boolean; error?: string }> {
    try {
        const supabase = createBrowserClient()

        // 1. 刷新當前 session
        const { data: { session }, error: refreshError } = await supabase.auth.refreshSession()

        if (refreshError) {
            console.error('Auth sync - refresh session error:', refreshError)
            return { success: false, error: refreshError.message }
        }

        if (!session) {
            console.warn('Auth sync - no session found after refresh')
            return { success: false, error: 'No session found' }
        }

        // 2. 驗證用戶狀態
        const { data: { user }, error: userError } = await supabase.auth.getUser()

        if (userError || !user) {
            console.error('Auth sync - get user error:', userError)
            return { success: false, error: userError?.message || 'User not found' }
        }

        // 3. 在生產環境中額外等待，確保 cookies 完全設置
        if (process.env.NODE_ENV === 'production') {
            await new Promise(resolve => setTimeout(resolve, 1000))
        }

        // 4. 驗證服務端是否能正確讀取認證狀態
        try {
            const response = await fetch('/api/debug/auth-status', {
                credentials: 'include',
                headers: {
                    'Cache-Control': 'no-cache'
                }
            })

            const result = await response.json()

            if (!result.success || !result.data?.serverSide?.user) {
                console.error('Auth sync - server-side validation failed:', result)
                return { success: false, error: 'Server-side validation failed' }
            }

            console.log('Auth sync - successful:', {
                clientUserId: user.id.slice(0, 8),
                serverUserId: result.data.serverSide.user.id.slice(0, 8)
            })

        } catch (validationError) {
            console.warn('Auth sync - server validation error (proceeding anyway):', validationError)
        }

        return { success: true }

    } catch (error: any) {
        console.error('Auth sync - unexpected error:', error)
        return { success: false, error: error.message || 'Unexpected error' }
    }
}

/**
 * 帶重試機制的身份驗證同步
 */
export async function forceAuthSyncWithRetry(maxRetries = 3): Promise<{ success: boolean; error?: string }> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        console.log(`Auth sync attempt ${attempt}/${maxRetries}`)

        const result = await forceAuthSync()

        if (result.success) {
            return result
        }

        if (attempt < maxRetries) {
            // 指數退避重試
            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000)
            console.log(`Auth sync failed, retrying in ${delay}ms...`)
            await new Promise(resolve => setTimeout(resolve, delay))
        }
    }

    return { success: false, error: `Auth sync failed after ${maxRetries} attempts` }
} 