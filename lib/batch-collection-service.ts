import { getSupabase } from "./api-utils"

export interface BatchCollectionItem {
    id: string
    type: "card" | "thread"
}

export interface CollectionAssociation {
    collection_id: string
    collections: {
        id: string
        name: string
        user_id: string
    }
}

export interface BatchCollectionResult {
    itemCollections: Record<string, string[]> // item_id -> collection_ids[]
    collectionDetails: Record<string, { id: string; name: string; user_id: string }>
}

// 批量收藏關聯服務
export class BatchCollectionService {
    private static instance: BatchCollectionService
    private cache = new Map<string, { data: BatchCollectionResult; timestamp: number }>()
    private readonly CACHE_DURATION = 5 * 60 * 1000 // 5分鐘快取

    static getInstance(): BatchCollectionService {
        if (!BatchCollectionService.instance) {
            BatchCollectionService.instance = new BatchCollectionService()
        }
        return BatchCollectionService.instance
    }

    private getCacheKey(items: BatchCollectionItem[], userId?: string): string {
        const itemsKey = items.map(item => `${item.type}:${item.id}`).sort().join(',')
        return `collections:${itemsKey}:${userId || 'anonymous'}`
    }

    // 批量獲取收藏關聯
    async getBatchCollectionAssociations(items: BatchCollectionItem[], userId: string): Promise<BatchCollectionResult> {
        if (items.length === 0) {
            return { itemCollections: {}, collectionDetails: {} }
        }

        const cacheKey = this.getCacheKey(items, userId)

        // 檢查快取
        const cached = this.cache.get(cacheKey)
        if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
            return cached.data
        }

        const supabase = getSupabase()
        const itemIds = items.map(item => item.id)
        const itemCollections: Record<string, string[]> = {}
        const collectionDetails: Record<string, { id: string; name: string; user_id: string }> = {}

        // 初始化結果
        items.forEach(item => {
            itemCollections[item.id] = []
        })

        try {
            // 批量查詢所有項目的收藏關聯
            const { data: collectionItems, error } = await supabase
                .from("collection_items")
                .select(`
                    collection_id,
                    item_id,
                    item_type,
                    collections!inner (
                        id,
                        name,
                        user_id
                    )
                `)
                .in("item_id", itemIds)
                .eq("collections.user_id", userId) // 只查詢屬於當前用戶的收藏牆

            if (error) {
                console.error("批量查詢收藏關聯錯誤:", error)
                throw error
            }

            // 處理查詢結果
            if (collectionItems) {
                collectionItems.forEach((item: any) => {
                    const itemId = item.item_id
                    const collectionId = item.collection_id
                    const collection = item.collections

                    // 添加到項目的收藏牆列表
                    if (itemCollections[itemId]) {
                        itemCollections[itemId].push(collectionId)
                    } else {
                        itemCollections[itemId] = [collectionId]
                    }

                    // 儲存收藏牆詳情
                    collectionDetails[collectionId] = {
                        id: collection.id,
                        name: collection.name,
                        user_id: collection.user_id
                    }
                })
            }

            const result = { itemCollections, collectionDetails }

            // 快取結果
            this.cache.set(cacheKey, {
                data: result,
                timestamp: Date.now()
            })

            return result
        } catch (error) {
            console.error("批量收藏關聯查詢錯誤:", error)
            return { itemCollections, collectionDetails }
        }
    }

    // 清除特定項目的快取
    clearItemCache(items: BatchCollectionItem[], userId?: string): void {
        const cacheKey = this.getCacheKey(items, userId)
        this.cache.delete(cacheKey)
    }

    // 清除所有快取
    clearCache(): void {
        this.cache.clear()
    }
}

// 便利函數：批量獲取收藏關聯
export async function getBatchCollectionAssociations(
    items: BatchCollectionItem[],
    userId: string
): Promise<BatchCollectionResult> {
    const service = BatchCollectionService.getInstance()
    return service.getBatchCollectionAssociations(items, userId)
}

// 便利函數：豐富項目數據（添加收藏關聯信息）
export async function enrichWithCollectionAssociations<T extends { id: string; type?: string; contentType?: string }>(
    items: T[],
    userId: string
): Promise<(T & { collectionIds: string[] })[]> {
    // 轉換為 BatchCollectionItem 格式
    const batchItems: BatchCollectionItem[] = items.map(item => ({
        id: item.id,
        // 優先使用 type，若無則從 contentType 推導
        type: (item.type === "card" || item.type === "thread")
            ? item.type
            : (item.contentType === "viewpoint" ? "card" : "thread")
    }))

    const result = await getBatchCollectionAssociations(batchItems, userId)

    // 將收藏關聯數據合併到原始項目中
    return items.map(item => ({
        ...item,
        collectionIds: result.itemCollections[item.id] || []
    }))
} 