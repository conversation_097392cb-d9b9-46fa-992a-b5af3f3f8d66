import { getSupabase } from "./api-utils"

export interface BatchItem {
    id: string
    type: "card" | "thread" | "comment"
}

export interface BatchStats {
    likes: number
    dislikes: number
    comments: number
    replies: number
    bookmarks: number
    views: number
}

export interface BatchUserStates {
    liked: boolean
    disliked: boolean
    bookmarked: boolean
}

export interface BatchStatsResult {
    stats: Record<string, BatchStats>
    userStates: Record<string, BatchUserStates>
}

// 通用的批量統計獲取器
export class BatchStatsService {
    private static instance: BatchStatsService
    private cache = new Map<string, { data: BatchStatsResult; timestamp: number }>()
    private readonly CACHE_DURATION = 5 * 60 * 1000 // 5分鐘快取

    static getInstance(): BatchStatsService {
        if (!BatchStatsService.instance) {
            BatchStatsService.instance = new BatchStatsService()
        }
        return BatchStatsService.instance
    }

    private getCacheKey(items: BatchItem[], userId?: string): string {
        const itemsKey = items.map(item => `${item.type}:${item.id}`).sort().join(',')
        return `${itemsKey}:${userId || 'anonymous'}`
    }

    // 批量獲取統計數據
    async getBatchStats(items: BatchItem[], userId?: string): Promise<BatchStatsResult> {
        if (items.length === 0) {
            return { stats: {}, userStates: {} }
        }

        const cacheKey = this.getCacheKey(items, userId)

        // 檢查快取
        const cached = this.cache.get(cacheKey)
        if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
            return cached.data
        }

        const supabase = getSupabase()
        const itemIds = items.map(item => item.id)
        const cardIds = items.filter(item => item.type === "card").map(item => item.id)
        const threadIds = items.filter(item => item.type === "thread").map(item => item.id)
        const commentIds = items.filter(item => item.type === "comment").map(item => item.id)

        const stats: Record<string, BatchStats> = {}
        const userStates: Record<string, BatchUserStates> = {}

        // 初始化統計數據
        items.forEach(item => {
            stats[item.id] = { likes: 0, dislikes: 0, comments: 0, replies: 0, bookmarks: 0, views: 0 }
            userStates[item.id] = { liked: false, disliked: false, bookmarked: false }
        })

        try {
            // 並行獲取所有統計數據
            const [
                reactionsData,
                commentsData,
                bookmarksData,
                viewsData,
                userReactionsData,
                userBookmarksData
            ] = await Promise.all([
                // 獲取反應統計
                this.getReactionsStats(supabase, itemIds, items.map(item => item.type)),

                // 獲取評論/回覆統計
                this.getCommentsStats(supabase, cardIds, threadIds, commentIds),

                // 獲取收藏統計
                this.getBookmarksStats(supabase, itemIds, items.map(item => item.type)),

                // 獲取瀏覽次數（僅針對討論串）
                this.getViewsStats(supabase, threadIds),

                // 獲取用戶反應狀態
                userId ? this.getUserReactionsState(supabase, itemIds, items.map(item => item.type), userId) : [],

                // 獲取用戶收藏狀態
                userId ? this.getUserBookmarksState(supabase, itemIds, items.map(item => item.type), userId) : []
            ])

            // 處理反應統計
            reactionsData.forEach((reaction: any) => {
                if (stats[reaction.item_id]) {
                    if (reaction.reaction_type === "like") {
                        stats[reaction.item_id].likes++
                    } else if (reaction.reaction_type === "dislike") {
                        stats[reaction.item_id].dislikes++
                    }
                }
            })

            // 處理評論統計
            commentsData.forEach((comment: any) => {
                const itemId = comment.root_item_id || comment.parent_comment_id
                if (stats[itemId]) {
                    if (comment.root_item_type === "card") {
                        stats[itemId].comments++
                    } else if (comment.root_item_type === "thread") {
                        stats[itemId].replies++
                    } else if (comment.parent_comment_id) {
                        stats[itemId].replies++
                    }
                }
            })

            // 處理收藏統計
            bookmarksData.forEach((bookmark: any) => {
                if (stats[bookmark.item_id]) {
                    stats[bookmark.item_id].bookmarks++
                }
            })

            // 處理瀏覽次數
            viewsData.forEach((view: any) => {
                if (stats[view.thread_id]) {
                    stats[view.thread_id].views = view.count || 0
                }
            })

            // 處理用戶反應狀態
            userReactionsData.forEach((reaction: any) => {
                if (userStates[reaction.item_id]) {
                    if (reaction.reaction_type === "like") {
                        userStates[reaction.item_id].liked = true
                    } else if (reaction.reaction_type === "dislike") {
                        userStates[reaction.item_id].disliked = true
                    }
                }
            })

            // 處理用戶收藏狀態
            userBookmarksData.forEach((bookmark: any) => {
                if (userStates[bookmark.item_id]) {
                    userStates[bookmark.item_id].bookmarked = true
                }
            })

            const result = { stats, userStates }

            // 快取結果
            this.cache.set(cacheKey, {
                data: result,
                timestamp: Date.now()
            })

            return result
        } catch (error) {
            console.error("批量統計獲取錯誤:", error)
            return { stats, userStates }
        }
    }

    private async getReactionsStats(supabase: any, itemIds: string[], itemTypes: string[]) {
        if (itemIds.length === 0) return []

        const { data, error } = await supabase
            .from("reactions")
            .select("item_id, item_type, reaction_type")
            .in("item_id", itemIds)
            .in("item_type", [...new Set(itemTypes)])

        if (error) throw error
        return data || []
    }

    private async getCommentsStats(supabase: any, cardIds: string[], threadIds: string[], commentIds: string[]) {
        const queries = []

        if (cardIds.length > 0) {
            queries.push(
                supabase
                    .from("comments")
                    .select("root_item_id, root_item_type")
                    .eq("root_item_type", "card")
                    .in("root_item_id", cardIds)
            )
        }

        if (threadIds.length > 0) {
            queries.push(
                supabase
                    .from("comments")
                    .select("root_item_id, root_item_type")
                    .eq("root_item_type", "thread")
                    .in("root_item_id", threadIds)
            )
        }

        if (commentIds.length > 0) {
            queries.push(
                supabase
                    .from("comments")
                    .select("parent_comment_id")
                    .in("parent_comment_id", commentIds)
            )
        }

        if (queries.length === 0) return []

        const results = await Promise.all(queries)
        return results.flatMap(result => result.data || [])
    }

    private async getBookmarksStats(supabase: any, itemIds: string[], itemTypes: string[]) {
        if (itemIds.length === 0) return []

        const { data, error } = await supabase
            .from("bookmarks")
            .select("item_id, item_type")
            .in("item_id", itemIds)
            .in("item_type", [...new Set(itemTypes)])

        if (error) throw error
        return data || []
    }

    private async getViewsStats(supabase: any, threadIds: string[]) {
        if (threadIds.length === 0) return []

        const { data, error } = await supabase
            .from("thread_views")
            .select("thread_id, count")
            .in("thread_id", threadIds)

        if (error) throw error
        return data || []
    }

    private async getUserReactionsState(supabase: any, itemIds: string[], itemTypes: string[], userId: string) {
        if (itemIds.length === 0 || !userId) return []

        const { data, error } = await supabase
            .from("reactions")
            .select("item_id, item_type, reaction_type")
            .in("item_id", itemIds)
            .in("item_type", [...new Set(itemTypes)])
            .eq("profile_id", userId)

        if (error) throw error
        return data || []
    }

    private async getUserBookmarksState(supabase: any, itemIds: string[], itemTypes: string[], userId: string) {
        if (itemIds.length === 0 || !userId) return []

        const { data, error } = await supabase
            .from("bookmarks")
            .select("item_id, item_type")
            .in("item_id", itemIds)
            .in("item_type", [...new Set(itemTypes)])
            .eq("profile_id", userId)

        if (error) throw error
        return data || []
    }

    // 清除快取
    clearCache(): void {
        this.cache.clear()
    }

    // 清除特定項目的快取
    clearItemCache(items: BatchItem[]): void {
        const keysToDelete = []
        for (const [key, value] of this.cache.entries()) {
            if (items.some(item => key.includes(`${item.type}:${item.id}`))) {
                keysToDelete.push(key)
            }
        }
        keysToDelete.forEach(key => this.cache.delete(key))
    }
}

// 便捷函數
export async function getBatchStats(items: BatchItem[], userId?: string): Promise<BatchStatsResult> {
    const service = BatchStatsService.getInstance()
    return service.getBatchStats(items, userId)
}

// 為任何類型的內容數組添加統計數據
export async function enrichWithBatchStats<T extends { id: string | number }>(
    items: T[],
    itemType: "card" | "thread" | "comment",
    userId?: string
): Promise<(T & { stats: BatchStats; userStates?: BatchUserStates })[]> {
    if (items.length === 0) return []

    const batchItems: BatchItem[] = items.map(item => ({
        id: typeof item.id === "number" ? item.id.toString() : item.id,
        type: itemType
    }))

    const result = await getBatchStats(batchItems, userId)

    return items.map(item => {
        const itemId = typeof item.id === "number" ? item.id.toString() : item.id
        const itemStats = result.stats[itemId] || { likes: 0, dislikes: 0, comments: 0, replies: 0, bookmarks: 0, views: 0 }
        const itemUserStates = result.userStates[itemId] || { liked: false, disliked: false, bookmarked: false }

        return {
            ...item,
            stats: {
                ...itemStats,
                // 將用戶狀態合併到 stats 中，這樣 ContentCard 可以直接使用
                hasLiked: itemUserStates.liked,
                hasDisliked: itemUserStates.disliked,
                hasBookmarked: itemUserStates.bookmarked
            },
            userStates: itemUserStates
        }
    })
} 