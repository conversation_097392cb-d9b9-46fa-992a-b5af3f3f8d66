import { createBrowserClient } from "@/lib/supabase/client"

// 定義收藏牆類型
interface Collection {
  id: string
  name: string
  description?: string
  cover_image?: string
  is_public: boolean
  user_id: string
  category_id?: string
  created_at: string
  updated_at: string
  itemCount?: number
}

// 定義收藏項目類型
interface BookmarkedItem {
  id: string
  collection_id: string
  item_type: "card" | "thread"
  item_id: string
  created_at: string
}

// 獲取用戶的所有收藏牆
export async function getUserCollections(supabase?: any): Promise<Collection[]> {
  try {
    const client = supabase || createBrowserClient()

    const { data, error } = await client
      .from("collections")
      .select("*")
      .order("created_at", { ascending: false })

    if (error) {
      console.error("Error fetching user collections:", error)
      return []
    }

    if (!data) {
      return []
    }

    // 為每個收藏牆獲取項目數量
    const collectionsWithCount = await Promise.all(
      data.map(async (collection: any) => {
        const { count: itemCount, error: countError } = await client
          .from("collection_items")
          .select("*", { count: "exact", head: true })
          .eq("collection_id", collection.id)

        if (countError) {
          console.warn("Error fetching item count for collection:", collection.id, countError)
        }

        return {
          ...collection,
          itemCount: itemCount || 0,
        }
      })
    )

    return collectionsWithCount
  } catch (error) {
    console.error("Error in getUserCollections:", error)
    return []
  }
}

// 獲取單個收藏牆
export async function getCollectionById(collectionId: string, supabase?: any): Promise<{ success: boolean; data?: Collection; error?: string }> {
  try {
    const client = supabase || createBrowserClient()

    const { data, error } = await client
      .from("collections")
      .select("*")
      .eq("id", collectionId)
      .single()

    if (error) {
      console.error("Error fetching collection:", error)
      return { success: false, error: error.message }
    }

    if (!data) {
      return { success: false, error: "收藏牆不存在" }
    }

    // 獲取收藏項目數量
    const { count: itemCount, error: countError } = await client
      .from("collection_items")
      .select("*", { count: "exact", head: true })
      .eq("collection_id", collectionId)

    if (countError) {
      console.warn("Error fetching item count:", countError)
    }

    // 處理收藏項目計數
    const formattedData = {
      ...data,
      itemCount: itemCount || 0,
    }

    return { success: true, data: formattedData }
  } catch (error) {
    console.error("Error in getCollectionById:", error)
    return { success: false, error: "獲取收藏牆時發生錯誤" }
  }
}

// 創建收藏牆
export async function createCollection(
  name: string,
  description?: string,
  isPublic = true,
  supabase?: any,
): Promise<Collection | null> {
  try {
    const client = supabase || createBrowserClient()

    const { data, error } = await client
      .from("collections")
      .insert({
        name,
        description,
        is_public: isPublic,
      })
      .select()
      .single()

    if (error) {
      console.error("Error creating collection:", error)
      return null
    }

    return data
  } catch (error) {
    console.error("Error in createCollection:", error)
    return null
  }
}

// 更新收藏牆
export async function updateCollection(
  id: string,
  updates: { name?: string; description?: string; isPublic?: boolean; coverImage?: string },
  supabase?: any,
): Promise<{ success: boolean; data?: Collection; error?: string }> {
  try {
    const client = supabase || createBrowserClient()

    const { data, error } = await client
      .from("collections")
      .update({
        name: updates.name,
        description: updates.description,
        is_public: updates.isPublic,
        cover_image: updates.coverImage,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id)
      .select()
      .single()

    if (error) {
      console.error("Error updating collection:", error)
      return { success: false, error: error.message }
    }

    return { success: true, data }
  } catch (error) {
    console.error("Error in updateCollection:", error)
    return { success: false, error: "更新收藏牆時發生錯誤" }
  }
}

// 刪除收藏牆
export async function deleteCollection(id: string, supabase?: any): Promise<{ success: boolean; error?: string }> {
  try {
    const response = await fetch(`/api/collections/${id}`, {
      method: 'DELETE',
    })

    if (!response.ok) {
      const errorData = await response.json()
      return { success: false, error: errorData.error || '刪除收藏牆失敗' }
    }

    const result = await response.json()
    return result
  } catch (error) {
    console.error("Error in deleteCollection:", error)
    return { success: false, error: "刪除收藏牆時發生錯誤" }
  }
}

// 獲取收藏牆中的項目
export async function getCollectionItems(
  collectionId: string,
  page = 1,
  limit = 20,
  supabase?: any,
): Promise<BookmarkedItem[]> {
  try {
    const client = supabase || createBrowserClient()
    const offset = (page - 1) * limit

    const { data, error } = await client
      .from("collection_items")
      .select("*")
      .eq("collection_id", collectionId)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error("Error fetching collection items:", error)
      return []
    }

    if (!data) {
      return []
    }

    return data
  } catch (error) {
    console.error("Error in getCollectionItems:", error)
    return []
  }
}

// 添加項目到收藏牆
export async function addItemToCollection(
  collectionId: string,
  itemType: "card" | "thread",
  itemId: string,
  supabase?: any,
): Promise<BookmarkedItem | null> {
  try {
    const client = supabase || createBrowserClient()

    // 檢查項目是否已經在收藏牆中
    const { data: existingItem, error: checkError } = await client
      .from("collection_items")
      .select("*")
      .eq("collection_id", collectionId)
      .eq("item_type", itemType)
      .eq("item_id", itemId)
      .maybeSingle()

    if (checkError) {
      console.error("Error checking existing item:", checkError)
      return null
    }

    // 如果已經收藏，則返回錯誤
    if (existingItem) {
      console.warn("Item already in collection")
      return null
    }

    // 添加項目到收藏牆
    const { data, error } = await client
      .from("collection_items")
      .insert({
        collection_id: collectionId,
        item_type: itemType,
        item_id: itemId,
      })
      .select()
      .single()

    if (error) {
      console.error("Error adding item to collection:", error)
      return null
    }

    return data
  } catch (error) {
    console.error("Error in addItemToCollection:", error)
    return null
  }
}

// 從收藏牆中移除項目
export async function removeItemFromCollection(collectionId: string, itemId: string, supabase?: any): Promise<boolean> {
  try {
    const client = supabase || createBrowserClient()

    const { error } = await client.from("collection_items").delete().eq("id", itemId).eq("collection_id", collectionId)

    if (error) {
      console.error("Error removing item from collection:", error)
      return false
    }

    return true
  } catch (error) {
    console.error("Error in removeItemFromCollection:", error)
    return false
  }
}

// 別名函數
export const getCollectionDetails = getCollectionById
