// 簡單的記憶體快取服務
interface CacheItem {
    data: any
    timestamp: number
    ttl: number
}

class MemoryCache {
    private cache = new Map<string, CacheItem>()
    private readonly defaultTTL = 5 * 60 * 1000 // 5分鐘預設 TTL

    set(key: string, value: any, ttl?: number): void {
        this.cache.set(key, {
            data: value,
            timestamp: Date.now(),
            ttl: ttl || this.defaultTTL
        })
    }

    get<T>(key: string): T | null {
        const item = this.cache.get(key)
        if (!item) return null

        const now = Date.now()
        if (now - item.timestamp > item.ttl) {
            this.cache.delete(key)
            return null
        }

        return item.data as T
    }

    has(key: string): boolean {
        const item = this.cache.get(key)
        if (!item) return false

        const now = Date.now()
        if (now - item.timestamp > item.ttl) {
            this.cache.delete(key)
            return false
        }

        return true
    }

    delete(key: string): boolean {
        return this.cache.delete(key)
    }

    clear(): void {
        this.cache.clear()
    }

    // 清理過期的快取項目
    cleanup(): void {
        const now = Date.now()
        for (const [key, item] of this.cache.entries()) {
            if (now - item.timestamp > item.ttl) {
                this.cache.delete(key)
            }
        }
    }

    // 獲取快取狀態
    size(): number {
        return this.cache.size
    }
}

// 創建全域快取實例
export const memoryCache = new MemoryCache()

// 自動清理過期快取（每 10 分鐘執行一次）
if (typeof setInterval !== 'undefined') {
    setInterval(() => {
        memoryCache.cleanup()
    }, 10 * 60 * 1000)
}

// 快取鍵生成器
export function generateCacheKey(prefix: string, params: Record<string, any> = {}): string {
    const sortedParams = Object.keys(params)
        .sort()
        .map(key => `${key}:${params[key]}`)
        .join('|')

    return `${prefix}${sortedParams ? `|${sortedParams}` : ''}`
}

// 快取裝飾器函數
export function withCache<T extends (...args: any[]) => Promise<any>>(
    fn: T,
    keyGenerator: (...args: Parameters<T>) => string,
    ttl?: number
): T {
    return (async (...args: Parameters<T>) => {
        const cacheKey = keyGenerator(...args)

        // 嘗試從快取獲取
        const cached = memoryCache.get(cacheKey)
        if (cached) {
            return cached
        }

        // 執行原函數
        const result = await fn(...args)

        // 存入快取
        memoryCache.set(cacheKey, result, ttl)

        return result
    }) as T
} 