import { getSupabase, type ApiResponse, handleError, successResponse, calculateOffset } from "./api-utils"
import type { Card, CardCreateInput, CardUpdateInput, PaginationParams } from "@/lib/types/index"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import { enrichWithBatchStats } from "./batch-stats-service"

// 獲取所有卡片
export async function getAllCards(params?: PaginationParams): Promise<ApiResponse<Card[]>> {
  try {
    const { page = 1, limit = 10 } = params || {}
    const offset = calculateOffset(page, limit)

    const supabase = getSupabase()
    const { data, error, count } = await supabase
      .from("cards")
      .select(
        `
        *,
        profiles:author_id (id, name, avatar),
        card_topics (topics (*)),
        card_subtopics (subtopics (*))
      `,
        { count: "exact" },
      )
      .eq("status", "published")
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) throw error

    // 使用新的批量統計服務
    const cardsWithLikes = await enrichWithBatchStats(data as any[], "card")

    return successResponse(cardsWithLikes as Card[], { total: count || 0, page, limit })
  } catch (error) {
    return handleError(error)
  }
}

// 獲取熱門卡片
export async function getPopularCards(params?: {
  page?: number
  limit?: number
  timeRange?: "day" | "week" | "month" | "all"
  source?: "editor" | "community" | "all"
}): Promise<ApiResponse<Card[]>> {
  try {
    const { page = 1, limit = 10, timeRange = "week", source = "all" } = params || {}
    const offset = calculateOffset(page, limit)

    const supabase = getSupabase()
    let query = supabase.from("cards").select(
      `
        *,
        profiles:author_id (id, name, avatar),
        card_topics (topics (*)),
        card_subtopics (subtopics (*))
      `,
      { count: "exact" },
    )
      .eq("status", "published")

    // 根據時間範圍篩選
    if (timeRange !== "all") {
      const now = new Date()
      let startDate: Date

      switch (timeRange) {
        case "day":
          startDate = new Date(now.setDate(now.getDate() - 1))
          break
        case "week":
          startDate = new Date(now.setDate(now.getDate() - 7))
          break
        case "month":
          startDate = new Date(now.setMonth(now.getMonth() - 1))
          break
        default:
          startDate = new Date(0) // 從 1970 年開始
      }

      query = query.gte("created_at", startDate.toISOString())
    }

    // 根據來源篩選
    if (source !== "all") {
      query = query.eq("contribution_type", source === "editor" ? "top_author" : "community")
    }

    // 先獲取卡片，不進行排序
    const { data, error, count } = await query.range(offset, offset + limit - 1)

    if (error) throw error

    // 使用新的批量統計服務
    const cardsWithLikes = await enrichWithBatchStats(data as any[], "card")

    // 根據點讚數排序
    cardsWithLikes.sort((a: any, b: any) => (b.stats?.likes || 0) - (a.stats?.likes || 0))

    return successResponse(cardsWithLikes as any, { total: count || 0, page, limit })
  } catch (error) {
    return handleError(error)
  }
}

// 獲取最新卡片
export async function getLatestCards(params?: {
  page?: number
  limit?: number
  source?: "editor" | "community" | "all"
}): Promise<ApiResponse<Card[]>> {
  try {
    const { page = 1, limit = 10, source = "all" } = params || {}
    const offset = calculateOffset(page, limit)

    const supabase = getSupabase()
    let query = supabase.from("cards").select(
      `
        *,
        profiles:author_id (id, name, avatar),
        card_topics (topics (*)),
        card_subtopics (subtopics (*))
      `,
      { count: "exact" },
    )
      .eq("status", "published")

    // 根據來源篩選
    if (source !== "all") {
      query = query.eq("contribution_type", source === "editor" ? "top_author" : "community")
    }

    // 排序和分頁
    const { data, error, count } = await query
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) throw error

    // 使用新的批量統計服務
    const cardsWithLikes = await enrichWithBatchStats(data, "card")

    return successResponse(cardsWithLikes, { total: count || 0, page, limit })
  } catch (error) {
    return handleError(error)
  }
}

// 獲取單個卡片詳情
export async function getCardById(id: string): Promise<Card | null> {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)

    // 1. 獲取卡片基本信息
    const { data: card, error } = await supabase
      .from("cards")
      .select(`
        *,
        author:author_id(id,name,email,avatar,bio),
        topics:card_topics(topic:topic_id(id,name,description,slug)),
        subtopics:card_subtopics(subtopic:subtopic_id(id,name,description,slug,topic_id))
      `)
      .eq("id", id)
      .single()

    if (error) {
      console.error("Error fetching card:", error)
      return null
    }

    if (!card) {
      return null
    }

    // 2. 獲取卡片的評論 - 分開查詢
    const { data: comments, error: commentsError } = await supabase
      .from("comments")
      .select(`
        *,
        author:author_id(id,name,avatar,bio)
      `)
      .eq("root_item_type", "card")
      .eq("root_item_id", id)
      .order("created_at", { ascending: true })

    if (commentsError) {
      console.error("Error fetching comments:", commentsError)
    }

    // 3. 獲取評論中引用的卡片
    const commentIds = comments ? comments.map((comment) => comment.id) : []
    const referencedCards = {}

    if (commentIds.length > 0) {
      const { data: references, error: referencesError } = await supabase
        .from("content_references")
        .select(`
          source_id,
          target_id,
          target_type,
          reference_type
        `)
        .eq("source_type", "comment")
        .in("source_id", commentIds)
        .eq("target_type", "card")

      if (referencesError) {
        console.error("Error fetching content references:", referencesError)
      } else if (references && references.length > 0) {
        // 獲取所有引用的卡片 ID
        const cardIds = references.map((ref) => ref.target_id)

        // 獲取引用的卡片詳情
        const { data: referencedCardsData, error: cardsError } = await supabase
          .from("cards")
          .select(`
            id,
            title,
            content,
            semantic_type,
            author:author_id(id,name)
          `)
          .in("id", cardIds)

        if (cardsError) {
          console.error("Error fetching referenced cards:", cardsError)
        } else if (referencedCardsData) {
          // 創建卡片 ID 到卡片詳情的映射
          referencedCardsData.forEach((card) => {
            referencedCards[card.id] = {
              id: card.id,
              title: card.title,
              content: card.content,
              type: card.semantic_type,
              author: card.author?.name || "未知作者",
              tags: [], // 可以根據需要添加標籤
            }
          })

          // 將引用的卡片添加到對應的評論中
          if (comments) {
            comments.forEach((comment) => {
              const reference = references.find((ref) => ref.source_id === comment.id)
              if (reference && referencedCards[reference.target_id]) {
                comment.referenced_card = referencedCards[reference.target_id]
              }
            })
          }
        }
      }
    }

    // 4. 獲取卡片統計數據
    const { data: likesData } = await supabase
      .from("reactions")
      .select("id", { count: "exact" })
      .eq("item_type", "card")
      .eq("item_id", id)
      .eq("reaction_type", "like")

    const { data: dislikesData } = await supabase
      .from("reactions")
      .select("id", { count: "exact" })
      .eq("item_type", "card")
      .eq("item_id", id)
      .eq("reaction_type", "dislike")

    // 處理數據格式
    const formattedCard: Card = {
      ...card,
      topics: card.topics.map((t: any) => t.topic),
      subtopics: card.subtopics.map((s: any) => s.subtopic),
      replies: comments || [],
      stats: {
        views: 0, // 暫時沒有實現瀏覽量統計
        likes: likesData?.length || 0,
        dislikes: dislikesData?.length || 0,
        comments: comments?.length || 0,
      },
    }

    return formattedCard
  } catch (error) {
    console.error("Error in getCardById:", error)
    return null
  }
}

// 格式化卡片數據
function formatCardData(data: any): Card {
  if (!data) return data

  // 提取主題
  const topics = data.card_topics?.map((ct: any) => ct.topics) || []

  // 提取子主題
  const subtopics = data.card_subtopics?.map((cs: any) => cs.subtopics) || []

  // 返回格式化後的卡片
  return {
    ...data,
    topics,
    subtopics,
    // 確保 author 存在
    author: data.author || { name: "未知作者" },
  }
}

// 已遷移到 BatchStatsService，此函數已被移除

// 獲取用戶的卡片
export async function getUserCards(userId: string, params?: PaginationParams): Promise<ApiResponse<Card[]>> {
  try {
    const { page = 1, limit = 10 } = params || {}
    const offset = calculateOffset(page, limit)

    const supabase = getSupabase()
    const { data, error, count } = await supabase
      .from("cards")
      .select(
        `
        *,
        profiles:author_id (id, name, avatar),
        card_topics (topics (*)),
        card_subtopics (subtopics (*))
      `,
        { count: "exact" },
      )
      .eq("author_id", userId)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) throw error

    // 使用新的批量統計服務
    const cardsWithLikes = await enrichWithBatchStats(data, "card")

    return successResponse(cardsWithLikes, { total: count || 0, page, limit })
  } catch (error) {
    return handleError(error)
  }
}

// 創建卡片
export async function createCard(cardData: CardCreateInput): Promise<ApiResponse<Card>> {
  try {
    const supabase = getSupabase()

    // 1. 創建卡片
    const { data: card, error: cardError } = await supabase
      .from("cards")
      .insert({
        title: cardData.title,
        content: cardData.content,
        author_id: cardData.authorId,
        semantic_type: cardData.semanticType,
        contribution_type: cardData.sourceType,
        original_source: cardData.originalSource,
      })
      .select()
      .single()

    if (cardError) throw cardError

    // 2. 添加主題關聯
    if (cardData.topicIds && cardData.topicIds.length > 0) {
      const topicRelations = cardData.topicIds.map((topicId) => ({
        card_id: card.id,
        topic_id: topicId,
      }))

      const { error: topicError } = await supabase.from("card_topics").insert(topicRelations)

      if (topicError) throw topicError
    }

    // 3. 添加子主題關聯
    if (cardData.subtopicIds && cardData.subtopicIds.length > 0) {
      const subtopicRelations = cardData.subtopicIds.map((subtopicId) => ({
        card_id: card.id,
        subtopic_id: subtopicId,
      }))

      const { error: subtopicError } = await supabase.from("card_subtopics").insert(subtopicRelations)

      if (subtopicError) throw subtopicError
    }

    return successResponse(card)
  } catch (error) {
    return handleError(error)
  }
}

// 更新卡片
export async function updateCard(id: string, updates: CardUpdateInput): Promise<ApiResponse<Card>> {
  try {
    const supabase = getSupabase()

    // 1. 更新卡片基本信息
    const { data: card, error: cardError } = await supabase
      .from("cards")
      .update({
        title: updates.title,
        content: updates.content,
        semantic_type: updates.semanticType,
        contribution_type: updates.sourceType,
        original_source: updates.originalSource,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id)
      .select()
      .single()

    if (cardError) throw cardError

    // 2. 如果提供了主題 ID，則更新主題關聯
    if (updates.topicIds) {
      // 先刪除現有關聯
      const { error: deleteTopicError } = await supabase.from("card_topics").delete().eq("card_id", id)

      if (deleteTopicError) throw deleteTopicError

      // 添加新關聯
      if (updates.topicIds.length > 0) {
        const topicRelations = updates.topicIds.map((topicId) => ({
          card_id: id,
          topic_id: topicId,
        }))

        const { error: insertTopicError } = await supabase.from("card_topics").insert(topicRelations)

        if (insertTopicError) throw insertTopicError
      }
    }

    // 3. 如果提供了子主題 ID，則更新子主題關聯
    if (updates.subtopicIds) {
      // 先刪除現有關聯
      const { error: deleteSubtopicError } = await supabase.from("card_subtopics").delete().eq("card_id", id)

      if (deleteSubtopicError) throw deleteSubtopicError

      // 添加新關聯
      if (updates.subtopicIds.length > 0) {
        const subtopicRelations = updates.subtopicIds.map((subtopicId) => ({
          card_id: id,
          subtopic_id: subtopicId,
        }))

        const { error: insertSubtopicError } = await supabase.from("card_subtopics").insert(subtopicRelations)

        if (insertSubtopicError) throw insertSubtopicError
      }
    }

    return successResponse(card)
  } catch (error) {
    return handleError(error)
  }
}

// 刪除卡片
export async function deleteCard(id: string): Promise<ApiResponse<null>> {
  try {
    const supabase = getSupabase()

    // 刪除卡片關聯的主題和子主題
    const { error: topicError } = await supabase.from("card_topics").delete().eq("card_id", id)

    if (topicError) throw topicError

    const { error: subtopicError } = await supabase.from("card_subtopics").delete().eq("card_id", id)

    if (subtopicError) throw subtopicError

    // 刪除卡片本身
    const { error } = await supabase.from("cards").delete().eq("id", id)

    if (error) throw error

    return successResponse(null, undefined, "卡片已成功刪除")
  } catch (error) {
    return handleError(error)
  }
}

// 點讚卡片
export async function likeCard(cardId: string, userId: string): Promise<ApiResponse<null>> {
  try {
    const supabase = getSupabase()

    // 檢查是否已經點讚
    const { data: existingLike, error: checkError } = await supabase
      .from("reactions")
      .select()
      .eq("item_type", "card")
      .eq("item_id", cardId)
      .eq("profile_id", userId)
      .eq("reaction_type", "like")
      .maybeSingle()

    if (checkError) throw checkError

    if (existingLike) {
      // 如果已經點讚，則取消點讚
      const { error: deleteError } = await supabase.from("reactions").delete().eq("id", existingLike.id)

      if (deleteError) throw deleteError

      return successResponse(null, undefined, "已取消點讚")
    } else {
      // 如果未點讚，則添加點讚
      const { error: insertError } = await supabase.from("reactions").insert({
        item_type: "card",
        item_id: cardId,
        profile_id: userId,
        reaction_type: "like",
      })

      if (insertError) throw insertError

      return successResponse(null, undefined, "已點讚")
    }
  } catch (error) {
    return handleError(error)
  }
}

// 檢查用戶是否已點讚卡片
export async function hasUserLikedCard(cardId: string, userId: string): Promise<ApiResponse<boolean>> {
  try {
    const supabase = getSupabase()

    const { data, error } = await supabase
      .from("reactions")
      .select()
      .eq("item_type", "card")
      .eq("item_id", cardId)
      .eq("profile_id", userId)
      .eq("reaction_type", "like")
      .maybeSingle()

    if (error) throw error

    return successResponse(!!data)
  } catch (error) {
    return handleError(error)
  }
}

// 獲取卡片的評論
export async function getCardComments(cardId: string, params?: PaginationParams): Promise<ApiResponse<Comment[]>> {
  try {
    const { page = 1, limit = 10 } = params || {}
    const offset = calculateOffset(page, limit)

    const supabase = getSupabase()
    const { data, error, count } = await supabase
      .from("comments")
      .select(
        `
        *,
        profiles:author_id (id, name, avatar)
      `,
        { count: "exact" },
      )
      .eq("root_item_type", "card")
      .eq("root_item_id", cardId)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) throw error

    return successResponse(data, { total: count || 0, page, limit })
  } catch (error) {
    return handleError(error)
  }
}

// 添加卡片評論
export async function addCardComment(cardId: string, userId: string, content: string): Promise<ApiResponse<Comment>> {
  try {
    const supabase = getSupabase()

    const { data, error } = await supabase
      .from("comments")
      .insert({
        root_item_type: "card",
        root_item_id: cardId,
        author_id: userId,
        content,
        parent_comment_id: null,
      })
      .select(`
        *,
        profiles:author_id (id, name, avatar)
      `)
      .single()

    if (error) throw error

    return successResponse(data)
  } catch (error) {
    return handleError(error)
  }
}

// 已遷移到 BatchStatsService，此函數已被移除
