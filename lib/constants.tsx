"use client"

import type React from "react"
import {
    BookOpen,
    Wrench,
    AlertTriangle,
    FlaskConical,
    LightbulbIcon,
    HelpCircle,
    FileText,
    MessageSquare,
    Zap,
    MessageCircle,
    Sparkles,
} from "lucide-react"

// 內容類型定義
export type ContentType = "viewpoint" | "thread"

// 語義類型配置
export const SEMANTIC_TYPE_CONFIG: Record<
    string,
    {
        icon: React.ReactNode
        label: string
        description: string
        color: string
        emoji: string
    }
> = {
    // 觀點卡語義類型
    concept: {
        icon: <BookOpen className="h-3.5 w-3.5" />,
        label: "概念整理",
        description: "原理、理論、詞彙解釋、系統性知識輸出",
        color: "bg-green-50 text-green-600 dark:bg-green-950 dark:text-green-300",
        emoji: "📚",
    },
    implementation: {
        icon: <Wrench className="h-3.5 w-3.5" />,
        label: "實作",
        description: "流程教學、步驟說明、指令解說",
        color: "bg-blue-50 text-blue-600 dark:bg-blue-950 dark:text-blue-300",
        emoji: "🛠️",
    },
    warning: {
        icon: <AlertTriangle className="h-3.5 w-3.5" />,
        label: "踩坑警示",
        description: "問題踩雷分享、Debug 解法、環境配置爛點",
        color: "bg-red-50 text-red-600 dark:bg-red-950 dark:text-red-300",
        emoji: "⚠️",
    },
    experience: {
        icon: <FlaskConical className="h-3.5 w-3.5" />,
        label: "實測經驗",
        description: "自己做過的實驗結果、性能比較、效果說明",
        color: "bg-purple-50 text-purple-600 dark:bg-purple-950 dark:text-purple-300",
        emoji: "🧪",
    },
    insight: {
        icon: <LightbulbIcon className="h-3.5 w-3.5" />,
        label: "看法",
        description: "對該主題的觀點、價值判斷、立場",
        color: "bg-amber-50 text-amber-600 dark:bg-amber-950 dark:text-amber-300",
        emoji: "💡",
    },
    debate: {
        icon: <HelpCircle className="h-3.5 w-3.5" />,
        label: "爭議論點",
        description: "值不值得做？哪個方法比較爛？",
        color: "bg-orange-50 text-orange-600 dark:bg-orange-950 dark:text-orange-300",
        emoji: "🤔",
    },
    guide: {
        icon: <Wrench className="h-3.5 w-3.5" />,
        label: "工具教學",
        description: "流程教學、步驟說明、指令解說",
        color: "bg-blue-50 text-blue-600 dark:bg-blue-950 dark:text-blue-300",
        emoji: "🛠️",
    },
    trap: {
        icon: <AlertTriangle className="h-3.5 w-3.5" />,
        label: "踩坑警示",
        description: "問題踩雷分享、Debug 解法、環境配置爛點",
        color: "bg-red-50 text-red-600 dark:bg-red-950 dark:text-red-300",
        emoji: "⚠️",
    },

    // 討論串語義類型
    discussion: {
        icon: <MessageSquare className="h-3.5 w-3.5" />,
        label: "討論",
        description: "一般討論主題",
        color: "bg-blue-50 text-blue-600 dark:bg-blue-950 dark:text-blue-300",
        emoji: "💬",
    },
    question: {
        icon: <HelpCircle className="h-3.5 w-3.5" />,
        label: "問題",
        description: "尋求解答的問題",
        color: "bg-red-50 text-red-600 dark:bg-red-950 dark:text-red-300",
        emoji: "❓",
    },
    brainstorm: {
        icon: <Zap className="h-3.5 w-3.5" />,
        label: "集思廣益",
        description: "集體思考和創意討論",
        color: "bg-amber-50 text-amber-600 dark:bg-amber-950 dark:text-amber-300",
        emoji: "⚡",
    },
    chat: {
        icon: <MessageCircle className="h-3.5 w-3.5" />,
        label: "閒聊",
        description: "輕鬆的交流討論",
        color: "bg-green-50 text-green-600 dark:bg-green-950 dark:text-green-300",
        emoji: "💬",
    },
}

// 內容類型配置
export const CONTENT_TYPE_CONFIG: Record<
    ContentType,
    {
        icon: React.ReactNode
        label: string
        color: string
    }
> = {
    viewpoint: {
        icon: <FileText className="h-3.5 w-3.5" />,
        label: "觀點卡",
        color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
    },
    thread: {
        icon: <MessageSquare className="h-3.5 w-3.5" />,
        label: "討論串",
        color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
    },
}

// 貢獻/來源類型配置
export const CONTRIBUTION_TYPE_CONFIG: Record<
    string,
    {
        label: string
        badge: string
        color: string
        show: boolean
    }
> = {
    top_author: {
        label: "意見領袖",
        badge: "Leader",
        color: "bg-gradient-to-r from-amber-500 to-orange-500 text-white",
        show: true,
    },
    community: {
        label: "社群貢獻",
        badge: "Community",
        color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
        show: true,
    },
    curated: {
        label: "系統整理",
        badge: "Curated",
        color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
        show: true,
    },
    original: {
        label: "原創內容",
        badge: "原創內容",
        color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
        show: true,
    },
    others: {
        label: "他人觀點",
        badge: "",
        color: "",
        show: false,
    },
    // 別名支持（向後兼容）
    leader: {
        label: "意見領袖",
        badge: "Leader",
        color: "bg-gradient-to-r from-amber-500 to-orange-500 text-white",
        show: true,
    },
}

// 獲取語義類型配置的輔助函數
export const getSemanticTypeConfig = (type: string) => {
    return SEMANTIC_TYPE_CONFIG[type] || SEMANTIC_TYPE_CONFIG.concept
}

// 獲取內容類型配置的輔助函數
export const getContentTypeConfig = (type: ContentType) => {
    return CONTENT_TYPE_CONFIG[type]
}

// 獲取貢獻類型配置的輔助函數
export const getContributionTypeConfig = (type: string, isLeader?: boolean) => {
    if (isLeader) {
        return CONTRIBUTION_TYPE_CONFIG.top_author
    }
    return CONTRIBUTION_TYPE_CONFIG[type] || CONTRIBUTION_TYPE_CONFIG.community
}

// 根據內容類型獲取對應的語義類型
export const getSemanticTypesByContentType = (contentType: ContentType) => {
    if (contentType === "thread") {
        return {
            question: SEMANTIC_TYPE_CONFIG.question,
            brainstorm: SEMANTIC_TYPE_CONFIG.brainstorm,
            chat: SEMANTIC_TYPE_CONFIG.chat,
            discussion: SEMANTIC_TYPE_CONFIG.discussion,
        }
    } else {
        return {
            insight: SEMANTIC_TYPE_CONFIG.insight,
            experience: SEMANTIC_TYPE_CONFIG.experience,
            guide: SEMANTIC_TYPE_CONFIG.guide,
            trap: SEMANTIC_TYPE_CONFIG.trap,
            debate: SEMANTIC_TYPE_CONFIG.debate,
            concept: SEMANTIC_TYPE_CONFIG.concept,
        }
    }
} 