// 模擬資料：主題
export const mockTopics = [
  {
    id: "llm",
    name: "LLM",
    description: "大型語言模型的最新進展與應用",
    subtopics: [
      { id: "transformer", name: "Transformer", description: "Transformer 架構及其變體" },
      { id: "embedding", name: "Embedding", description: "文本嵌入技術與應用" },
      { id: "lora", name: "LoRA", description: "低秩適應微調技術" },
      { id: "fine-tuning", name: "微調", description: "大型語言模型的微調方法" },
      { id: "inference-optimization", name: "推理優化", description: "模型推理性能優化技術" },
    ],
  },
  {
    id: "prompt-engineering",
    name: "Prompt Engineering",
    description: "提示工程的技巧與最佳實踐",
    subtopics: [
      { id: "zero-shot", name: "Zero-shot", description: "零樣本提示技術" },
      { id: "few-shot", name: "Few-shot", description: "少樣本提示技術" },
      { id: "cot", name: "CoT", description: "思維鏈提示方法" },
      { id: "prompt-templates", name: "提示模板", description: "結構化提示模板設計" },
      { id: "role-design", name: "角色設計", description: "AI 角色與人格設計" },
    ],
  },
  {
    id: "rag",
    name: "RAG",
    description: "檢索增強生成的架構與實作",
    subtopics: [
      { id: "faiss", name: "FAISS", description: "Facebook AI 相似性搜索庫" },
      { id: "pinecone", name: "Pinecone", description: "向量搜索服務" },
      { id: "vector-database", name: "向量數據庫", description: "向量數據庫技術與選型" },
      { id: "retrieval-strategy", name: "檢索策略", description: "多種檢索策略的比較與選擇" },
      { id: "re-ranking", name: "重排序", description: "檢索結果的重排序技術" },
    ],
  },
  {
    id: "text-to-video",
    name: "Text-to-Video",
    description: "文本到視頻生成的前沿技術",
    subtopics: [
      { id: "video-diffusion", name: "Video Diffusion", description: "視頻擴散模型" },
      { id: "gen1", name: "Gen1", description: "Runway 的文本到視頻模型" },
      { id: "sora", name: "Sora", description: "OpenAI 的文本到視頻模型" },
      { id: "temporal-control", name: "時序控制", description: "視頻生成中的時序控制技術" },
      { id: "style-transfer", name: "風格遷移", description: "視頻風格遷移��術" },
    ],
  },
]

export const mockCollections = [
  {
    id: 1,
    name: "RAG 實作",
    description: "關於 RAG 系統的實作案例",
    coverImage: "/abstract-geometric-shapes.png",
    itemCount: 15,
    isPublic: true,
    createdAt: "2023-06-01T10:00:00Z",
    updatedAt: "2023-06-15T14:30:00Z",
    category: "技術學習",
  },
  {
    id: 2,
    name: "LLM 提示工程",
    description: "LLM 提示工程的技巧與最佳實踐",
    coverImage: "/diverse-team-brainstorm.png",
    itemCount: 20,
    isPublic: false,
    createdAt: "2023-05-20T15:00:00Z",
    updatedAt: "2023-06-10T09:45:00Z",
    category: "技術學習",
  },
  {
    id: 3,
    name: "AI 論文精選",
    description: "精選的 AI 相關論文",
    coverImage: "/diverse-research-team.png",
    itemCount: 10,
    isPublic: true,
    createdAt: "2023-04-10T12:00:00Z",
    updatedAt: "2023-05-25T16:20:00Z",
    category: "個人收藏",
  },
]

export const mockBookmarkedItems = [
  {
    id: 1,
    type: "viewpoint",
    title: "GPT-4o 與 Claude 3 Opus 的實際應用比較",
    content:
      "經過多次實測，我發現 GPT-4o 在創意寫作和多模態理解上表現更佳，而 Claude 3 Opus 在長文本分析和事實準確性方面略勝一籌。",
    topics: ["LLM"],
    subtopics: ["GPT-4o", "Claude 3"],
    author: {
      name: "AI研究員",
      avatar: "/diverse-research-team.png",
    },
    timestamp: "2小時前",
    stats: {
      likes: 42,
      dislikes: 3,
      comments: 12,
      bookmarks: 18,
    },
    collectionIds: [1, 2],
  },
  {
    id: 2,
    type: "discussion",
    title: "如何有效評估 RAG 系統的檢索質量？",
    content: "最近在構建 RAG 系統時，遇到了評估檢索質量的問題...",
    topics: ["RAG"],
    tags: ["評估指標", "檢索質量"],
    author: {
      name: "技術愛好者",
      avatar: "/mystical-forest-spirit.png",
    },
    timestamp: "1天前",
    stats: {
      replies: 27,
      views: 342,
      likes: 18,
      dislikes: 2,
    },
    collectionIds: [2],
  },
  {
    id: 3,
    type: "viewpoint",
    title: "使用 LoRA 微調 Llama 3 的實戰經驗分享",
    content:
      "最近嘗試使用 LoRA 技術微調 Llama 3 模型，發現幾個關鍵優化點。首先，參數設置對性能影響顯著，尤其是 rank 和 alpha 值的選擇。",
    topics: ["Fine-tuning"],
    subtopics: ["LoRA", "Llama 3"],
    author: {
      name: "技術愛好者",
      avatar: "/mystical-forest-spirit.png",
    },
    timestamp: "1天前",
    stats: {
      likes: 28,
      dislikes: 2,
      comments: 8,
      bookmarks: 15,
    },
    collectionIds: [1],
  },
]

export const mockCategories = [
  { id: 1, name: "技術學習", count: 2 },
  { id: 2, name: "個人收藏", count: 1 },
  { id: 3, name: "討論收藏", count: 0 },
  { id: 4, name: "未分類", count: 0 },
]

export const mockTags = [
  { id: 1, name: "LLM", count: 120 },
  { id: 2, name: "RAG", count: 95 },
  { id: 3, name: "Prompt Engineering", count: 80 },
  { id: 4, name: "Transformer", count: 65 },
  { id: 5, name: "LoRA", count: 50 },
  { id: 6, name: "Fine-tuning", count: 40 },
]
