import { getSupabase, type ApiR<PERSON>ponse, handleError, successResponse } from "./api-utils"
import { enrichWithBatchStats, type BatchItem } from "./batch-stats-service"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

export interface MyPostsStats {
    cardsCount: {
        draft: number
        pending: number
        published: number
    }
    threadsCount: {
        draft: number
        published: number
    }
    interactionStats: {
        totalLikes: number
        totalComments: number
        totalBookmarks: number
        totalViews: number
    }
}

export interface MyPostsContent {
    pendingViewpoints: any[]
    draftViewpoints: any[]
    publishedViewpoints: any[]
    draftDiscussions: any[]
    publishedDiscussions: any[]
    stats: MyPostsStats
}

// 獲取用戶所有發文數據（優化版本）
export async function getMyPostsContent(userId: string): Promise<ApiResponse<MyPostsContent>> {
    try {
        const supabase = getSupabase()

        // 並行獲取所有數據
        const [
            pendingCardsResult,
            draftCardsResult,
            publishedCardsResult,
            draftThreadsResult,
            publishedThreadsResult
        ] = await Promise.all([
            // 待審核觀點卡
            supabase
                .from("cards")
                .select(`
          *,
          profiles:author_id (id, name, avatar),
          card_topics (topics (*)),
          card_subtopics (subtopics (*))
        `)
                .eq("author_id", userId)
                .eq("status", "pending")
                .order("updated_at", { ascending: false }),

            // 草稿觀點卡
            supabase
                .from("cards")
                .select(`
          *,
          profiles:author_id (id, name, avatar),
          card_topics (topics (*)),
          card_subtopics (subtopics (*))
        `)
                .eq("author_id", userId)
                .eq("status", "draft")
                .order("updated_at", { ascending: false }),

            // 已發布觀點卡
            supabase
                .from("cards")
                .select(`
          *,
          profiles:author_id (id, name, avatar),
          card_topics (topics (*)),
          card_subtopics (subtopics (*))
        `)
                .eq("author_id", userId)
                .eq("status", "published")
                .order("updated_at", { ascending: false }),

            // 草稿討論
            supabase
                .from("threads")
                .select(`
          *,
          profiles:author_id (id, name, avatar),
          thread_topics (topics (*)),
          thread_subtopics (subtopics (*))
        `)
                .eq("author_id", userId)
                .eq("status", "draft")
                .order("updated_at", { ascending: false }),

            // 已發布討論
            supabase
                .from("threads")
                .select(`
          *,
          profiles:author_id (id, name, avatar),
          thread_topics (topics (*)),
          thread_subtopics (subtopics (*))
        `)
                .eq("author_id", userId)
                .eq("status", "published")
                .order("updated_at", { ascending: false })
        ])

        // 檢查錯誤
        if (pendingCardsResult.error) throw pendingCardsResult.error
        if (draftCardsResult.error) throw draftCardsResult.error
        if (publishedCardsResult.error) throw publishedCardsResult.error
        if (draftThreadsResult.error) throw draftThreadsResult.error
        if (publishedThreadsResult.error) throw publishedThreadsResult.error

        const pendingCards = pendingCardsResult.data || []
        const draftCards = draftCardsResult.data || []
        const publishedCards = publishedCardsResult.data || []
        const draftThreads = draftThreadsResult.data || []
        const publishedThreads = publishedThreadsResult.data || []

        // 使用 BatchStatsService 為已發布內容添加統計數據
        const [publishedCardsWithStats, publishedThreadsWithStats] = await Promise.all([
            publishedCards.length > 0 ? enrichWithBatchStats(publishedCards as any[], "card", userId) : [],
            publishedThreads.length > 0 ? enrichWithBatchStats(publishedThreads as any[], "thread", userId) : []
        ])

        // 格式化數據
        const formatCard = (card: any, includeStats = false) => ({
            id: card.id,
            contentType: "viewpoint",
            semanticType: card.semantic_type,
            title: card.title,
            content: card.content,
            topics: card.card_topics?.map((ct: any) => ct.topics?.name || ct.topics) || [],
            subtopics: card.card_subtopics?.map((cs: any) => cs.subtopics?.name || cs.subtopics) || [],
            author: {
                id: card.profiles?.id || card.author_id,
                name: card.profiles?.name || "未知用戶",
                avatar: card.profiles?.avatar,
            },
            contribution_type: card.contribution_type,
            originalAuthor: card.original_author,
            originalSource: card.original_url,
            timestamp: new Date(card.updated_at || card.created_at).toLocaleDateString("zh-TW"),
            status: card.status,
            stats: includeStats ? card.stats : {
                likes: 0,
                dislikes: 0,
                comments: 0,
                replies: 0,
                bookmarks: 0,
                views: 0,
                hasLiked: false,
                hasDisliked: false,
                hasBookmarked: false
            },
            userStates: includeStats ? card.userStates : undefined,
            submittedAt: card.created_at,
            publishedAt: card.published_at,
            lastSaved: card.updated_at,
            variant: "grid",
            features: { truncate: true },
        })

        const formatThread = (thread: any, includeStats = false) => ({
            id: thread.id,
            contentType: "thread",
            semanticType: thread.semantic_type,
            title: thread.title,
            content: thread.content,
            topics: thread.thread_topics?.map((tt: any) => tt.topics?.name || tt.topics) || [],
            tags: thread.thread_subtopics?.map((ts: any) => ts.subtopics?.name || ts.subtopics) || [],
            author: {
                id: thread.profiles?.id || thread.author_id,
                name: thread.profiles?.name || "未知用戶",
                avatar: thread.profiles?.avatar,
            },
            timestamp: new Date(thread.updated_at || thread.created_at).toLocaleDateString("zh-TW"),
            status: thread.status,
            stats: includeStats ? thread.stats : {
                likes: 0,
                dislikes: 0,
                comments: 0,
                replies: 0,
                bookmarks: 0,
                views: 0,
                hasLiked: false,
                hasDisliked: false,
                hasBookmarked: false
            },
            userStates: includeStats ? thread.userStates : undefined,
            submittedAt: thread.created_at,
            publishedAt: thread.published_at,
            lastSaved: thread.updated_at,
            variant: "grid",
            features: { truncate: true },
        })

        // 計算互動統計
        const allPublishedContent = [...publishedCardsWithStats, ...publishedThreadsWithStats]
        const interactionStats = {
            totalLikes: allPublishedContent.reduce((sum, item) => sum + (item.stats?.likes || 0), 0),
            totalComments:
                publishedCardsWithStats.reduce((sum, item) => sum + (item.stats?.comments || 0), 0) +
                publishedThreadsWithStats.reduce((sum, item) => sum + (item.stats?.replies || 0), 0),
            totalBookmarks: allPublishedContent.reduce((sum, item) => sum + (item.stats?.bookmarks || 0), 0),
            totalViews: publishedThreadsWithStats.reduce((sum, item) => sum + (item.stats?.views || 0), 0)
        }

        const result: MyPostsContent = {
            pendingViewpoints: pendingCards.map(card => formatCard(card)),
            draftViewpoints: draftCards.map(card => formatCard(card)),
            publishedViewpoints: publishedCardsWithStats.map(card => formatCard(card, true)),
            draftDiscussions: draftThreads.map(thread => formatThread(thread)),
            publishedDiscussions: publishedThreadsWithStats.map(thread => formatThread(thread, true)),
            stats: {
                cardsCount: {
                    draft: draftCards.length,
                    pending: pendingCards.length,
                    published: publishedCards.length,
                },
                threadsCount: {
                    draft: draftThreads.length,
                    published: publishedThreads.length,
                },
                interactionStats
            }
        }

        return successResponse(result)
    } catch (error) {
        return handleError(error)
    }
}

// 清除用戶發文相關的快取
export async function clearMyPostsCache(userId: string, contentIds: string[], contentTypes: ("card" | "thread")[]): Promise<void> {
    try {
        const { BatchStatsService } = await import("./batch-stats-service")
        const batchService = BatchStatsService.getInstance()

        const batchItems: BatchItem[] = contentIds.map((id, index) => ({
            id,
            type: contentTypes[index] || "card"
        }))

        batchService.clearItemCache(batchItems)
    } catch (error) {
        console.error("清除快取失敗:", error)
    }
} 