import { createBrowserClient } from "@/lib/supabase/client"
import type { Database } from "@/lib/database.types"

type Profile = Database["public"]["Tables"]["profiles"]["Row"]

// 客戶端版本 - 不使用 cookies
export async function getProfileById(userId: string, supabase?: any): Promise<Profile | null> {
  try {
    const client = supabase || createBrowserClient()

    const { data, error } = await client.from("profiles").select("*").eq("id", userId).single()

    if (error) {
      console.error("Error fetching profile:", error)
      return null
    }

    return data
  } catch (error) {
    console.error("Error in getProfileById:", error)
    return null
  }
}

// 獲取當前登入用戶的個人資料
export async function getCurrentUserProfile(supabase?: any): Promise<Profile | null> {
  try {
    const client = supabase || createBrowserClient()

    // 獲取當前用戶會話
    const {
      data: { session },
      error: sessionError,
    } = await client.auth.getSession()

    if (sessionError || !session) {
      console.error("No active session:", sessionError)
      return null
    }

    // 獲取用戶資料
    const { data, error } = await client.from("profiles").select("*").eq("id", session.user.id).single()

    if (error) {
      console.error("Error fetching current user profile:", error)
      return null
    }

    return data
  } catch (error) {
    console.error("Error in getCurrentUserProfile:", error)
    return null
  }
}

// 更新用戶個人資料
export async function updateProfile(
  userId: string,
  updates: Partial<Profile>,
  supabase?: any,
): Promise<Profile | null> {
  try {
    const client = supabase || createBrowserClient()

    const { data, error } = await client
      .from("profiles")
      .update({
        username: updates.username,
        name: updates.name,
        bio: updates.bio,
        avatar: updates.avatar,
        website: updates.website,
        updated_at: new Date().toISOString(),
      })
      .eq("id", userId)
      .select()
      .single()

    if (error) {
      console.error("Error updating profile:", error)
      return null
    }

    return data
  } catch (error) {
    console.error("Error in updateProfile:", error)
    return null
  }
}

// 檢查用戶名是否可用
export async function isUsernameAvailable(username: string, currentUserId?: string, supabase?: any): Promise<boolean> {
  try {
    const client = supabase || createBrowserClient()
    let query = client.from("profiles").select("id").eq("username", username)

    // 如果提供了當前用戶 ID，則排除該用戶
    if (currentUserId) {
      query = query.neq("id", currentUserId)
    }

    const { data, error } = await query

    if (error) {
      console.error("Error checking username availability:", error)
      return false
    }

    // 如果沒有找到匹配的用戶名，則表示可用
    return data.length === 0
  } catch (error) {
    console.error("Error in isUsernameAvailable:", error)
    return false
  }
}

// 別名函數
export const getProfile = getProfileById
