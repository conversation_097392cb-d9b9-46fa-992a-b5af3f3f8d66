import { getSupabase } from "@/lib/api-utils"
import { type ApiResponse, handleError, successResponse, type PaginationParams, calculateOffset } from "./api-utils"

export interface Reaction {
  id: string
  profile_id: string
  item_type: "card" | "thread" | "comment"
  item_id: string
  reaction_type: string
  created_at: string
}

export interface ReactionCount {
  reaction_type: string
  count: number
}

// 添加或切換反應
export async function toggleReaction(
  itemType: string,
  itemId: string,
  reactionType: string,
): Promise<ApiResponse<{ action: "added" | "removed" }>> {
  try {
    const supabase = getSupabase()

    // 檢查用戶是否已經對該項目做出了相同類型的反應
    const { data: existingReaction, error: checkError } = await supabase
      .from("reactions")
      .select("*")
      .eq("item_type", itemType)
      .eq("item_id", itemId)
      .eq("reaction_type", reactionType)
      .maybeSingle()

    if (checkError) throw checkError

    // 如果已經有反應，則刪除它（取消反應）
    if (existingReaction) {
      const { error: deleteError } = await supabase.from("reactions").delete().eq("id", existingReaction.id)

      if (deleteError) throw deleteError

      return successResponse({ action: "removed" })
    }

    // 如果是互斥反應（如點讚和倒讚），則先刪除互斥的反應
    if (reactionType === "like" || reactionType === "dislike") {
      const oppositeType = reactionType === "like" ? "dislike" : "like"

      await supabase
        .from("reactions")
        .delete()
        .eq("item_type", itemType)
        .eq("item_id", itemId)
        .eq("reaction_type", oppositeType)
    }

    // 添加新的反應
    const { error: insertError } = await supabase.from("reactions").insert({
      item_type: itemType,
      item_id: itemId,
      reaction_type: reactionType,
    })

    if (insertError) throw insertError

    return successResponse({ action: "added" })
  } catch (error) {
    return handleError(error)
  }
}

// 獲取用戶對特定項目的反應
export async function getUserReaction(
  itemType: string,
  itemId: string,
  reactionType?: string,
): Promise<ApiResponse<Reaction | null>> {
  try {
    const supabase = getSupabase()
    let query = supabase.from("reactions").select("*").eq("item_type", itemType).eq("item_id", itemId)

    if (reactionType) {
      query = query.eq("reaction_type", reactionType)
    }

    const { data, error } = await query.maybeSingle()

    if (error) throw error

    return successResponse(data)
  } catch (error) {
    return handleError(error)
  }
}

// 獲取項目的反應計數
export async function getReactionCounts(
  itemType: string,
  itemId: string,
): Promise<ApiResponse<Record<string, number>>> {
  try {
    const supabase = getSupabase()
    const { data, error } = await supabase
      .from("reactions")
      .select("reaction_type")
      .eq("item_type", itemType)
      .eq("item_id", itemId)

    if (error) throw error

    // 計算每種反應類型的數量
    const counts: Record<string, number> = {}
    data.forEach((reaction) => {
      const type = reaction.reaction_type
      counts[type] = (counts[type] || 0) + 1
    })

    return successResponse(counts)
  } catch (error) {
    return handleError(error)
  }
}

// 獲取多個項目的反應計數
export async function getBatchReactionCounts(
  items: Array<{ itemType: string; itemId: string }>,
): Promise<ApiResponse<Record<string, Record<string, number>>>> {
  try {
    if (items.length === 0) {
      return successResponse({})
    }

    const supabase = getSupabase()

    // 構建查詢條件
    const conditions = items.map((item) => `(item_type.eq.${item.itemType},item_id.eq.${item.itemId})`).join(",")

    const { data, error } = await supabase.from("reactions").select("item_type, item_id, reaction_type").or(conditions)

    if (error) throw error

    // 計算每個項目每種反應類型的數量
    const counts: Record<string, Record<string, number>> = {}

    data.forEach((reaction) => {
      const key = `${reaction.item_type}:${reaction.item_id}`
      if (!counts[key]) {
        counts[key] = {}
      }

      const type = reaction.reaction_type
      counts[key][type] = (counts[key][type] || 0) + 1
    })

    return successResponse(counts)
  } catch (error) {
    return handleError(error)
  }
}

// 檢查用戶是否已對項目做出反應
export async function hasUserReacted(
  itemType: string,
  itemId: string,
  reactionType?: string,
): Promise<ApiResponse<boolean>> {
  try {
    const supabase = getSupabase()
    let query = supabase.from("reactions").select("id").eq("item_type", itemType).eq("item_id", itemId)

    if (reactionType) {
      query = query.eq("reaction_type", reactionType)
    }

    const { data, error } = await query.maybeSingle()

    if (error) throw error

    return successResponse(!!data)
  } catch (error) {
    return handleError(error)
  }
}

// 獲取項目的所有反應
export async function getItemReactions(
  itemType: string,
  itemId: string,
  { page = 1, limit = 20 }: PaginationParams = {},
): Promise<ApiResponse<Reaction[]>> {
  try {
    const supabase = getSupabase()
    const offset = calculateOffset(page, limit)

    const { data, error, count } = await supabase
      .from("reactions")
      .select("*, profiles(*)", { count: "exact" })
      .eq("item_type", itemType)
      .eq("item_id", itemId)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) throw error

    return successResponse(data, count)
  } catch (error) {
    return handleError(error)
  }
}
