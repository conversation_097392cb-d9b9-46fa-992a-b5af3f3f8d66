import { supabase } from "@/lib/supabase/client"
import type { SemanticType, CardType, ContributionType } from "@/lib/types"

// 導入卡片數據
export async function seedCards() {
  try {
    // 獲取所有主題和子主題
    const { data: topics, error: topicsError } = await supabase.from("topics").select("id, name")
    if (topicsError) {
      return { success: false, error: `獲取主題失敗: ${topicsError.message}` }
    }

    const { data: subtopics, error: subtopicsError } = await supabase.from("subtopics").select("id, name, topic_id")
    if (subtopicsError) {
      return { success: false, error: `獲取子主題失敗: ${subtopicsError.message}` }
    }

    // 獲取第一個用戶作為作者
    const { data: users, error: userError } = await supabase.from("profiles").select("id").limit(1)
    if (userError || !users || users.length === 0) {
      return { success: false, error: "無法獲取用戶數據" }
    }

    const authorId = users[0].id

    // 根據實際主題和子主題創建模擬卡片
    const mockCards = []

    // 為每個主題創建至少一張卡片
    for (const topic of topics) {
      // 獲取該主題下的所有子主題
      const topicSubtopics = subtopics.filter((s) => s.topic_id === topic.id)

      // 創建不同類型的卡片
      const cardTypes: CardType[] = ["internal", "external"]
      const semanticTypes: SemanticType[] = ["insight", "experience", "guide", "trap", "debate", "concept"]
      const contributionTypes: ContributionType[] = ["top_author", "community", "curated", "original", "others"]

      // 為每個主題創建 2-3 張卡片
      const numCards = 2 + Math.floor(Math.random() * 2) // 2 或 3
      for (let i = 0; i < numCards; i++) {
        // 隨機選擇卡片類型
        const cardType = cardTypes[Math.floor(Math.random() * cardTypes.length)]
        const semanticType = semanticTypes[Math.floor(Math.random() * semanticTypes.length)]
        const contributionType = contributionTypes[Math.floor(Math.random() * contributionTypes.length)]

        // 隨機選擇 1-2 個子主題（如果有）
        const selectedSubtopics = []
        if (topicSubtopics.length > 0) {
          const numSubtopics = Math.min(1 + Math.floor(Math.random() * 2), topicSubtopics.length) // 1 或 2，但不超過可用子主題數
          const shuffledSubtopics = [...topicSubtopics].sort(() => 0.5 - Math.random())
          selectedSubtopics.push(...shuffledSubtopics.slice(0, numSubtopics))
        }

        // 創建卡片
        const card = {
          title: generateCardTitle(topic.name, semanticType),
          content: generateCardContent(topic.name, semanticType),
          card_type: cardType,
          semantic_type: semanticType,
          contribution_type: contributionType,
          topic_id: topic.id,
          subtopic_ids: selectedSubtopics.map((s) => s.id),
        }

        // 如果是外部卡片，添加原作者和原始 URL
        if (cardType === "external") {
          card["original_author"] = generateAuthorName()
          card["original_url"] = `https://example.com/${topic.name.toLowerCase().replace(/\s+/g, "-")}/${Math.random()
            .toString(36)
            .substring(2, 8)}`
        }

        mockCards.push(card)
      }
    }

    // 創建卡片和關聯
    const created = []
    const errors = []

    for (const mockCard of mockCards) {
      try {
        // 插入卡片
        const { data: card, error: cardError } = await supabase
          .from("cards")
          .insert({
            title: mockCard.title,
            content: mockCard.content,
            author_id: authorId,
            card_type: mockCard.card_type,
            semantic_type: mockCard.semantic_type,
            contribution_type: mockCard.contribution_type,
            original_author: mockCard.original_author,
            original_url: mockCard.original_url,
          })
          .select()
          .single()

        if (cardError) {
          errors.push({ card: mockCard.title, error: cardError })
          continue
        }

        // 創建主題關聯
        const { error: topicError } = await supabase
          .from("card_topics")
          .insert({ card_id: card.id, topic_id: mockCard.topic_id })

        if (topicError) {
          errors.push({ card: mockCard.title, error: `創建主題關聯失敗: ${topicError.message}` })
        }

        // 創建子主題關聯
        if (mockCard.subtopic_ids && mockCard.subtopic_ids.length > 0) {
          const subtopicRelations = mockCard.subtopic_ids.map((subtopicId) => ({
            card_id: card.id,
            subtopic_id: subtopicId,
          }))

          const { error: subtopicError } = await supabase.from("card_subtopics").insert(subtopicRelations)

          if (subtopicError) {
            errors.push({ card: mockCard.title, error: `創建子主題關聯失敗: ${subtopicError.message}` })
          }
        }

        created.push(card)
      } catch (error) {
        errors.push({ card: mockCard.title, error })
      }
    }

    return {
      success: true,
      created: created.length,
      errors: errors.length > 0 ? errors : null,
    }
  } catch (error) {
    console.error("導入卡片數據時出錯:", error)
    return { success: false, error }
  }
}

// 生成卡片標題
function generateCardTitle(topicName: string, semanticType: SemanticType): string {
  const titles = {
    insight: [
      `${topicName}領域的最新洞察`,
      `${topicName}的未來趨勢分析`,
      `${topicName}中的創新思維`,
      `解析${topicName}的關鍵因素`,
      `${topicName}領域的突破性發現`,
    ],
    experience: [
      `我在${topicName}項目中的經驗分享`,
      `${topicName}實戰案例分析`,
      `從零開始學習${topicName}的心得`,
      `${topicName}專案踩坑記錄`,
      `${topicName}領域五年工作總結`,
    ],
    guide: [
      `${topicName}入門指南`,
      `${topicName}最佳實踐`,
      `${topicName}進階技巧`,
      `${topicName}優化策略`,
      `${topicName}完全手冊`,
    ],
    trap: [
      `${topicName}中常見的陷阱`,
      `${topicName}新手常犯的錯誤`,
      `${topicName}項目中的風險規避`,
      `${topicName}開發中的常見誤區`,
      `避免${topicName}中的這些問題`,
    ],
    debate: [
      `${topicName}：不同方法的比較`,
      `${topicName}中的爭議話題`,
      `${topicName}：傳統vs現代方法`,
      `關於${topicName}的兩種觀點`,
      `${topicName}領域的辯論焦點`,
    ],
    concept: [
      `${topicName}的核心概念解析`,
      `理解${topicName}的基本原理`,
      `${topicName}中的關鍵術語`,
      `${topicName}概念框架詳解`,
      `${topicName}理論基礎`,
    ],
  }

  const titleOptions = titles[semanticType]
  return titleOptions[Math.floor(Math.random() * titleOptions.length)]
}

// 生成卡片內容
function generateCardContent(topicName: string, semanticType: SemanticType): string {
  const paragraphs = [
    `${topicName}是當今技術領域中不可忽視的重要部分。隨著技術的發展，我們看到了越來越多的創新應用和解決方案。`,
    `在實際項目中，${topicName}的應用需要考慮多方面因素，包括性能、可維護性、安全性和用戶體驗等。`,
    `許多開發者在學習${topicName}時往往忽略了基礎概念的重要性，導致在後期開發中遇到各種問題。深入理解核心原理是成功的關鍵。`,
    `${topicName}領域正在快速發展，新的工具和方法不斷湧現。保持學習和適應變化的能力對於專業人士來說至關重要。`,
    `在團隊協作中，良好的${topicName}實踐可以顯著提高生產力和代碼質量。建立共同的標準和流程是成功項目的基礎。`,
  ]

  // 根據語義類型添加特定內容
  let specificContent = ""
  switch (semanticType) {
    case "insight":
      specificContent = `最近的研究表明，${topicName}領域正在朝著更加智能化和自動化的方向發展。企業應該關注這一趨勢，並考慮如何將這些新技術整合到現有系統中。`
      break
    case "experience":
      specificContent = `在我參與的一個${topicName}項目中，我們遇到了嚴重的性能問題。通過深入分析和優化，我們最終將響應時間減少了70%，大大提升了用戶體驗。`
      break
    case "guide":
      specificContent = `要成功實施${topicName}，請遵循以下步驟：1) 充分了解需求和限制；2) 選擇合���的工具和框架；3) 建立清晰的架構；4) 實施時注重代碼質量；5) 持續測試和優化。`
      break
    case "trap":
      specificContent = `在${topicName}開發中，最常見的錯誤是過早優化和忽視可維護性。記住，首先讓代碼正確工作，然後再考慮優化。同時，始終為未來的維護者（可能是你自己）考慮。`
      break
    case "debate":
      specificContent = `關於${topicName}，有兩種主要觀點：一種認為應該優先考慮靈活性和快速迭代，另一種則強調穩定性和長期維護。兩種方法各有優缺點，選擇哪種取決於具體的項目需求和團隊情況。`
      break
    case "concept":
      specificContent = `${topicName}的核心概念包括：數據流管理、狀態一致性、模塊化設計和錯誤處理。理解這些概念對於構建健壯的系統至關重要。`
      break
  }

  // 隨機選擇 2-3 個段落，並添加特定內容
  const selectedParagraphs = []
  const shuffledParagraphs = [...paragraphs].sort(() => 0.5 - Math.random())
  const numParagraphs = 2 + Math.floor(Math.random() * 2) // 2 或 3
  selectedParagraphs.push(...shuffledParagraphs.slice(0, numParagraphs))
  selectedParagraphs.push(specificContent)

  return selectedParagraphs.join("\n\n")
}

// 生成作者名稱
function generateAuthorName(): string {
  const firstNames = ["Alex", "Jordan", "Taylor", "Morgan", "Casey", "Riley", "Avery", "Quinn", "Skyler", "Dakota"]
  const lastNames = [
    "Smith",
    "Johnson",
    "Williams",
    "Brown",
    "Jones",
    "Miller",
    "Davis",
    "Garcia",
    "Rodriguez",
    "Wilson",
  ]

  const firstName = firstNames[Math.floor(Math.random() * firstNames.length)]
  const lastName = lastNames[Math.floor(Math.random() * lastNames.length)]

  return `${firstName} ${lastName}`
}
