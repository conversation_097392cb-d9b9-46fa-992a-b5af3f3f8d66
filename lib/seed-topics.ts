import { supabase } from "@/lib/supabase/client"
import { mockTopics } from "@/lib/mock-data"

export async function seedTopics() {
  try {
    console.log("開始導入主題資料...")

    // 檢查是否已有主題資料
    const { data: existingTopics, error: checkError } = await supabase.from("topics").select("id").limit(1)

    if (checkError) {
      console.error("檢查主題資料時出錯:", checkError)
      return { success: false, error: checkError }
    }

    // 如果已有資料，則跳過
    if (existingTopics && existingTopics.length > 0) {
      console.log("主題資料已存在，跳過導入")
      return { success: true, message: "主題資料已存在，跳過導入" }
    }

    // 準備主題資料
    const topicsToInsert = mockTopics.map((topic) => ({
      name: topic.name,
      description: topic.description,
    }))

    // 插入主題資料
    const { data: insertedTopics, error: insertError } = await supabase.from("topics").insert(topicsToInsert).select()

    if (insertError) {
      console.error("插入主題資料時出錯:", insertError)
      return { success: false, error: insertError }
    }

    console.log(`成功導入 ${insertedTopics.length} 個主題`)

    // 準備子主題資料
    const subtopicsToInsert = []

    for (let i = 0; i < mockTopics.length; i++) {
      const topic = mockTopics[i]
      const insertedTopic = insertedTopics[i]

      if (topic.subtopics && topic.subtopics.length > 0) {
        for (const subtopic of topic.subtopics) {
          subtopicsToInsert.push({
            name: subtopic.name,
            topic_id: insertedTopic.id,
            description: subtopic.description || "",
          })
        }
      }
    }

    // 插入子主題資料
    if (subtopicsToInsert.length > 0) {
      const { data: insertedSubtopics, error: subtopicError } = await supabase
        .from("subtopics")
        .insert(subtopicsToInsert)
        .select()

      if (subtopicError) {
        console.error("插入子主題資料時出錯:", subtopicError)
        return { success: false, error: subtopicError }
      }

      console.log(`成功導入 ${insertedSubtopics.length} 個子主題`)
    }

    return {
      success: true,
      message: `成功導入 ${insertedTopics.length} 個主題和 ${subtopicsToInsert.length} 個子主題`,
    }
  } catch (error) {
    console.error("導入主題資料時出錯:", error)
    return { success: false, error }
  }
}
