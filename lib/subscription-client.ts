"use client"

// 前端訂閱服務 - 用於處理客戶端 API 調用
export type SubscriptionItemType = 'topic' | 'subtopic'

export interface SubscriptionApiResponse<T = any> {
    success: boolean
    data?: T
    error?: string
    message?: string
}

// 基礎 API 調用函數
async function apiCall<T>(endpoint: string, options?: RequestInit): Promise<SubscriptionApiResponse<T>> {
    try {
        const response = await fetch(endpoint, {
            headers: {
                'Content-Type': 'application/json',
                ...options?.headers,
            },
            ...options,
        })

        const result = await response.json()
        return result
    } catch (error) {
        return {
            success: false,
            error: error instanceof Error ? error.message : "網路錯誤"
        }
    }
}

// 檢查訂閱狀態
export async function checkSubscriptionStatus(
    itemType: SubscriptionItemType,
    itemId: string
): Promise<SubscriptionApiResponse<{ subscribed: boolean; subscription?: any }>> {
    return apiCall(`/api/subscriptions/status?itemType=${itemType}&itemId=${itemId}`)
}

// 獲取訂閱統計
export async function getSubscriptionStats(
    itemType: SubscriptionItemType,
    itemId: string
): Promise<SubscriptionApiResponse<{ item_type: string; item_id: string; subscriber_count: number }>> {
    return apiCall(`/api/subscriptions/stats?itemType=${itemType}&itemId=${itemId}`)
}

// 切換訂閱狀態
export async function toggleSubscription(
    itemType: SubscriptionItemType,
    itemId: string
): Promise<SubscriptionApiResponse<{ subscribed: boolean; subscription?: any }>> {
    return apiCall('/api/subscriptions', {
        method: 'POST',
        body: JSON.stringify({
            itemType,
            itemId,
            action: 'toggle'
        }),
    })
}

// 訂閱項目
export async function subscribeToItem(
    itemType: SubscriptionItemType,
    itemId: string
): Promise<SubscriptionApiResponse<{ subscribed: boolean; subscription?: any }>> {
    return apiCall('/api/subscriptions', {
        method: 'POST',
        body: JSON.stringify({
            itemType,
            itemId,
            action: 'subscribe'
        }),
    })
}

// 取消訂閱項目
export async function unsubscribeFromItem(
    itemType: SubscriptionItemType,
    itemId: string
): Promise<SubscriptionApiResponse<{ subscribed: boolean }>> {
    return apiCall('/api/subscriptions', {
        method: 'POST',
        body: JSON.stringify({
            itemType,
            itemId,
            action: 'unsubscribe'
        }),
    })
}

// 獲取用戶訂閱列表
export async function getUserSubscriptions(
    itemType?: SubscriptionItemType
): Promise<SubscriptionApiResponse<any[]>> {
    const params = itemType ? `?itemType=${itemType}` : ''
    return apiCall(`/api/subscriptions${params}`)
}

// 批量檢查訂閱狀態
export async function checkBatchSubscriptionStatus(
    items: Array<{ itemType: SubscriptionItemType; itemId: string }>
): Promise<SubscriptionApiResponse<Array<{ itemType: string; itemId: string; subscribed: boolean }>>> {
    const results = await Promise.all(
        items.map(async ({ itemType, itemId }) => {
            const result = await checkSubscriptionStatus(itemType, itemId)
            return {
                itemType,
                itemId,
                subscribed: result.success ? result.data?.subscribed || false : false
            }
        })
    )

    return {
        success: true,
        data: results
    }
} 