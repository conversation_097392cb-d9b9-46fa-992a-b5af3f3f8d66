import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

export type SubscriptionItemType = 'topic' | 'subtopic'

export interface Subscription {
    id: string
    profile_id: string
    item_type: SubscriptionItemType
    item_id: string
    created_at: string
}

export interface SubscriptionStats {
    item_type: SubscriptionItemType
    item_id: string
    subscriber_count: number
}

export interface SubscriptionResult {
    success: boolean
    data?: any
    error?: string
    message?: string
}

// 取得 Supabase 客戶端（服務端）
async function getSupabaseClient() {
    const cookieStore = cookies()
    return await createServerClient(cookieStore)
}

// 檢查用戶是否已訂閱特定項目
export async function checkSubscriptionStatus(
    profileId: string,
    itemType: SubscriptionItemType,
    itemId: string
): Promise<SubscriptionResult> {
    try {
        const supabase = await getSupabaseClient()

        const { data, error } = await supabase
            .from("content_subscriptions")
            .select("*")
            .eq("profile_id", profileId)
            .eq("item_type", itemType)
            .eq("item_id", itemId)
            .single()

        if (error) {
            // 如果沒有找到記錄，代表未訂閱
            if (error.code === 'PGRST116') {
                return {
                    success: true,
                    data: { subscribed: false }
                }
            }
            throw error
        }

        return {
            success: true,
            data: {
                subscribed: true,
                subscription: data
            }
        }
    } catch (error: any) {
        return {
            success: false,
            error: error.message || "檢查訂閱狀態失敗"
        }
    }
}

// 訂閱項目
export async function subscribeToItem(
    profileId: string,
    itemType: SubscriptionItemType,
    itemId: string
): Promise<SubscriptionResult> {
    try {
        const supabase = await getSupabaseClient()

        const { data, error } = await supabase
            .from("content_subscriptions")
            .insert({
                profile_id: profileId,
                item_type: itemType,
                item_id: itemId,
            })
            .select()
            .single()

        if (error) {
            // 處理重複訂閱錯誤
            if (error.code === '23505') {
                return {
                    success: true,
                    data: { subscribed: true },
                    message: "已經訂閱過了"
                }
            }
            throw error
        }

        return {
            success: true,
            data: { subscribed: true, subscription: data },
            message: "訂閱成功"
        }
    } catch (error: any) {
        return {
            success: false,
            error: error.message || "訂閱失敗"
        }
    }
}

// 取消訂閱項目
export async function unsubscribeFromItem(
    profileId: string,
    itemType: SubscriptionItemType,
    itemId: string
): Promise<SubscriptionResult> {
    try {
        const supabase = await getSupabaseClient()

        const { error } = await supabase
            .from("content_subscriptions")
            .delete()
            .eq("profile_id", profileId)
            .eq("item_type", itemType)
            .eq("item_id", itemId)

        if (error) throw error

        return {
            success: true,
            data: { subscribed: false },
            message: "取消訂閱成功"
        }
    } catch (error: any) {
        return {
            success: false,
            error: error.message || "取消訂閱失敗"
        }
    }
}

// 切換訂閱狀態
export async function toggleSubscription(
    profileId: string,
    itemType: SubscriptionItemType,
    itemId: string
): Promise<SubscriptionResult> {
    try {
        // 先檢查當前狀態
        const statusResult = await checkSubscriptionStatus(profileId, itemType, itemId)
        if (!statusResult.success) {
            return statusResult
        }

        const isCurrentlySubscribed = statusResult.data.subscribed

        if (isCurrentlySubscribed) {
            return await unsubscribeFromItem(profileId, itemType, itemId)
        } else {
            return await subscribeToItem(profileId, itemType, itemId)
        }
    } catch (error: any) {
        return {
            success: false,
            error: error.message || "切換訂閱狀態失敗"
        }
    }
}

// 獲取用戶的所有訂閱
export async function getUserSubscriptions(
    profileId: string,
    itemType?: SubscriptionItemType
): Promise<SubscriptionResult> {
    try {
        const supabase = await getSupabaseClient()

        let query = supabase
            .from("content_subscriptions")
            .select("*")
            .eq("profile_id", profileId)
            .order("created_at", { ascending: false })

        if (itemType) {
            query = query.eq("item_type", itemType)
        }

        const { data, error } = await query

        if (error) throw error

        return {
            success: true,
            data: data || []
        }
    } catch (error: any) {
        return {
            success: false,
            error: error.message || "獲取訂閱列表失敗"
        }
    }
}

// 獲取項目的訂閱統計
export async function getSubscriptionStats(
    itemType: SubscriptionItemType,
    itemId: string
): Promise<SubscriptionResult> {
    try {
        const supabase = await getSupabaseClient()

        const { count, error } = await supabase
            .from("content_subscriptions")
            .select("*", { count: "exact", head: true })
            .eq("item_type", itemType)
            .eq("item_id", itemId)

        if (error) throw error

        return {
            success: true,
            data: {
                item_type: itemType,
                item_id: itemId,
                subscriber_count: count || 0
            }
        }
    } catch (error: any) {
        return {
            success: false,
            error: error.message || "獲取訂閱統計失敗"
        }
    }
}

// 驗證項目是否存在
export async function validateSubscriptionItem(
    itemType: SubscriptionItemType,
    itemId: string
): Promise<SubscriptionResult> {
    try {
        const supabase = await getSupabaseClient()

        let tableName = itemType === 'topic' ? 'topics' : 'subtopics'

        const { data, error } = await supabase
            .from(tableName)
            .select("id, name")
            .eq("id", itemId)
            .single()

        if (error) {
            if (error.code === 'PGRST116') {
                return {
                    success: false,
                    error: `${itemType === 'topic' ? '主題' : '子主題'}不存在`
                }
            }
            throw error
        }

        return {
            success: true,
            data: { exists: true, item: data }
        }
    } catch (error: any) {
        return {
            success: false,
            error: error.message || "驗證項目失敗"
        }
    }
}

// 獲取用戶訂閱的項目詳情
export async function getUserSubscriptionDetails(
    profileId: string
): Promise<SubscriptionResult> {
    try {
        const supabase = await getSupabaseClient()

        // 使用視圖獲取詳細資訊
        const { data, error } = await supabase
            .from("subscription_details")
            .select("*")
            .eq("profile_id", profileId)
            .order("subscribed_at", { ascending: false })

        if (error) throw error

        // 分組數據
        const topics = data?.filter(item => item.item_type === 'topic') || []
        const subtopics = data?.filter(item => item.item_type === 'subtopic') || []

        return {
            success: true,
            data: {
                topics,
                subtopics,
                total: data?.length || 0
            }
        }
    } catch (error: any) {
        return {
            success: false,
            error: error.message || "獲取訂閱詳情失敗"
        }
    }
}

// 批量檢查訂閱狀態
export async function checkBatchSubscriptionStatus(
    profileId: string,
    items: Array<{ itemType: SubscriptionItemType; itemId: string }>
): Promise<SubscriptionResult> {
    try {
        const results = await Promise.all(
            items.map(async ({ itemType, itemId }) => {
                const result = await checkSubscriptionStatus(profileId, itemType, itemId)
                return {
                    itemType,
                    itemId,
                    subscribed: result.success ? result.data.subscribed : false
                }
            })
        )

        return {
            success: true,
            data: results
        }
    } catch (error: any) {
        return {
            success: false,
            error: error.message || "批量檢查訂閱狀態失敗"
        }
    }
}

// 獲取用戶訂閱統計
export async function getUserSubscriptionCount(
    profileId: string
): Promise<SubscriptionResult> {
    try {
        const supabase = await getSupabaseClient()

        const { data, error } = await supabase
            .rpc('get_user_subscription_count', { user_id: profileId })
            .single()

        if (error) throw error

        return {
            success: true,
            data: data || {
                total_subscriptions: 0,
                topic_subscriptions: 0,
                subtopic_subscriptions: 0
            }
        }
    } catch (error: any) {
        return {
            success: false,
            error: error.message || "獲取用戶訂閱統計失敗"
        }
    }
} 