import { createClient } from "@supabase/supabase-js"

// Cookie 操作輔助函數
function getCookie(name: string): string | null {
  if (typeof window === 'undefined') return null
  const value = `; ${document.cookie}`
  const parts = value.split(`; ${name}=`)
  if (parts.length === 2) {
    return decodeURIComponent(parts.pop()?.split(';').shift() || '')
  }
  return null
}

function setCookie(name: string, value: string, options: { secure?: boolean } = {}) {
  if (typeof window === 'undefined') return

  const expires = new Date()
  expires.setTime(expires.getTime() + (7 * 24 * 60 * 60 * 1000)) // 7 days

  let cookieString = `${name}=${encodeURIComponent(value)}; expires=${expires.toUTCString()}; path=/; SameSite=Lax`

  if (options.secure) {
    cookieString += '; Secure'
  }

  document.cookie = cookieString
}

function removeCookie(name: string) {
  if (typeof window === 'undefined') return
  document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax`
}

// 使用單例模式確保只創建一個客戶端實例
let supabaseClient: ReturnType<typeof createClient> | null = null

export function createBrowserClient() {
  if (supabaseClient) return supabaseClient

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error(
      "Missing Supabase environment variables. Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY.",
    )
    throw new Error("Missing Supabase environment variables")
  }

  // 使用與 middleware 和 server 一致的 storageKey
  const storageKey = 'sb-auth-token'
  console.log("Creating Supabase client with storageKey:", storageKey)

  supabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
      storageKey,
      flowType: 'pkce',
      storage: {
        getItem: (key) => {
          if (typeof window === 'undefined') return null

          // 主要從 localStorage 讀取
          try {
            const localStorageValue = window.localStorage.getItem(key)
            if (localStorageValue) {
              console.log(`Storage getItem (localStorage): ${key} - ${localStorageValue.length} chars`)
              return localStorageValue
            }
          } catch (error) {
            console.warn('Failed to read from localStorage:', error)
          }

          // 回退到 cookie（用於 SSR 兼容性）
          if (key === storageKey) {
            const cookieValue = getCookie(key)
            if (cookieValue) {
              console.log(`Storage getItem (cookie): ${key} - ${cookieValue.length} chars`)
              // 同步回 localStorage
              try {
                window.localStorage.setItem(key, cookieValue)
              } catch (error) {
                console.warn('Failed to sync cookie to localStorage:', error)
              }
              return cookieValue
            }
          }

          console.log(`Storage getItem: ${key} - not found`)
          return null
        },
        setItem: (key, value) => {
          if (typeof window === 'undefined') return

          console.log(`Storage setItem: ${key} - ${value.length} chars`)

          // 主要設置 localStorage
          try {
            window.localStorage.setItem(key, value)
          } catch (error) {
            console.error('Failed to set localStorage:', error)
          }

          // 同時設置 cookie 用於 SSR
          if (key === storageKey) {
            try {
              setCookie(key, value, {
                secure: window.location.protocol === 'https:'
              })
            } catch (error) {
              console.warn('Failed to set cookie:', error)
            }
          }
        },
        removeItem: (key) => {
          if (typeof window === 'undefined') return

          console.log(`Storage removeItem: ${key}`)

          // 清除 localStorage
          try {
            window.localStorage.removeItem(key)
          } catch (error) {
            console.warn('Failed to remove from localStorage:', error)
          }

          // 清除 cookie
          if (key === storageKey) {
            try {
              removeCookie(key)
            } catch (error) {
              console.warn('Failed to remove cookie:', error)
            }
          }
        }
      },
    },
    global: {
      headers: {
        'X-Client-Info': 'nextjs-app'
      }
    }
  })

  // 添加全局錯誤處理
  if (typeof window !== 'undefined') {
    supabaseClient.auth.onAuthStateChange((event, session) => {
      console.log('Supabase auth state change:', {
        event,
        hasSession: !!session,
        userId: session?.user?.id?.slice(0, 8)
      })

      // 在生產環境下額外處理認證狀態變化
      if (process.env.NODE_ENV === 'production' && event === 'SIGNED_OUT') {
        // 確保清除所有認證相關資料
        try {
          window.localStorage.removeItem(storageKey)
          removeCookie(storageKey)
        } catch (error) {
          console.warn('Failed to clean up auth storage:', error)
        }
      }
    })
  }

  return supabaseClient
}

// 為了向後兼容，保留 supabase 實例
export const supabase = createBrowserClient()
