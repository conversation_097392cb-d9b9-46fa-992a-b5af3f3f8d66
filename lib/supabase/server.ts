import { createServerClient as createSupabaseServerClient } from "@supabase/ssr"
import { cookies } from "next/headers"

export async function createServerClient(cookieStore?: ReturnType<typeof cookies>) {
  const cookieStoreToUse = cookieStore || cookies()
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseKey) {
    throw new Error("Missing Supabase environment variables")
  }

  return createSupabaseServerClient(
    supabaseUrl,
    supabaseKey,
    {
      cookies: {
        getAll: async () => {
          const resolvedCookies = await cookieStoreToUse
          return resolvedCookies.getAll() || []
        },
        setAll: async (cookiesToSet) => {
          try {
            const resolvedCookies = await cookieStoreToUse
            cookiesToSet.forEach(({ name, value, options }) => {
              resolvedCookies.set(name, value, options)
            })
          } catch (error) {
            // 在 Next.js 15 中，cookies 只能在 Server Actions 或 Route Handlers 中修改
            // 在組件渲染過程中忽略 cookie 設置錯誤
            console.debug("Cookie setting skipped:", error instanceof Error ? error.message : error)
          }
        }
      },
      auth: {
        // 使用與客戶端和 middleware 相同的 storageKey
        storageKey: 'sb-auth-token'
      }
    }
  )
}
