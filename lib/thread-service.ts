import { getSupabase, type ApiResponse, handleError, successResponse } from "./api-utils"
import type { Thread } from "@/lib/types"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

// 獲取主題相關的討論串
export async function getThreadsByTopic(topicId: string, limit = 10): Promise<ApiResponse<Thread[]>> {
  try {
    const supabase = getSupabase()
    const { data, error } = await supabase
      .from("thread_topics")
      .select(`
        thread_id,
        threads:thread_id (
          id,
          title,
          content,
          author_id,
          semantic_type,
          status,
          created_at,
          updated_at,
          profiles:author_id (
            id,
            name,
            avatar
          )
        )
      `)
      .eq("topic_id", topicId)
      .eq("threads.status", "published")
      .limit(limit)

    if (error) throw error

    // 獲取每個討論串的統計信息和參與者
    const threadsWithStats = await Promise.all(
      data.map(async (item: any) => {
        const threadId = item.threads.id

        // 獲取評論數
        const { count: commentCount } = await supabase
          .from("comments")
          .select("*", { count: "exact" })
          .eq("root_item_id", threadId)
          .eq("root_item_type", "thread")

        // 獲取瀏覽數
        const { data: viewData } = await supabase
          .from("thread_views")
          .select("count")
          .eq("thread_id", threadId)
          .single()

        // 獲取 topics 和 subtopics
        const { data: topicsData } = await supabase
          .from("thread_topics")
          .select(`
            topic:topic_id(id, name, slug)
          `)
          .eq("thread_id", threadId)

        const { data: subtopicsData } = await supabase
          .from("thread_subtopics")
          .select(`
            subtopic:subtopic_id(id, name, slug)
          `)
          .eq("thread_id", threadId)

        // 獲取參與者（包括作者和評論者）
        const { data: participants } = await supabase
          .from("comments")
          .select(`
            author_id,
            created_at,
            profiles:author_id(id, name, avatar)
          `)
          .eq("root_item_id", threadId)
          .eq("root_item_type", "thread")
          .order("created_at", { ascending: false })

        // 去重參與者，並包含原作者
        const uniqueParticipants = new Map()

        // 添加原作者
        if (item.threads.profiles) {
          uniqueParticipants.set(item.threads.author_id, item.threads.profiles)
        }

        // 添加評論者
        if (participants) {
          participants.forEach((comment: any) => {
            if (comment.profiles && !uniqueParticipants.has(comment.author_id)) {
              uniqueParticipants.set(comment.author_id, comment.profiles)
            }
          })
        }

        // 計算最後活動時間（討論串創建 vs 最後評論時間）
        const threadCreatedAt = new Date(item.threads.created_at)
        const lastCommentAt = participants && participants.length > 0
          ? new Date(participants[0].created_at) // 已按 created_at 降序排列
          : null

        const lastActivityAt = lastCommentAt && lastCommentAt > threadCreatedAt
          ? lastCommentAt
          : threadCreatedAt

        return {
          id: item.threads.id,
          title: item.threads.title,
          content: item.threads.content,
          author_id: item.threads.author_id,
          semantic_type: item.threads.semantic_type,
          created_at: item.threads.created_at,
          updated_at: item.threads.updated_at || item.threads.created_at,
          last_activity_at: lastActivityAt.toISOString(),
          author: item.threads.profiles,
          topics: topicsData?.map((t: any) => t.topic) || [],
          subtopics: subtopicsData?.map((s: any) => s.subtopic) || [],
          stats: {
            replies: commentCount || 0,
            views: viewData?.count || Math.floor(Math.random() * 1000),
            participants: Array.from(uniqueParticipants.values())
          }
        }
      })
    )

    return successResponse(threadsWithStats)
  } catch (error) {
    return handleError(error)
  }
}

// 獲取討論串的評論數
export async function getThreadCommentCount(threadId: string): Promise<ApiResponse<number>> {
  try {
    const supabase = getSupabase()
    const { count, error } = await supabase
      .from("comments")
      .select("*", { count: "exact" })
      .eq("root_item_id", threadId)
      .eq("root_item_type", "thread")

    if (error) throw error

    return successResponse(count || 0)
  } catch (error) {
    return handleError(error)
  }
}

// 獲取討論串的瀏覽數（假設有一個 thread_views 表）
export async function getThreadViewCount(threadId: string): Promise<ApiResponse<number>> {
  try {
    // 這裡假設有一個 thread_views 表來記錄瀏覽數
    // 如果沒有，可以返回一個隨機數或固定值作為示例
    const supabase = getSupabase()
    const { count, error } = await supabase
      .from("thread_views")
      .select("*", { count: "exact" })
      .eq("thread_id", threadId)

    if (error) {
      // 如果表不存在，返回一個隨機數作為示例
      return successResponse(Math.floor(Math.random() * 1000))
    }

    return successResponse(count || 0)
  } catch (error) {
    // 如果出錯，返回一個隨機數作為示例
    return successResponse(Math.floor(Math.random() * 1000))
  }
}

// 新增：增加討論串瀏覽數
export async function incrementThreadViewCount(threadId: string): Promise<ApiResponse<number>> {
  try {
    const supabase = getSupabase()
    // 先查詢是否有該 thread_id 的紀錄
    const { data, error } = await supabase
      .from("thread_views")
      .select("count")
      .eq("thread_id", threadId)
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116: No rows found
      throw error
    }

    let newCount = 1
    if (data) {
      // 已有紀錄，更新 count +1
      newCount = (data.count || 0) + 1
      const { error: updateError } = await supabase
        .from("thread_views")
        .update({ count: newCount, last_updated: new Date().toISOString() })
        .eq("thread_id", threadId)
      if (updateError) throw updateError
    } else {
      // 沒有紀錄，插入新的一筆
      const { error: insertError } = await supabase
        .from("thread_views")
        .insert({ thread_id: threadId, count: newCount, last_updated: new Date().toISOString() })
      if (insertError) throw insertError
    }
    return successResponse(newCount)
  } catch (error) {
    return handleError(error)
  }
}

// 獲取單個討論串詳情
export async function getThreadById(id: string): Promise<Thread | null> {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)

    // 1. 獲取討論基本信息
    const { data: thread, error } = await supabase
      .from("threads")
      .select(`
        *,
        author:author_id(id,name,email,avatar,bio),
        topics:thread_topics(topic:topic_id(id,name,description)),
        subtopics:thread_subtopics(subtopic:subtopic_id(id,name,description,topic_id))
      `)
      .eq("id", id)
      .single()

    if (error) {
      console.error("Error fetching thread:", error)
      return null
    }

    if (!thread) {
      return null
    }

    // 2. 獲取討論的評論
    const { data: comments, error: commentsError } = await supabase
      .from("comments")
      .select(`
        *,
        author:author_id(id,name,avatar,bio)
      `)
      .eq("root_item_type", "thread")
      .eq("root_item_id", id)
      .order("created_at", { ascending: true })

    if (commentsError) {
      console.error("Error fetching comments:", commentsError)
    }

    // 3. 獲取評論中引用的卡片
    const commentIds = comments ? comments.map((comment) => comment.id) : []
    const referencedCards = {}

    if (commentIds.length > 0) {
      const { data: references, error: referencesError } = await supabase
        .from("content_references")
        .select(`
          source_id,
          target_id,
          target_type,
          reference_type
        `)
        .eq("source_type", "comment")
        .in("source_id", commentIds)
        .eq("target_type", "card")

      if (referencesError) {
        console.error("Error fetching content references:", referencesError)
      } else if (references && references.length > 0) {
        // 獲取所有引用的卡片 ID
        const cardIds = references.map((ref) => ref.target_id)

        // 獲取引用的卡片詳情
        const { data: referencedCardsData, error: cardsError } = await supabase
          .from("cards")
          .select(`
            id,
            title,
            content,
            semantic_type,
            author:author_id(id,name)
          `)
          .in("id", cardIds)

        if (cardsError) {
          console.error("Error fetching referenced cards:", cardsError)
        } else if (referencedCardsData) {
          // 創建卡片 ID 到卡片詳情的映射
          referencedCardsData.forEach((card) => {
            referencedCards[card.id] = {
              id: card.id,
              title: card.title,
              content: card.content,
              type: card.semantic_type,
              author: card.author?.name || "未知作者",
              tags: [],
            }
          })

          // 將引用的卡片添加到對應的評論中
          if (comments) {
            comments.forEach((comment) => {
              const reference = references.find((ref) => ref.source_id === comment.id)
              if (reference && referencedCards[reference.target_id]) {
                comment.referenced_card = referencedCards[reference.target_id]
              }
            })
          }
        }
      }
    }

    // 4. 獲取討論的點讚數和倒讚數
    const { data: likesData } = await supabase
      .from("reactions")
      .select("id", { count: "exact" })
      .eq("item_type", "thread")
      .eq("item_id", id)
      .eq("reaction_type", "like")

    const { data: dislikesData } = await supabase
      .from("reactions")
      .select("id", { count: "exact" })
      .eq("item_type", "thread")
      .eq("item_id", id)
      .eq("reaction_type", "dislike")

    // 處理數據格式
    const formattedThread: Thread = {
      ...thread,
      topics: thread.topics.map((t: any) => t.topic),
      subtopics: thread.subtopics.map((s: any) => s.subtopic),
      replies: comments || [],
      stats: {
        views: 0, // 暫時沒有實現瀏覽量統計
        likes: likesData?.length || 0,
        dislikes: dislikesData?.length || 0,
        comments: comments?.length || 0,
      },
    }

    return formattedThread
  } catch (error) {
    console.error("Error in getThreadById:", error)
    return null
  }
}

// 已遷移到 BatchStatsService，此函數已被移除
