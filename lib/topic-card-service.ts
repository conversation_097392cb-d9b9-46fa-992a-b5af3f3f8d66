import { getSupabase, type ApiResponse, handleError, successResponse, safeGetUser } from "./api-utils"
import { enrichWithBatchStats } from "./batch-stats-service"
import type { Card } from "@/lib/types"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

// 獲取主題相關的卡片
export async function getCardsByTopic(topicId: string, limit = 10): Promise<ApiResponse<Card[]>> {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)

    const { data, error } = await supabase
      .from("card_topics")
      .select(`
        card_id,
        cards:card_id (
          id,
          title,
          content,
          author_id,
          card_type,
          semantic_type,
          contribution_type,
          original_author,
          original_url,
          created_at,
          updated_at,
          status,
          profiles:author_id (
            id,
            name,
            avatar
          )
        )
      `)
      .eq("topic_id", topicId)
      .eq("cards.status", "published")
      .limit(limit)

    if (error) throw error

    // 重新格式化數據以符合 Card 類型
    const cards: Card[] = data
      .filter(item => item.cards) // 過濾掉空的卡片
      .map((item: any) => ({
        id: item.cards.id,
        title: item.cards.title,
        content: item.cards.content,
        author_id: item.cards.author_id,
        card_type: item.cards.card_type,
        semantic_type: item.cards.semantic_type,
        contribution_type: item.cards.contribution_type,
        original_author: item.cards.original_author,
        original_url: item.cards.original_url,
        created_at: item.cards.created_at,
        updated_at: item.cards.updated_at,
        author: item.cards.profiles,
        topics: [], // 初始化為空陣列
        subtopics: [], // 初始化為空陣列
      }))

    if (cards.length === 0) {
      return successResponse(cards)
    }

    // 批量獲取所有卡片的主題和子主題
    const cardIds = cards.map(card => card.id)

    const [topicData, subtopicData] = await Promise.all([
      // 批量獲取主題
      supabase
        .from("card_topics")
        .select(`
          card_id,
          topics:topic_id (
            id,
            name,
            slug
          )
        `)
        .in("card_id", cardIds),

      // 批量獲取子主題
      supabase
        .from("card_subtopics")
        .select(`
          card_id,
          subtopics:subtopic_id (
            id,
            name,
            slug
          )
        `)
        .in("card_id", cardIds)
    ])

    if (topicData.error) throw topicData.error
    if (subtopicData.error) throw subtopicData.error

    // 創建映射表
    const topicsMap = new Map()
    const subtopicsMap = new Map()

    topicData.data?.forEach((item: any) => {
      if (!topicsMap.has(item.card_id)) {
        topicsMap.set(item.card_id, [])
      }
      topicsMap.get(item.card_id).push(item.topics)
    })

    subtopicData.data?.forEach((item: any) => {
      if (!subtopicsMap.has(item.card_id)) {
        subtopicsMap.set(item.card_id, [])
      }
      subtopicsMap.get(item.card_id).push(item.subtopics)
    })

    // 添加主題和子主題到卡片
    cards.forEach(card => {
      card.topics = topicsMap.get(card.id) || []
      card.subtopics = subtopicsMap.get(card.id) || []
    })

    // 獲取當前用戶信息
    const { user } = await safeGetUser(supabase)
    const userId = user?.id

    // 使用統一的批量統計服務，包含用戶 ID
    const cardsWithStats = await enrichWithBatchStats(cards, "card", userId)

    return successResponse(cardsWithStats)
  } catch (error) {
    return handleError(error)
  }
}

// 獲取子主題相關的卡片
export async function getCardsBySubtopic(subtopicId: string, limit = 10): Promise<ApiResponse<Card[]>> {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)

    const { data, error } = await supabase
      .from("card_subtopics")
      .select(`
        card_id,
        cards:card_id (
          id,
          title,
          content,
          author_id,
          card_type,
          semantic_type,
          contribution_type,
          original_author,
          original_url,
          created_at,
          updated_at,
          status,
          profiles:author_id (
            id,
            name,
            avatar
          )
        )
      `)
      .eq("subtopic_id", subtopicId)
      .eq("cards.status", "published")
      .limit(limit)

    if (error) throw error

    // 重新格式化數據以符合 Card 類型
    const cards: Card[] = data
      .filter(item => item.cards) // 過濾掉空的卡片
      .map((item: any) => ({
        id: item.cards.id,
        title: item.cards.title,
        content: item.cards.content,
        author_id: item.cards.author_id,
        card_type: item.cards.card_type,
        semantic_type: item.cards.semantic_type,
        contribution_type: item.cards.contribution_type,
        original_author: item.cards.original_author,
        original_url: item.cards.original_url,
        created_at: item.cards.created_at,
        updated_at: item.cards.updated_at,
        author: item.cards.profiles,
        topics: [], // 初始化為空陣列
        subtopics: [], // 初始化為空陣列
      }))

    if (cards.length === 0) {
      return successResponse(cards)
    }

    // 批量獲取所有卡片的主題和子主題
    const cardIds = cards.map(card => card.id)

    const [topicData, subtopicData] = await Promise.all([
      // 批量獲取主題
      supabase
        .from("card_topics")
        .select(`
          card_id,
          topics:topic_id (
            id,
            name,
            slug
          )
        `)
        .in("card_id", cardIds),

      // 批量獲取子主題
      supabase
        .from("card_subtopics")
        .select(`
          card_id,
          subtopics:subtopic_id (
            id,
            name,
            slug
          )
        `)
        .in("card_id", cardIds)
    ])

    if (topicData.error) throw topicData.error
    if (subtopicData.error) throw subtopicData.error

    // 創建映射表
    const topicsMap = new Map()
    const subtopicsMap = new Map()

    topicData.data?.forEach((item: any) => {
      if (!topicsMap.has(item.card_id)) {
        topicsMap.set(item.card_id, [])
      }
      topicsMap.get(item.card_id).push(item.topics)
    })

    subtopicData.data?.forEach((item: any) => {
      if (!subtopicsMap.has(item.card_id)) {
        subtopicsMap.set(item.card_id, [])
      }
      subtopicsMap.get(item.card_id).push(item.subtopics)
    })

    // 添加主題和子主題到卡片
    cards.forEach(card => {
      card.topics = topicsMap.get(card.id) || []
      card.subtopics = subtopicsMap.get(card.id) || []
    })

    // 獲取當前用戶信息
    const { user } = await safeGetUser(supabase)
    const userId = user?.id

    // 使用統一的批量統計服務，包含用戶 ID
    const cardsWithStats = await enrichWithBatchStats(cards, "card", userId)

    return successResponse(cardsWithStats)
  } catch (error) {
    return handleError(error)
  }
}

// 已遷移到 BatchStatsService，此函數已被移除
