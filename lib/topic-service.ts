import { getSupabase, type ApiResponse, handleError, successResponse } from "./api-utils"
import type { Topic, Subtopic } from "@/lib/types"

// 獲取所有主題
export async function getAllTopics(): Promise<ApiResponse<Topic[]>> {
  try {
    const supabase = getSupabase()
    const { data, error } = await supabase
      .from("topics")
      .select(`
        *,
        subtopics (*)
      `)
      .order("name")

    if (error) throw error

    return successResponse(data)
  } catch (error) {
    return handleError(error)
  }
}

// 獲取所有主題及其子主題（為了兼容性）
export async function getAllTopicsWithSubtopics(): Promise<ApiResponse<Topic[]>> {
  // 這個函數只是 getAllTopics 的別名，因為 getAllTopics 已經包含了子主題
  return getAllTopics()
}

// 獲取單個主題
export async function getTopicById(id: string): Promise<ApiResponse<Topic>> {
  try {
    const supabase = getSupabase()
    const { data, error } = await supabase
      .from("topics")
      .select(`
        *,
        subtopics (*)
      `)
      .eq("id", id)
      .single()

    if (error) throw error

    return successResponse(data)
  } catch (error) {
    return handleError(error)
  }
}

// 獲取主題（通過 slug）- 使用 slug 欄位而不是 name
export async function getTopicBySlug(slug: string): Promise<ApiResponse<Topic>> {
  try {
    // Reduce logging
    // console.log(`Fetching topic by slug: ${slug}`)
    const supabase = getSupabase()

    // 使用 slug 欄位進行精確匹配
    const { data, error } = await supabase
      .from("topics")
      .select(`
        *,
        subtopics (*)
      `)
      .eq("slug", slug)

    if (error) throw error

    // Reduce logging
    // console.log(`Found ${data?.length || 0} topics with slug: ${slug}`)

    // 如果沒有找到結果
    if (!data || data.length === 0) {
      // console.log(`No topic found with slug: ${slug}`)
      return {
        success: false,
        data: null,
        error: `No topic found with slug: ${slug}`,
      }
    }

    // 如果找到多個結果，使用第一個（這種情況不應該發生，因為我們有唯一約束）
    if (data.length > 1) {
      console.log(`Multiple topics found with slug: ${slug}, using the first one`)
    }

    return successResponse(data[0])
  } catch (error) {
    console.error("Error in getTopicBySlug:", error)
    return handleError(error)
  }
}

// 同樣修改 getSubtopicBySlug 函數，使用 slug 欄位
export async function getSubtopicBySlug(topicSlug: string, subtopicSlug: string): Promise<ApiResponse<Subtopic>> {
  try {
    // Reduce logging
    // console.log(`Fetching subtopic with slug: ${subtopicSlug} for topic with slug: ${topicSlug}`)
    const supabase = getSupabase()

    // First get the topic using the slug field
    const { data: topics, error: topicError } = await supabase.from("topics").select("id").eq("slug", topicSlug)

    if (topicError) throw topicError

    if (!topics || topics.length === 0) {
      // console.log(`No topic found with slug: ${topicSlug}`)
      return {
        success: false,
        data: null,
        error: `No topic found with slug: ${topicSlug}`,
      }
    }

    const topicId = topics[0].id
    // Reduce logging
    // console.log(`Found topic with id: ${topicId}`)

    // Then get the subtopic using the slug field
    const { data: subtopics, error } = await supabase
      .from("subtopics")
      .select("*")
      .eq("topic_id", topicId)
      .eq("slug", subtopicSlug)

    if (error) throw error

    if (!subtopics || subtopics.length === 0) {
      // console.log(`No subtopic found with slug: ${subtopicSlug} for topic with slug: ${topicSlug}`)
      return {
        success: false,
        data: null,
        error: `No subtopic found with slug: ${subtopicSlug} for topic with slug: ${topicSlug}`,
      }
    }

    // Reduce logging
    // console.log(`Found subtopic with slug: ${subtopicSlug}`)
    return successResponse(subtopics[0])
  } catch (error) {
    console.error("Error fetching subtopic by slug:", error)
    return handleError(error)
  }
}

// 獲取熱門主題
export async function getPopularTopics(limit = 5): Promise<ApiResponse<Topic[]>> {
  try {
    const supabase = getSupabase()
    // 這裡假設我們有一個 topic_stats 表來記錄主題的熱度
    // 如果沒有，可以根據卡片數量或其他指標來排序
    const { data, error } = await supabase
      .from("topics")
      .select(`
        *,
        subtopics (count)
      `)
      .order("card_count", { ascending: false })
      .limit(limit)

    if (error) throw error

    return successResponse(data)
  } catch (error) {
    return handleError(error)
  }
}

// 獲取熱門子主題
export async function getPopularSubtopics(limit = 10): Promise<ApiResponse<Subtopic[]>> {
  try {
    const supabase = getSupabase()

    // 獲取所有子主題，並使用 JOIN 查詢獲取相關的主題信息
    const { data: subtopics, error: subtopicsError } = await supabase
      .from("subtopics")
      .select(`
        id,
        name,
        slug,
        description,
        topic_id,
        topics!topic_id (
          id,
          name,
          slug
        )
      `)
      .order("name")
      .limit(limit * 2) // 多取一些，以防某些沒有卡片

    if (subtopicsError) {
      console.error("Error fetching subtopics:", subtopicsError)
      throw subtopicsError
    }

    if (!subtopics || subtopics.length === 0) {
      console.log("No subtopics found")
      return successResponse([])
    }

    // 獲取每個子主題的卡片數量
    const subtopicsWithCount = await Promise.all(
      subtopics.map(async (subtopic) => {
        try {
          const { count, error: countError } = await supabase
            .from("card_subtopics")
            .select("*", { count: "exact", head: true })
            .eq("subtopic_id", subtopic.id)

          if (countError) {
            console.warn(`Error getting count for subtopic ${subtopic.id}:`, countError)
            return {
              ...subtopic,
              card_count: 0,
            }
          }

          return {
            ...subtopic,
            card_count: count || 0,
          }
        } catch (err) {
          console.warn(`Failed to get count for subtopic ${subtopic.id}:`, err)
          return {
            ...subtopic,
            card_count: 0,
          }
        }
      }),
    )

    // 按卡片數量排序，並過濾掉沒有卡片的子主題
    const sortedSubtopics = subtopicsWithCount
      .filter(subtopic => subtopic.card_count > 0)
      .sort((a, b) => b.card_count - a.card_count)
      .slice(0, limit)

    console.log(`Found ${sortedSubtopics.length} popular subtopics`)
    return successResponse(sortedSubtopics)
  } catch (error) {
    console.error("Error in getPopularSubtopics:", error)
    return handleError(error)
  }
}

// 創建主題
export async function createTopic(name: string, description: string, imageUrl?: string): Promise<ApiResponse<Topic>> {
  try {
    const supabase = getSupabase()
    const { data, error } = await supabase
      .from("topics")
      .insert({
        name,
        description,
        image_url: imageUrl,
      })
      .select()
      .single()

    if (error) throw error

    return successResponse(data)
  } catch (error) {
    return handleError(error)
  }
}

// 創建子主題
export async function createSubtopic(
  topicId: string,
  name: string,
  description: string,
): Promise<ApiResponse<Subtopic>> {
  try {
    const supabase = getSupabase()
    const { data, error } = await supabase
      .from("subtopics")
      .insert({
        topic_id: topicId,
        name,
        description,
      })
      .select()
      .single()

    if (error) throw error

    return successResponse(data)
  } catch (error) {
    return handleError(error)
  }
}

// 更新主題
export async function updateTopic(id: string, updates: Partial<Topic>): Promise<ApiResponse<Topic>> {
  try {
    const supabase = getSupabase()
    const { data, error } = await supabase
      .from("topics")
      .update({
        name: updates.name,
        description: updates.description,
        image_url: updates.image_url,
      })
      .eq("id", id)
      .select()
      .single()

    if (error) throw error

    return successResponse(data)
  } catch (error) {
    return handleError(error)
  }
}

// 更新子主題
export async function updateSubtopic(id: string, updates: Partial<Subtopic>): Promise<ApiResponse<Subtopic>> {
  try {
    const supabase = getSupabase()
    const { data, error } = await supabase
      .from("subtopics")
      .update({
        name: updates.name,
        description: updates.description,
      })
      .eq("id", id)
      .select()
      .single()

    if (error) throw error

    return successResponse(data)
  } catch (error) {
    return handleError(error)
  }
}

// 刪除主題
export async function deleteTopic(id: string): Promise<ApiResponse<null>> {
  try {
    const supabase = getSupabase()
    // 先刪除主題下的所有子主題
    const { error: subtopicsError } = await supabase.from("subtopics").delete().eq("topic_id", id)

    if (subtopicsError) throw subtopicsError

    // 再刪除主題本身
    const { error } = await supabase.from("topics").delete().eq("id", id)

    if (error) throw error

    return successResponse(null, undefined, "主題已成功刪除")
  } catch (error) {
    return handleError(error)
  }
}

// 刪除子主題
export async function deleteSubtopic(id: string): Promise<ApiResponse<null>> {
  try {
    const supabase = getSupabase()
    const { error } = await supabase.from("subtopics").delete().eq("id", id)

    if (error) throw error

    return successResponse(null, undefined, "子主題已成功刪除")
  } catch (error) {
    return handleError(error)
  }
}

// 獲取主題的卡片數量
export async function getTopicCardCount(topicId: string): Promise<ApiResponse<number>> {
  try {
    const supabase = getSupabase()
    const { count, error } = await supabase.from("card_topics").select("*", { count: "exact" }).eq("topic_id", topicId)

    if (error) throw error

    return successResponse(count || 0)
  } catch (error) {
    return handleError(error)
  }
}

// 獲取子主題的卡片數量
export async function getSubtopicCardCount(subtopicId: string): Promise<ApiResponse<number>> {
  try {
    const supabase = getSupabase()
    const { count, error } = await supabase
      .from("card_subtopics")
      .select("*", { count: "exact" })
      .eq("subtopic_id", subtopicId)

    if (error) throw error

    return successResponse(count || 0)
  } catch (error) {
    return handleError(error)
  }
}
