// 通用類型
export interface ApiResponse<T> {
    success: boolean
    data: T | null
    error?: string
    meta?: {
        total?: number
        page?: number
        limit?: number
    }
}

// 分頁參數
export interface PaginationParams {
    page?: number
    limit?: number
}

// 用戶資料
export interface Profile {
    id: string
    name: string
    email?: string
    avatar?: string
    bio?: string
}

// 主題
export interface Topic {
    id: string
    name: string
    description?: string
    slug?: string
}

// 子主題
export interface Subtopic {
    id: string
    name: string
    description?: string
    topic_id: string
    slug?: string
}

// 評論
export interface Comment {
    id: string
    content: string
    author_id: string
    root_item_id: string
    root_item_type: string
    parent_comment_id?: string
    created_at: string
    updated_at?: string
    author?: Profile
    referenced_card?: any
}

// 卡片統計
export interface CardStats {
    views?: number
    likes?: number
    dislikes?: number
    comments?: number
    replies?: number
    bookmarks?: number
    // 用戶狀態
    hasLiked?: boolean
    hasDisliked?: boolean
    hasBookmarked?: boolean
}

// 卡片
export interface Card {
    id: string
    title: string
    content: string
    author_id: string
    card_type: string
    semantic_type: string
    contribution_type: string
    original_author?: string
    original_url?: string
    created_at: string
    updated_at?: string
    author?: Profile
    topics?: Topic[]
    subtopics?: Subtopic[]
    stats?: CardStats
    comments?: Comment[]
}

// 討論串
export interface Thread {
    id: string
    title: string
    content: string
    author_id: string
    semantic_type: string
    status?: string
    created_at: string
    updated_at?: string
    author?: Profile
    topics?: Topic[]
    subtopics?: Subtopic[]
    stats?: {
        views?: number
        likes?: number
        dislikes?: number
        comments?: number
    }
    replies?: Comment[]
}

// 討論串列表項目
export interface Discussion {
    id: string
    title: string
    content: string
    author_id: string
    semantic_type: string
    created_at: string
    updated_at: string
    author?: Profile
}

// 卡片創建輸入
export interface CardCreateInput {
    title: string
    content: string
    authorId: string
    semanticType: string
    sourceType: string
    originalSource?: string
    topicIds?: string[]
    subtopicIds?: string[]
}

// 卡片更新輸入
export interface CardUpdateInput {
    title?: string
    content?: string
    semanticType?: string
    sourceType?: string
    originalSource?: string
    topicIds?: string[]
    subtopicIds?: string[]
} 