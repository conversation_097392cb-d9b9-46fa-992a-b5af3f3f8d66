import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createServerClient } from '@supabase/ssr'

// 這支 Middleware 的唯一責任：
// 1. 在每次請求時建立 Supabase Server Client
// 2. 透過 getSession() 刷新 (refresh) cookie，確保 RSC / SSR 可以取到最新的 access token
// 3. 不負責路由保護，權限驗證請在各個 page / API 內使用 safeGetUser 處理
export async function middleware(request: NextRequest) {
    // 若環境變數缺失，直接放行
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    if (!supabaseUrl || !supabaseKey) return NextResponse.next()

    // 跳過靜態資源和 Next.js 內部路由
    if (
        request.nextUrl.pathname.startsWith('/_next') ||
        request.nextUrl.pathname.startsWith('/api/_next') ||
        request.nextUrl.pathname.includes('/favicon.ico') ||
        request.nextUrl.pathname.includes('/robots.txt') ||
        request.nextUrl.pathname.includes('/sitemap.xml')
    ) {
        return NextResponse.next()
    }

    const response = NextResponse.next()

    try {
        // 建立 Supabase Server Client
        const supabase = createServerClient(supabaseUrl, supabaseKey, {
            cookies: {
                getAll() {
                    return request.cookies.getAll()
                },
                setAll(cookies) {
                    cookies.forEach(({ name, value, options }) => {
                        response.cookies.set(name, value, {
                            ...options,
                            secure: request.url.startsWith('https:'),
                            sameSite: 'lax',
                            path: '/',
                            httpOnly: false,
                        })
                    })
                },
            },
            auth: {
                storageKey: 'sb-auth-token',
            },
        })

        // 觸發一次 getSession() 以刷新 Cookie - 添加錯誤處理
        try {
            await supabase.auth.getSession()
        } catch (error) {
            // 在生產環境下，忽略 session 錯誤但記錄
            if (process.env.NODE_ENV === 'production') {
                console.debug('Session refresh failed in middleware:', error)
            } else {
                console.warn('Session refresh failed in middleware:', error)
            }
        }

        return response
    } catch (error) {
        // 如果 middleware 出錯，不應該阻止請求
        console.error('Middleware error:', error)
        return NextResponse.next()
    }
}

export const config = {
    matcher: [
        /*
         * Match all request paths except for the ones starting with:
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         * - robots.txt, sitemap.xml (SEO files)
         */
        '/((?!_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml).*)',
    ],
}