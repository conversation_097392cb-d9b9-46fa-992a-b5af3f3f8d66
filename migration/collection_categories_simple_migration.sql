-- ===============================
-- 收藏牆分類功能 - 簡化版 SQL 遷移檔案（無視圖版本）
-- 在 Supabase Console 中執行
-- ===============================

-- 1. 建立收藏牆分類表格（簡化版）
CREATE TABLE IF NOT EXISTS public.collection_categories (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    name text NOT NULL,
    description text,
    sort_order integer DEFAULT 0, -- 排序順序
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    
    -- 確保使用者內的分類名稱唯一
    UNIQUE(user_id, name)
);

-- 2. 為 COLLECTIONS 表格新增 category_id 欄位
ALTER TABLE public.collections 
ADD COLUMN IF NOT EXISTS category_id uuid REFERENCES public.collection_categories(id) ON DELETE SET NULL;

-- 3. 建立必要的索引
CREATE INDEX IF NOT EXISTS idx_collection_categories_user_id ON public.collection_categories(user_id);
CREATE INDEX IF NOT EXISTS idx_collection_categories_sort_order ON public.collection_categories(user_id, sort_order);
CREATE INDEX IF NOT EXISTS idx_collections_category_id ON public.collections(category_id);

-- 4. 為 collection_categories 表建立 updated_at 自動更新觸發器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_collection_categories_updated_at ON public.collection_categories;
CREATE TRIGGER update_collection_categories_updated_at 
    BEFORE UPDATE ON public.collection_categories 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 5. 建立 collections 表的 updated_at 觸發器（如果還沒有的話）
DROP TRIGGER IF EXISTS update_collections_updated_at ON public.collections;
CREATE TRIGGER update_collections_updated_at 
    BEFORE UPDATE ON public.collections 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 6. 設定 RLS (Row Level Security) 政策
ALTER TABLE public.collection_categories ENABLE ROW LEVEL SECURITY;

-- 使用者只能查看、編輯自己的分類
CREATE POLICY "Users can view own collection categories" ON public.collection_categories
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own collection categories" ON public.collection_categories
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own collection categories" ON public.collection_categories
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own collection categories" ON public.collection_categories
    FOR DELETE USING (auth.uid() = user_id);

-- 完成！架構更簡潔，需要分類名稱和項目數量時再透過 JOIN 查詢 