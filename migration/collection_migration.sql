drop table if exists public.COLLECTION_ITEMS;

create table public.COLLECTION_ITEMS (
  id            uuid primary key default uuid_generate_v4(),
  collection_id uuid not null
                 references public.COLLECTIONS(id) on delete cascade,
  item_type     text not null check (item_type in ('card','thread')),
  item_id       uuid not null,
  created_at    timestamptz default now(),
  unique (collection_id, item_type, item_id)
);

-- 建索引：加速「列出某收藏牆」與「反查某卡片／討論串被誰收藏」
create index idx_collection_items_collection_id on public.COLLECTION_ITEMS (collection_id);
create index idx_collection_items_item on public.COLLECTION_ITEMS (item_type, item_id);
-- 複合索引，用於快速檢查重複
create index idx_collection_items_unique_check on public.COLLECTION_ITEMS (collection_id, item_type, item_id);

create or replace function public.verify_collection_item_ref()
returns trigger language plpgsql as $$
declare
  ok boolean;
begin
  if NEW.item_type = 'card' then
    select exists(select 1 from public.CARDS where id = NEW.item_id) into ok;
  elsif NEW.item_type = 'thread' then
    select exists(select 1 from public.THREADS where id = NEW.item_id) into ok;
  else
    raise exception 'Unsupported item_type: %', NEW.item_type;
  end if;

  if not ok then
    raise exception 'Referenced % with id % does not exist', NEW.item_type, NEW.item_id;
  end if;
  
  return NEW;
end;
$$;

create trigger trg_verify_collection_item_ref
before insert or update on public.COLLECTION_ITEMS
for each row execute function public.verify_collection_item_ref();

-- 啟用 RLS
alter table public.COLLECTION_ITEMS enable row level security;

-- RLS 政策：只有收藏牆的擁有者可以操作項目
create policy "collection_items_select_policy"
on public.COLLECTION_ITEMS
for select
using (
  exists (
    select 1 from public.COLLECTIONS c
    where c.id = collection_id
      and c.user_id = auth.uid()
  )
);

create policy "collection_items_insert_policy"
on public.COLLECTION_ITEMS
for insert
with check (
  exists (
    select 1 from public.COLLECTIONS c
    where c.id = collection_id
      and c.user_id = auth.uid()
  )
);

create policy "collection_items_delete_policy"
on public.COLLECTION_ITEMS
for delete
using (
  exists (
    select 1 from public.COLLECTIONS c
    where c.id = collection_id
      and c.user_id = auth.uid()
  )
);

-- 如果需要支援公開收藏牆的檢視
create policy "collection_items_public_select_policy"
on public.COLLECTION_ITEMS
for select
using (
  exists (
    select 1 from public.COLLECTIONS c
    where c.id = collection_id
      and c.is_public = true
  )
);

-- 清理備份表（可選，生產環境建議保留一段時間）
-- drop table if exists public.COLLECTION_ITEMS_BACKUP;
