-- 1. 角色清單
CREATE TABLE ROLES (
  role_key text PRIMARY KEY,          -- 'owner' | 'topic_mod'
  description text
);

-- 2. 使用者 ↔ 角色 (多對多，支援到期日)
CREATE TABLE PROFILE_ROLES (
  profile_id uuid REFERENCES PROFILES(id) ON DELETE CASCADE,
  role_key   text REFERENCES ROLES(role_key) ON DELETE CASCADE,
  granted_at timestamptz DEFAULT now(),
  granted_by uuid REFERENCES PROFILES(id),
  expires_at timestamptz,
  PRIMARY KEY (profile_id, role_key)
);

-- 3. Topic 版主指派表
CREATE TABLE TOPIC_MODERATORS (
  topic_id   uuid REFERENCES TOPICS(id) ON DELETE CASCADE,
  profile_id uuid REFERENCES PROFILES(id) ON DELETE CASCADE,
  granted_at timestamptz DEFAULT now(),
  granted_by uuid REFERENCES PROFILES(id),
  PRIMARY KEY (topic_id, profile_id)
);
