-- ========================================
-- 訂閱功能遷移檔案
-- ========================================

-- 1️⃣ 確保 uuid 擴充已啟用
create extension if not exists "uuid-ossp";

-- 2️⃣ 建立訂閱表
create table if not exists public.content_subscriptions (
  id         uuid primary key default uuid_generate_v4(),
  profile_id uuid not null
               references public.profiles(id) on delete cascade,
  item_type  text not null check (item_type in ('topic', 'subtopic')),
  item_id    uuid not null,              -- 指向 topics / subtopics
  created_at timestamptz default now(),
  unique (profile_id, item_type, item_id) -- 同一人對同一標的只能訂一次
);

-- 3️⃣ 建立索引以優化查詢性能
create index if not exists idx_content_subscriptions_profile_id 
  on public.content_subscriptions (profile_id);

create index if not exists idx_content_subscriptions_item 
  on public.content_subscriptions (item_type, item_id);

create index if not exists idx_content_subscriptions_created_at 
  on public.content_subscriptions (created_at desc);

-- 4️⃣ 建立覆合索引以優化特定查詢
create index if not exists idx_content_subscriptions_profile_item 
  on public.content_subscriptions (profile_id, item_type, item_id);

-- 5️⃣ 開啟 Row-Level Security
alter table public.content_subscriptions enable row level security;

-- 6️⃣ 建立 RLS 政策

-- 用戶可以查看自己的訂閱
create policy "users_can_view_own_subscriptions"
  on public.content_subscriptions
  for select
  using (auth.uid() = profile_id);

-- 用戶可以新增自己的訂閱
create policy "users_can_insert_own_subscriptions"
  on public.content_subscriptions
  for insert
  with check (auth.uid() = profile_id);

-- 用戶可以刪除自己的訂閱
create policy "users_can_delete_own_subscriptions"
  on public.content_subscriptions
  for delete
  using (auth.uid() = profile_id);

-- 允許所有人查看訂閱統計（不包含個人資訊）
-- 這個政策僅用於計數查詢，不會洩露用戶資訊
create policy "public_can_view_subscription_stats"
  on public.content_subscriptions
  for select
  using (true); -- 允許所有人進行統計查詢

-- 7️⃣ 建立函數來檢查項目是否存在
create or replace function public.validate_subscription_item(
  p_item_type text,
  p_item_id uuid
) returns boolean
language plpgsql
security definer
as $$
declare
  item_exists boolean := false;
begin
  if p_item_type = 'topic' then
    select exists(select 1 from public.topics where id = p_item_id) into item_exists;
  elsif p_item_type = 'subtopic' then
    select exists(select 1 from public.subtopics where id = p_item_id) into item_exists;
  end if;
  
  return item_exists;
end;
$$;

-- 8️⃣ 新增檢查約束以確保 item_id 對應的項目存在
-- 注意：這個約束可能會影響性能，可以考慮移除並在應用層處理
-- alter table public.content_subscriptions 
--   add constraint check_valid_subscription_item 
--   check (public.validate_subscription_item(item_type, item_id));

-- 9️⃣ 建立觸發器函數來更新時間戳
create or replace function public.update_content_subscriptions_updated_at()
returns trigger
language plpgsql
as $$
begin
  new.created_at = now();
  return new;
end;
$$;

-- 10️⃣ 建立視圖來方便查詢訂閱詳情
create or replace view public.subscription_details as
select 
  cs.id as subscription_id,
  cs.profile_id,
  cs.item_type,
  cs.item_id,
  cs.created_at as subscribed_at,
  case 
    when cs.item_type = 'topic' then t.name
    when cs.item_type = 'subtopic' then st.name
  end as item_name,
  case 
    when cs.item_type = 'topic' then t.slug
    when cs.item_type = 'subtopic' then st.slug
  end as item_slug,
  case 
    when cs.item_type = 'topic' then t.description
    when cs.item_type = 'subtopic' then st.description
  end as item_description,
  -- 如果是子主題，也包含父主題資訊
  case 
    when cs.item_type = 'subtopic' then pt.id
    else null
  end as parent_topic_id,
  case 
    when cs.item_type = 'subtopic' then pt.name
    else null
  end as parent_topic_name,
  case 
    when cs.item_type = 'subtopic' then pt.slug
    else null
  end as parent_topic_slug
from public.content_subscriptions cs
left join public.topics t on cs.item_type = 'topic' and cs.item_id = t.id
left join public.subtopics st on cs.item_type = 'subtopic' and cs.item_id = st.id
left join public.topics pt on cs.item_type = 'subtopic' and st.topic_id = pt.id;

-- 11️⃣ 建立統計視圖
create or replace view public.subscription_stats as
select 
  item_type,
  item_id,
  count(*) as subscriber_count,
  case 
    when item_type = 'topic' then t.name
    when item_type = 'subtopic' then st.name
  end as item_name
from public.content_subscriptions cs
left join public.topics t on cs.item_type = 'topic' and cs.item_id = t.id
left join public.subtopics st on cs.item_type = 'subtopic' and cs.item_id = st.id
group by item_type, item_id, t.name, st.name;

-- 12️⃣ 給視圖設置 RLS（如果需要）
-- alter view public.subscription_details set (security_barrier = true);
-- alter view public.subscription_stats set (security_barrier = true);

-- 13️⃣ 建立函數來取得用戶的訂閱統計
create or replace function public.get_user_subscription_count(user_id uuid)
returns table(
  total_subscriptions integer,
  topic_subscriptions integer,
  subtopic_subscriptions integer
)
language sql
security definer
as $$
  select 
    count(*)::integer as total_subscriptions,
    count(case when item_type = 'topic' then 1 end)::integer as topic_subscriptions,
    count(case when item_type = 'subtopic' then 1 end)::integer as subtopic_subscriptions
  from public.content_subscriptions
  where profile_id = user_id;
$$;

-- 14️⃣ 建立函數來檢查用戶是否訂閱了特定項目
create or replace function public.is_user_subscribed(
  user_id uuid,
  p_item_type text,
  p_item_id uuid
)
returns boolean
language sql
security definer
as $$
  select exists(
    select 1 
    from public.content_subscriptions 
    where profile_id = user_id 
      and item_type = p_item_type 
      and item_id = p_item_id
  );
$$; 