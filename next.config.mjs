/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  // 為 API 文檔頁面配置，減少 React Strict Mode 警告
  experimental: {
    // 關閉對第三方庫的嚴格模式檢查
    strictNextHead: false,
  },
  // 配置 webpack 來忽略特定的警告
  webpack: (config, { isServer, webpack }) => {
    if (!isServer) {
      // 抑制 React 生命週期警告
      config.resolve.alias = {
        ...config.resolve.alias,
        'react/jsx-runtime': 'react/jsx-runtime',
      }
    }

    // 處理 Supabase realtime-js 的動態依賴警告
    config.module = {
      ...config.module,
      unknownContextCritical: false,
      unknownContextRegExp: /^\.\/.*$/,
      exprContextCritical: false,
    }

    // 忽略特定模塊的動態導入警告 (Next.js 15 優化版本)
    config.ignoreWarnings = [
      ...(config.ignoreWarnings || []),
      // 忽略 Supabase realtime-js 的警告
      (warning) => {
        return (
          warning.module &&
          warning.module.resource &&
          warning.module.resource.includes('@supabase/realtime-js')
        )
      },
      // 忽略動態依賴表達式的警告
      (warning) => {
        return warning.message && warning.message.includes('Critical dependency: the request of a dependency is an expression')
      }
    ]

    return config
  },
}

export default nextConfig
