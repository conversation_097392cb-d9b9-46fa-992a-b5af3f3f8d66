{"openapi": "3.0.0", "info": {"title": "AILogora API", "version": "1.0.0", "description": "AI 論壇平台的 API 文檔", "contact": {"name": "AILogora Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:3000", "description": "開發環境"}, {"url": "https://your-production-url.com", "description": "生產環境"}], "components": {"schemas": {"SuccessResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object"}, "message": {"type": "string", "example": "Request successful"}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string", "example": "Error message"}}}, "Topic": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "example": "人工智慧"}, "slug": {"type": "string", "example": "artificial-intelligence"}, "description": {"type": "string", "example": "討論人工智慧相關的主題"}}}, "Thread": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "title": {"type": "string", "example": "討論串標題"}, "content": {"type": "string", "example": "討論串內容"}, "semantic_type": {"type": "string", "enum": ["question", "discussion", "announcement"], "example": "discussion"}, "status": {"type": "string", "enum": ["draft", "published", "archived"], "example": "published"}, "author_id": {"type": "string", "format": "uuid"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}, "CreateThreadRequest": {"type": "object", "required": ["title", "content", "semanticType"], "properties": {"title": {"type": "string", "example": "新討論串標題"}, "content": {"type": "string", "example": "討論串的詳細內容"}, "semanticType": {"type": "string", "enum": ["question", "discussion", "announcement"], "example": "discussion"}, "topicIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "example": ["123e4567-e89b-12d3-a456-************"]}, "subtopicIds": {"type": "array", "items": {"type": "string", "format": "uuid"}}, "status": {"type": "string", "enum": ["draft", "published"], "default": "published", "example": "published"}}}}, "securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"BearerAuth": []}], "tags": [{"name": "Topics", "description": "主題相關的 API"}, {"name": "Threads", "description": "討論串相關的 API"}, {"name": "<PERSON><PERSON>", "description": "認證相關的 API"}, {"name": "Comments", "description": "評論相關的 API"}, {"name": "Collections", "description": "收藏相關的 API"}], "paths": {"/api/auth/check": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "檢查用戶認證狀態", "description": "檢查當前用戶是否已經登入，並返回用戶基本資訊", "responses": {"200": {"description": "成功檢查認證狀態", "content": {"application/json": {"schema": {"type": "object", "properties": {"authenticated": {"type": "boolean", "example": true, "description": "用戶是否已認證"}, "user": {"type": "object", "nullable": true, "properties": {"id": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}}}}}}}}, "401": {"description": "認證失敗", "content": {"application/json": {"schema": {"type": "object", "properties": {"authenticated": {"type": "boolean", "example": false}, "error": {"type": "string", "example": "Invalid token"}}}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"type": "object", "properties": {"authenticated": {"type": "boolean", "example": false}, "error": {"type": "string", "example": "Internal server error"}}}}}}}}}, "/api/auth/session": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "獲取用戶會話資訊", "description": "獲取當前用戶的會話狀態和詳細資訊", "responses": {"200": {"description": "成功獲取會話資訊", "content": {"application/json": {"schema": {"type": "object", "properties": {"isAuthenticated": {"type": "boolean", "example": true, "description": "用戶是否已認證"}, "user": {"type": "object", "nullable": true, "properties": {"id": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}}}, "session": {"type": "boolean", "example": true, "description": "會話是否有效"}, "error": {"type": "string", "nullable": true, "example": null, "description": "錯誤訊息（如有）"}}}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"type": "object", "properties": {"isAuthenticated": {"type": "boolean", "example": false}, "error": {"type": "string", "example": "Internal server error"}}}}}}}}}, "/api/bookmarks/check": {"get": {"tags": ["Bookmarks"], "summary": "檢查收藏狀態", "description": "檢查指定項目是否已被用戶收藏", "security": [{"BearerAuth": []}], "parameters": [{"in": "query", "name": "itemType", "required": true, "schema": {"type": "string", "enum": ["card", "thread"]}, "description": "項目類型", "example": "card"}, {"in": "query", "name": "itemId", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "項目 ID", "example": "123e4567-e89b-12d3-a456-************"}], "responses": {"200": {"description": "成功檢查收藏狀態", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"isBookmarked": {"type": "boolean", "example": true, "description": "是否已收藏"}}}}}}}}, "400": {"description": "請求參數錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/bookmarks/count": {"get": {"tags": ["Bookmarks"], "summary": "獲取收藏數量", "description": "獲取指定項目的收藏總數", "parameters": [{"in": "query", "name": "itemType", "required": true, "schema": {"type": "string", "enum": ["card", "thread"]}, "description": "項目類型", "example": "card"}, {"in": "query", "name": "itemId", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "項目 ID", "example": "123e4567-e89b-12d3-a456-************"}], "responses": {"200": {"description": "成功獲取收藏數量", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"count": {"type": "integer", "example": 42, "description": "收藏總數"}}}}}}}}, "400": {"description": "請求參數錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/bookmarks": {"get": {"tags": ["Bookmarks"], "summary": "獲取收藏列表", "description": "獲取用戶的收藏內容列表，支援分頁和類型篩選", "security": [{"BearerAuth": []}], "parameters": [{"in": "query", "name": "page", "required": false, "schema": {"type": "integer", "default": 1, "minimum": 1}, "description": "頁碼", "example": 1}, {"in": "query", "name": "limit", "required": false, "schema": {"type": "integer", "default": 10, "minimum": 1, "maximum": 100}, "description": "每頁數量", "example": 10}, {"in": "query", "name": "itemType", "required": false, "schema": {"type": "string", "enum": ["card", "thread", "all"]}, "description": "收藏項目類型篩選", "example": "card"}], "responses": {"200": {"description": "成功獲取收藏列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "itemType": {"type": "string", "enum": ["card", "thread"]}, "title": {"type": "string"}, "content": {"type": "string"}, "author": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "avatar": {"type": "string"}}}, "topics": {"type": "array", "items": {"type": "string"}}, "subtopics": {"type": "array", "items": {"type": "string"}}, "createdAt": {"type": "string", "format": "date-time"}}}}, "pagination": {"type": "object", "properties": {"total": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}}}}}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["Bookmarks"], "summary": "添加收藏", "description": "將指定內容添加到用戶收藏列表", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["itemId", "itemType"], "properties": {"itemId": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************", "description": "要收藏的項目 ID"}, "itemType": {"type": "string", "enum": ["card", "thread"], "example": "card", "description": "收藏項目類型"}}}}}}, "responses": {"200": {"description": "操作成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "請求參數錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "已存在收藏", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["Bookmarks"], "summary": "取消收藏", "description": "從用戶收藏列表中移除指定內容", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["itemId", "itemType"], "properties": {"itemId": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************", "description": "要取消收藏的項目 ID"}, "itemType": {"type": "string", "enum": ["card", "thread"], "example": "card", "description": "收藏項目類型"}}}}}}, "responses": {"200": {"description": "操作成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "請求參數錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/cards/{cardId}/stats": {"get": {"tags": ["Cards"], "summary": "獲取觀點卡統計", "description": "獲取指定觀點卡的統計資訊，包括反應數、評論數等", "parameters": [{"in": "path", "name": "cardId", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "觀點卡 ID", "example": "123e4567-e89b-12d3-a456-************"}], "responses": {"200": {"description": "成功獲取統計資訊", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"likes": {"type": "integer", "example": 15, "description": "讚的數量"}, "dislikes": {"type": "integer", "example": 2, "description": "踩的數量"}, "comments": {"type": "integer", "example": 8, "description": "評論數量"}, "bookmarks": {"type": "integer", "example": 5, "description": "收藏數量"}}}}}}}}, "404": {"description": "觀點卡不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/cards": {"get": {"tags": ["Cards"], "summary": "獲取觀點卡列表", "description": "獲取分頁的觀點卡列表，支援按來源和主題篩選", "parameters": [{"in": "query", "name": "page", "required": false, "schema": {"type": "integer", "default": 1, "minimum": 1}, "description": "頁碼", "example": 1}, {"in": "query", "name": "limit", "required": false, "schema": {"type": "integer", "default": 10, "minimum": 1, "maximum": 100}, "description": "每頁數量", "example": 10}, {"in": "query", "name": "source", "required": false, "schema": {"type": "string", "enum": ["editor", "community", "all"]}, "description": "卡片來源篩選", "example": "community"}, {"in": "query", "name": "topicId", "required": false, "schema": {"type": "string", "format": "uuid"}, "description": "主題 ID 篩選", "example": "123e4567-e89b-12d3-a456-************"}], "responses": {"200": {"description": "成功回應", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Card"}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}, "total": {"type": "integer", "example": 50}}}}}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["Cards"], "summary": "創建新的觀點卡", "description": "創建新的觀點卡並關聯相關主題", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["title", "content", "semanticType"], "properties": {"title": {"type": "string", "example": "AI 技術的未來發展", "description": "觀點卡標題"}, "content": {"type": "string", "example": "人工智能將在未來十年內改變我們的生活方式...", "description": "觀點卡內容"}, "semanticType": {"type": "string", "enum": ["claim", "evidence", "reasoning", "question"], "example": "claim", "description": "語義類型"}, "contributionType": {"type": "string", "enum": ["original", "curated", "translated"], "example": "original", "description": "貢獻類型"}, "originalAuthor": {"type": "string", "example": "張三", "description": "原作者（如果是轉載）"}, "originalUrl": {"type": "string", "format": "uri", "example": "https://example.com/article", "description": "原文鏈接（如果是轉載）"}, "topicIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "example": ["123e4567-e89b-12d3-a456-************"], "description": "關聯的主題 ID 陣列"}, "subtopicIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "example": ["223e4567-e89b-12d3-a456-************"], "description": "關聯的子主題 ID 陣列"}, "status": {"type": "string", "enum": ["pending", "published", "rejected"], "default": "pending", "example": "pending", "description": "卡片狀態"}}}}}}, "responses": {"200": {"description": "成功創建觀點卡", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/Card"}, "message": {"type": "string", "example": "觀點卡創建成功"}}}}}}, "400": {"description": "請求參數錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["Cards"], "summary": "更新觀點卡", "description": "更新指定的觀點卡及其關聯主題", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id", "title", "content", "semanticType"], "properties": {"id": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************", "description": "觀點卡 ID"}, "title": {"type": "string", "example": "AI 技術的未來發展（更新）", "description": "觀點卡標題"}, "content": {"type": "string", "example": "人工智能將在未來十年內改變我們的生活方式...（更新內容）", "description": "觀點卡內容"}, "semanticType": {"type": "string", "enum": ["claim", "evidence", "reasoning", "question"], "example": "claim", "description": "語義類型"}, "contributionType": {"type": "string", "enum": ["original", "curated", "translated"], "example": "original", "description": "貢獻類型"}, "originalAuthor": {"type": "string", "example": "張三", "description": "原作者（如果是轉載）"}, "originalUrl": {"type": "string", "format": "uri", "example": "https://example.com/article", "description": "原文鏈接（如果是轉載）"}, "topicIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "example": ["123e4567-e89b-12d3-a456-************"], "description": "關聯的主題 ID 陣列"}, "subtopicIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "example": ["223e4567-e89b-12d3-a456-************"], "description": "關聯的子主題 ID 陣列"}, "status": {"type": "string", "enum": ["pending", "published", "rejected"], "example": "published", "description": "卡片狀態"}}}}}}, "responses": {"200": {"description": "成功更新觀點卡", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/Card"}, "message": {"type": "string", "example": "觀點卡更新成功"}}}}}}, "400": {"description": "請求參數錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "卡片不存在或無權限編輯", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/collections/{collectionId}": {"get": {"tags": ["Collections"], "summary": "獲取收藏集詳情", "description": "獲取指定收藏集的詳細資訊和項目列表", "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "collectionId", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "收藏集 ID", "example": "123e4567-e89b-12d3-a456-************"}, {"in": "query", "name": "page", "required": false, "schema": {"type": "integer", "default": 1, "minimum": 1}, "description": "頁碼", "example": 1}, {"in": "query", "name": "limit", "required": false, "schema": {"type": "integer", "default": 20, "minimum": 1, "maximum": 100}, "description": "每頁數量", "example": 20}], "responses": {"200": {"description": "成功獲取收藏集詳情", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"collection": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "example": "我的 AI 收藏"}, "description": {"type": "string"}, "coverImage": {"type": "string"}, "itemCount": {"type": "integer"}, "isPublic": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "contentType": {"type": "string", "enum": ["viewpoint", "discussion"]}, "title": {"type": "string"}, "content": {"type": "string"}, "author": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "avatar": {"type": "string"}}}, "topics": {"type": "array", "items": {"type": "string"}}, "addedAt": {"type": "string", "format": "date-time"}}}}, "pagination": {"type": "object", "properties": {"total": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}}}}}}}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "收藏集不存在或無權限訪問", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["Collections"], "summary": "更新收藏集", "description": "更新指定收藏集的資訊", "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "collectionId", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "收藏集 ID", "example": "123e4567-e89b-12d3-a456-************"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "example": "更新後的收藏集名稱", "description": "收藏集名稱"}, "description": {"type": "string", "example": "更新後的描述", "description": "收藏集描述"}, "coverImage": {"type": "string", "example": "/new-cover.png", "description": "封面圖片路徑"}, "isPublic": {"type": "boolean", "description": "是否公開收藏集"}, "categoryId": {"type": "string", "format": "uuid", "description": "分類 ID"}}}}}}, "responses": {"200": {"description": "成功更新收藏集", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "收藏集不存在或無權限訪問", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["Collections"], "summary": "刪除收藏集", "description": "刪除指定的收藏集", "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "collectionId", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "收藏集 ID", "example": "123e4567-e89b-12d3-a456-************"}], "responses": {"200": {"description": "成功刪除收藏集", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "收藏集不存在或無權限訪問", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/collections/categories/{categoryId}": {"get": {"tags": ["Collections"], "summary": "獲取收藏集分類詳情", "description": "獲取指定收藏集分類的詳細資訊", "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "categoryId", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "分類 ID", "example": "123e4567-e89b-12d3-a456-************"}], "responses": {"200": {"description": "成功獲取分類詳情", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "example": "技術相關"}, "description": {"type": "string", "example": "技術和開發相關的收藏"}, "sortOrder": {"type": "integer", "example": 1}, "count": {"type": "integer", "example": 5, "description": "該分類下的收藏集數量"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}}}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "分類不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["Collections"], "summary": "更新收藏集分類", "description": "更新指定收藏集分類的資訊", "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "categoryId", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "分類 ID", "example": "123e4567-e89b-12d3-a456-************"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "example": "更新後的分類名稱", "description": "分類名稱"}, "description": {"type": "string", "example": "更新後的描述", "description": "分類描述"}, "sortOrder": {"type": "integer", "example": 2, "description": "排序順序"}}}}}}, "responses": {"200": {"description": "成功更新分類", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "請求參數錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "分類不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["Collections"], "summary": "刪除收藏集分類", "description": "刪除指定的收藏集分類", "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "categoryId", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "分類 ID", "example": "123e4567-e89b-12d3-a456-************"}], "responses": {"200": {"description": "成功刪除分類", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "分類不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/collections/categories": {"get": {"tags": ["Collections"], "summary": "獲取收藏集分類列表", "description": "獲取用戶的收藏集分類列表，按排序順序返回", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "成功獲取分類列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "example": "技術相關"}, "description": {"type": "string", "example": "技術和開發相關的收藏"}, "sortOrder": {"type": "integer", "example": 1}, "count": {"type": "integer", "example": 5, "description": "該分類下的收藏集數量"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}}}}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["Collections"], "summary": "創建收藏集分類", "description": "創建新的收藏集分類", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "example": "新分類", "description": "分類名稱"}, "description": {"type": "string", "example": "分類描述", "description": "分類描述"}}}}}}, "responses": {"200": {"description": "成功創建分類", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "請求參數錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/collections/items": {"post": {"tags": ["Collections"], "summary": "添加項目到收藏集", "description": "將觀點卡或討論串添加到指定的收藏集中", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["collectionId", "itemId", "itemType"], "properties": {"collectionId": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************", "description": "收藏集 ID"}, "itemId": {"type": "string", "format": "uuid", "example": "456e7890-e89b-12d3-a456-************", "description": "項目 ID"}, "itemType": {"type": "string", "enum": ["card", "thread"], "example": "card", "description": "項目類型"}}}}}}, "responses": {"200": {"description": "成功添加項目到收藏集", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"action": {"type": "string", "example": "added"}, "collectionItem": {"type": "object"}}}, "message": {"type": "string", "example": "項目已添加到「我的收藏」"}}}}}}, "400": {"description": "請求參數錯誤或項目已存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "收藏集不存在或無權限訪問", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["Collections"], "summary": "從收藏集移除項目", "description": "將觀點卡或討論串從指定的收藏集中移除", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["collectionId", "itemId", "itemType"], "properties": {"collectionId": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************", "description": "收藏集 ID"}, "itemId": {"type": "string", "format": "uuid", "example": "456e7890-e89b-12d3-a456-************", "description": "項目 ID"}, "itemType": {"type": "string", "enum": ["card", "thread"], "example": "card", "description": "項目類型"}}}}}}, "responses": {"200": {"description": "成功從收藏集移除項目", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "請求參數錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "收藏集不存在或無權限訪問", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/collections": {"get": {"tags": ["Collections"], "summary": "獲取收藏集列表", "description": "獲取用戶的收藏集列表，支援分頁和分類篩選", "security": [{"BearerAuth": []}], "parameters": [{"in": "query", "name": "page", "required": false, "schema": {"type": "integer", "default": 1, "minimum": 1}, "description": "頁碼", "example": 1}, {"in": "query", "name": "limit", "required": false, "schema": {"type": "integer", "default": 50, "minimum": 1, "maximum": 100}, "description": "每頁數量", "example": 50}, {"in": "query", "name": "categoryId", "required": false, "schema": {"type": "string", "format": "uuid"}, "description": "分類 ID 篩選，使用 \"uncategorized\" 獲取未分類收藏集", "example": "123e4567-e89b-12d3-a456-************"}], "responses": {"200": {"description": "成功獲取收藏集列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "description": {"type": "string"}, "coverImage": {"type": "string"}, "itemCount": {"type": "integer"}, "isPublic": {"type": "boolean"}, "categoryId": {"type": "string", "format": "uuid", "nullable": true}, "categoryName": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}}, "pagination": {"type": "object", "properties": {"total": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}}}}}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["Collections"], "summary": "創建收藏集", "description": "創建新的收藏集", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "example": "我的 AI 收藏", "description": "收藏集名稱"}, "description": {"type": "string", "example": "關於人工智能的精選內容", "description": "收藏集描述"}, "coverImage": {"type": "string", "example": "/abstract-geometric-shapes.png", "description": "封面圖片路徑"}, "isPublic": {"type": "boolean", "default": true, "description": "是否公開收藏集"}, "categoryId": {"type": "string", "format": "uuid", "description": "分類 ID（可選）"}}}}}}, "responses": {"200": {"description": "收藏集創建成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "description": {"type": "string"}, "coverImage": {"type": "string"}, "isPublic": {"type": "boolean"}, "categoryId": {"type": "string", "format": "uuid", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "message": {"type": "string", "example": "收藏牆創建成功"}}}}}}, "400": {"description": "請求參數錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/comments/reactions/batch": {"post": {"tags": ["Comments"], "summary": "批量獲取評論反應", "description": "批量獲取多個評論的反應統計（讚/踩）和用戶的反應狀態", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["commentIds"], "properties": {"commentIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "example": ["123e4567-e89b-12d3-a456-************", "223e4567-e89b-12d3-a456-************"], "description": "評論 ID 陣列"}}}}}}, "responses": {"200": {"description": "成功獲取評論反應資料", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"counts": {"type": "object", "additionalProperties": {"type": "object", "properties": {"likes": {"type": "integer", "example": 5, "description": "讚的數量"}, "dislikes": {"type": "integer", "example": 1, "description": "踩的數量"}}}, "example": {"123e4567-e89b-12d3-a456-************": {"likes": 5, "dislikes": 1}}}, "userReactions": {"type": "object", "additionalProperties": {"type": "object", "properties": {"liked": {"type": "boolean", "example": true, "description": "用戶是否已讚"}, "disliked": {"type": "boolean", "example": false, "description": "用戶是否已踩"}}}, "example": {"123e4567-e89b-12d3-a456-************": {"liked": true, "disliked": false}}}}}}}}}}, "400": {"description": "請求參數錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/debug/cookies": {"get": {"tags": ["Debug"], "summary": "偵錯 Cookies", "description": "檢查當前請求中的 Cookies 資訊，用於偵錯認證相關問題", "responses": {"200": {"description": "成功獲取 Cookies 資訊", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalCookies": {"type": "integer", "example": 15, "description": "總 Cookie 數量"}, "authCookies": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "example": "sb-access-token"}, "valuePreview": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "hasValue": {"type": "boolean", "example": true}}}, "description": "認證相關的 Cookies"}, "allCookieNames": {"type": "array", "items": {"type": "string"}, "example": ["sb-access-token", "sb-refresh-token", "_vercel_jwt"], "description": "所有 Cookie 名稱"}, "requestCookies": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "valuePreview": {"type": "string"}}}, "description": "請求中的 Cookies"}}}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "<PERSON>ie debug failed"}, "message": {"type": "string", "example": "Unknown error"}}}}}}}}}, "/api/debug/topics": {"get": {"tags": ["Debug"], "summary": "偵錯主題資料", "description": "獲取所有主題資料用於偵錯，檢查主題表是否正常", "responses": {"200": {"description": "成功獲取主題資料", "content": {"application/json": {"schema": {"type": "object", "properties": {"count": {"type": "integer", "example": 25, "description": "主題總數"}, "topics": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "example": "人工智能"}, "slug": {"type": "string", "example": "artificial-intelligence"}, "description": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}}}}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Internal server error"}}}}}}}}}, "/api/docs": {"get": {"tags": ["Documentation"], "summary": "獲取 API 文檔規範", "description": "返回 OpenAPI 3.0 格式的 API 文檔規範，用於 Swagger UI 顯示", "responses": {"200": {"description": "成功返回 API 文檔規範", "content": {"application/json": {"schema": {"type": "object", "description": "OpenAPI 3.0 規範對象", "properties": {"openapi": {"type": "string", "example": "3.0.0"}, "info": {"type": "object", "properties": {"title": {"type": "string", "example": "AILogora API"}, "version": {"type": "string", "example": "1.0.0"}}}, "paths": {"type": "object", "description": "API 路徑定義"}, "components": {"type": "object", "description": "可重用的組件定義"}}}}}}, "500": {"description": "生成文檔規範時發生錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/explore": {"get": {"tags": ["Explore"], "summary": "GET /api/explore", "description": "探索相關的 API", "responses": {"200": {"description": "成功回應", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/library/tags": {"get": {"tags": ["Library"], "summary": "獲取收藏庫標籤", "description": "根據用戶收藏的內容分析並返回相關的主題和子主題標籤，按使用頻率排序", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "成功獲取標籤列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "topic-123e4567-e89b-12d3-a456-************", "description": "標籤唯一識別符"}, "name": {"type": "string", "example": "人工智能", "description": "標籤名稱"}, "count": {"type": "integer", "example": 5, "description": "在收藏中出現的次數"}, "type": {"type": "string", "enum": ["topic", "subtopic"], "example": "topic", "description": "標籤類型"}, "originalId": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************", "description": "原始主題或子主題 ID"}}}}, "total": {"type": "integer", "example": 15, "description": "標籤總數"}}}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/my-content/drafts": {"get": {"tags": ["My Content"], "summary": "獲取草稿內容", "description": "根據 ID 和類型獲取用戶的草稿內容（觀點卡或討論串）", "security": [{"BearerAuth": []}], "parameters": [{"in": "query", "name": "type", "required": true, "schema": {"type": "string", "enum": ["card", "thread"]}, "description": "內容類型", "example": "card"}, {"in": "query", "name": "id", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "草稿 ID", "example": "123e4567-e89b-12d3-a456-************"}], "responses": {"200": {"description": "成功獲取草稿內容", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "title": {"type": "string", "example": "草稿標題"}, "content": {"type": "string", "example": "草稿內容"}, "semanticType": {"type": "string", "example": "question"}, "contributionType": {"type": "string", "example": "original", "description": "僅觀點卡有此屬性"}, "originalAuthor": {"type": "string", "description": "僅觀點卡有此屬性"}, "originalUrl": {"type": "string", "description": "僅觀點卡有此屬性"}, "topicIds": {"type": "array", "items": {"type": "string", "format": "uuid"}}, "subtopicIds": {"type": "array", "items": {"type": "string", "format": "uuid"}}, "tags": {"type": "array", "items": {"type": "string"}}, "status": {"type": "string", "example": "draft"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}}}}}}, "400": {"description": "請求參數錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "草稿不存在或無權限訪問", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/my-content": {"get": {"tags": ["My Content"], "summary": "獲取我的內容", "description": "獲取用戶創建的觀點卡和討論串，支援分頁、類型篩選、狀態篩選", "security": [{"BearerAuth": []}], "parameters": [{"in": "query", "name": "type", "required": false, "schema": {"type": "string", "enum": ["cards", "threads", "all"], "default": "all"}, "description": "內容類型篩選", "example": "all"}, {"in": "query", "name": "status", "required": false, "schema": {"type": "string", "enum": ["draft", "pending", "published", "all"], "default": "all"}, "description": "內容狀態篩選", "example": "all"}, {"in": "query", "name": "page", "required": false, "schema": {"type": "integer", "default": 1, "minimum": 1}, "description": "頁碼", "example": 1}, {"in": "query", "name": "limit", "required": false, "schema": {"type": "integer", "default": 10, "minimum": 1, "maximum": 50}, "description": "每頁數量", "example": 10}], "responses": {"200": {"description": "成功獲取我的內容列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "contentType": {"type": "string", "enum": ["viewpoint", "discussion"], "example": "viewpoint"}, "semanticType": {"type": "string", "example": "question"}, "title": {"type": "string", "example": "我的觀點卡標題"}, "content": {"type": "string", "example": "觀點卡的詳細內容"}, "topics": {"type": "array", "items": {"type": "string"}, "example": ["人工智能", "機器學習"]}, "subtopics": {"type": "array", "items": {"type": "string"}, "example": ["深度學習"], "description": "觀點卡專有"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "討論串專有"}, "author": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "avatar": {"type": "string"}}}, "status": {"type": "string", "enum": ["draft", "pending", "published"]}, "stats": {"type": "object", "properties": {"likes": {"type": "integer"}, "dislikes": {"type": "integer", "description": "僅觀點卡有此屬性"}, "comments": {"type": "integer", "description": "僅觀點卡有此屬性"}, "replies": {"type": "integer", "description": "僅討論串有此屬性"}, "views": {"type": "integer", "description": "僅討論串有此屬性"}}}, "submittedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time", "nullable": true}, "lastSaved": {"type": "string", "format": "date-time"}}}}, "pagination": {"type": "object", "properties": {"total": {"type": "integer", "example": 25}, "page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}, "totalPages": {"type": "integer", "example": 3}}}, "summary": {"type": "object", "properties": {"totalCards": {"type": "integer", "example": 15, "description": "觀點卡總數"}, "totalThreads": {"type": "integer", "example": 10, "description": "討論串總數"}, "cardsCount": {"type": "object", "properties": {"draft": {"type": "integer"}, "pending": {"type": "integer"}, "published": {"type": "integer"}}}, "threadsCount": {"type": "object", "properties": {"draft": {"type": "integer"}, "published": {"type": "integer"}}}}}}}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["My Content"], "summary": "刪除我的內容", "description": "刪除用戶創建的觀點卡或討論串（僅限草稿狀態）", "security": [{"BearerAuth": []}], "parameters": [{"in": "query", "name": "type", "required": true, "schema": {"type": "string", "enum": ["card", "thread"]}, "description": "內容類型", "example": "card"}, {"in": "query", "name": "id", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "內容 ID", "example": "123e4567-e89b-12d3-a456-************"}], "responses": {"200": {"description": "成功刪除內容", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "觀點卡已成功刪除"}}}}}}, "400": {"description": "請求參數錯誤或只能刪除草稿", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "無權限刪除此內容", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "內容不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/reactions/check": {"get": {"tags": ["Reactions"], "summary": "檢查反應狀態", "description": "檢查用戶是否已對指定項目進行特定類型的反應", "parameters": [{"in": "query", "name": "itemType", "required": true, "schema": {"type": "string", "enum": ["card", "thread", "comment"]}, "description": "項目類型", "example": "card"}, {"in": "query", "name": "itemId", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "項目 ID", "example": "123e4567-e89b-12d3-a456-************"}, {"in": "query", "name": "reactionType", "required": true, "schema": {"type": "string", "enum": ["like", "dislike", "laugh", "love", "angry"]}, "description": "反應類型", "example": "like"}], "responses": {"200": {"description": "成功檢查反應狀態", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"hasReacted": {"type": "boolean", "example": true, "description": "用戶是否已反應"}}}, "warning": {"type": "string", "description": "當 itemId 格式無效時的警告訊息"}}}}}}, "400": {"description": "請求參數錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/reactions/count": {"get": {"tags": ["Reactions"], "summary": "獲取反應統計", "description": "獲取指定項目的各類型反應統計數量", "parameters": [{"in": "query", "name": "itemType", "required": true, "schema": {"type": "string", "enum": ["card", "thread", "comment"]}, "description": "項目類型", "example": "card"}, {"in": "query", "name": "itemId", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "項目 ID", "example": "123e4567-e89b-12d3-a456-************"}], "responses": {"200": {"description": "成功獲取反應統計", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "additionalProperties": {"type": "integer"}, "example": {"like": 25, "dislike": 3, "love": 8, "laugh": 12}, "description": "各類型反應的統計數量"}, "warning": {"type": "string", "description": "當 itemId 格式無效時的警告訊息"}}}}}}, "400": {"description": "請求參數錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/reactions": {"post": {"tags": ["Reactions"], "summary": "管理內容反應", "description": "對觀點卡或討論串新增或取消反應（點讚/倒讚）。如果用戶已有相同反應，則會取消該反應。對於互斥反應（點讚/倒讚），會自動移除對立反應。", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["itemType", "itemId", "reactionType"], "properties": {"itemType": {"type": "string", "enum": ["card", "thread"], "description": "反應目標類型", "example": "card"}, "itemId": {"type": "string", "format": "uuid", "description": "目標項目的 UUID", "example": "123e4567-e89b-12d3-a456-************"}, "reactionType": {"type": "string", "enum": ["like", "dislike"], "description": "反應類型", "example": "like"}}}}}}, "responses": {"200": {"description": "成功處理反應請求", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"action": {"type": "string", "enum": ["added", "removed"], "description": "執行的動作（新增或移除反應）", "example": "added"}}}}}}}}, "400": {"description": "請求參數錯誤", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string", "enum": ["缺少必要參數", "無效的項目 ID 格式，僅支援 UUID"], "example": "缺少必要參數"}}}}}}, "401": {"description": "未授權", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/subtopics/{subtopicId}/cards": {"get": {"tags": ["Subtopics"], "summary": "獲取子主題下的觀點卡", "description": "獲取指定子主題下的觀點卡列表", "parameters": [{"in": "path", "name": "subtopicId", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "子主題 ID", "example": "123e4567-e89b-12d3-a456-************"}], "responses": {"200": {"description": "成功獲取觀點卡列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Card"}}}}}}}, "404": {"description": "子主題不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/subtopics/popular": {"get": {"tags": ["Subtopics"], "summary": "獲取熱門子主題", "description": "獲取最受歡迎的子主題列表，按熱門程度排序", "parameters": [{"in": "query", "name": "limit", "required": false, "schema": {"type": "integer", "default": 10, "minimum": 1, "maximum": 50}, "description": "限制返回的子主題數量", "example": 10}], "responses": {"200": {"description": "成功獲取熱門子主題列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "example": "機器學習"}, "description": {"type": "string", "example": "人工智能中的機器學習技術"}, "popularityScore": {"type": "number", "example": 95.5, "description": "熱門度分數"}, "cardCount": {"type": "integer", "example": 142, "description": "相關觀點卡數量"}, "threadCount": {"type": "integer", "example": 38, "description": "相關討論串數量"}}}}}}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string", "example": "Failed to fetch popular subtopics"}, "details": {"type": "string", "description": "詳細錯誤訊息"}}}}}}}}}, "/api/subtopics": {"get": {"tags": ["Subtopics"], "summary": "獲取子主題列表", "description": "獲取子主題列表，可選擇性按特定主題篩選。返回子主題的基本資訊及其所屬主題。", "parameters": [{"in": "query", "name": "topicId", "required": false, "schema": {"type": "string", "format": "uuid"}, "description": "主題 ID，如果提供則只返回該主題下的子主題", "example": "123e4567-e89b-12d3-a456-************"}, {"in": "query", "name": "limit", "required": false, "schema": {"type": "integer", "default": 100, "minimum": 1, "maximum": 500}, "description": "返回數量限制", "example": 50}], "responses": {"200": {"description": "成功獲取子主題列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "子主題 ID", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "子主題名稱", "example": "深度學習"}, "slug": {"type": "string", "description": "子主題別名（URL 友善）", "example": "deep-learning"}, "description": {"type": "string", "nullable": true, "description": "子主題描述", "example": "深度學習相關討論"}, "topic_id": {"type": "string", "format": "uuid", "description": "所屬主題 ID", "example": "456e7890-e89b-12d3-a456-************"}, "topics": {"type": "object", "nullable": true, "description": "所屬主題資訊", "properties": {"id": {"type": "string", "format": "uuid", "example": "456e7890-e89b-12d3-a456-************"}, "name": {"type": "string", "example": "人工智能"}, "slug": {"type": "string", "example": "artificial-intelligence"}}}}}}}}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string", "enum": ["Failed to fetch subtopics", "Internal server error"], "example": "Failed to fetch subtopics"}}}}}}}}}, "/api/threads/{threadId}/comments/count": {"get": {"tags": ["Threads"], "summary": "獲取討論串評論數量", "description": "獲取指定討論串的評論總數", "parameters": [{"in": "path", "name": "threadId", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "討論串 ID", "example": "123e4567-e89b-12d3-a456-************"}], "responses": {"200": {"description": "成功獲取評論數量", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"count": {"type": "integer", "example": 25, "description": "評論總數"}}}}}}}}, "404": {"description": "討論串不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/threads/{threadId}/views/count": {"get": {"tags": ["Threads"], "summary": "獲取討論串瀏覽數量", "description": "獲取指定討論串的瀏覽總數", "parameters": [{"in": "path", "name": "threadId", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "討論串 ID", "example": "123e4567-e89b-12d3-a456-************"}], "responses": {"200": {"description": "成功獲取瀏覽數量", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"count": {"type": "integer", "example": 150, "description": "瀏覽總數"}}}}}}}}, "404": {"description": "討論串不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/threads": {"post": {"tags": ["Threads"], "summary": "創建新討論串", "description": "創建一個新的討論串，需要用戶登入", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateThreadRequest"}}}}, "responses": {"200": {"description": "成功創建討論串", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Thread"}}}]}}}}, "400": {"description": "請求參數錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授權 - 用戶未登入", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "get": {"tags": ["Threads"], "summary": "獲取討論串列表", "description": "獲取討論串列表，支援分頁和篩選", "parameters": [{"in": "query", "name": "page", "required": false, "schema": {"type": "integer", "default": 1, "minimum": 1}, "description": "頁碼"}, {"in": "query", "name": "limit", "required": false, "schema": {"type": "integer", "default": 10, "minimum": 1, "maximum": 100}, "description": "每頁顯示數量"}, {"in": "query", "name": "topicId", "required": false, "schema": {"type": "string", "format": "uuid"}, "description": "根據主題 ID 篩選"}, {"in": "query", "name": "status", "required": false, "schema": {"type": "string", "enum": ["draft", "published", "archived"], "default": "published"}, "description": "根據狀態篩選"}], "responses": {"200": {"description": "成功獲取討論串列表", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Thread"}}, "pagination": {"type": "object", "properties": {"total": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}}}}}]}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["Threads"], "summary": "更新討論串", "description": "更新指定的討論串，只有作者可以更新", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id", "title", "content", "semanticType"], "properties": {"id": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************", "description": "討論串 ID"}, "title": {"type": "string", "example": "更新後的討論串標題", "description": "討論串標題"}, "content": {"type": "string", "example": "更新後的討論串內容", "description": "討論串內容"}, "semanticType": {"type": "string", "enum": ["question", "discussion", "announcement"], "example": "discussion", "description": "語義類型"}, "topicIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "example": ["123e4567-e89b-12d3-a456-************"], "description": "關聯的主題 ID 陣列"}, "subtopicIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "example": ["223e4567-e89b-12d3-a456-************"], "description": "關聯的子主題 ID 陣列"}, "status": {"type": "string", "enum": ["draft", "published", "archived"], "example": "published", "description": "討論串狀態"}}}}}}, "responses": {"200": {"description": "成功更新討論串", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/Thread"}, "message": {"type": "string", "example": "討論串更新成功"}}}}}}, "400": {"description": "請求參數錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授權 - 用戶未登入", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "討論串不存在或無權限編輯", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/topics/{topicId}/cards": {"get": {"tags": ["Topics"], "summary": "獲取主題下的觀點卡", "description": "獲取指定主題下的觀點卡列表", "parameters": [{"in": "path", "name": "topicId", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "主題 ID", "example": "123e4567-e89b-12d3-a456-************"}, {"in": "query", "name": "limit", "required": false, "schema": {"type": "integer", "default": 20, "minimum": 1, "maximum": 100}, "description": "限制返回的觀點卡數量", "example": 20}], "responses": {"200": {"description": "成功獲取觀點卡列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Card"}}}}}}}, "404": {"description": "主題不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/topics/{topicId}/threads": {"get": {"tags": ["Topics"], "summary": "獲取主題下的討論串", "description": "獲取指定主題下的討論串列表", "parameters": [{"in": "path", "name": "topicId", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "主題 ID", "example": "123e4567-e89b-12d3-a456-************"}], "responses": {"200": {"description": "成功獲取討論串列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Thread"}}}}}}}, "404": {"description": "主題不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/topics": {"get": {"tags": ["Topics"], "summary": "獲取主題列表", "description": "獲取所有可用的主題，可選擇包含子主題", "parameters": [{"in": "query", "name": "includeSubtopics", "required": false, "schema": {"type": "boolean", "default": false}, "description": "是否包含子主題"}, {"in": "query", "name": "limit", "required": false, "schema": {"type": "integer", "default": 100, "minimum": 1, "maximum": 1000}, "description": "回傳結果的最大數量"}], "responses": {"200": {"description": "成功獲取主題列表", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Topic"}}}}]}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/topics/with-subtopics": {"get": {"tags": ["Topics"], "summary": "獲取主題及其子主題", "description": "獲取所有主題及其關聯的子主題列表，以樹狀結構返回", "responses": {"200": {"description": "成功獲取主題及子主題列表", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "example": "人工智能"}, "description": {"type": "string", "example": "關於人工智能技術的討論"}, "icon": {"type": "string", "example": "🤖"}, "color": {"type": "string", "example": "#3B82F6"}, "subtopics": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "example": "機器學習"}, "description": {"type": "string", "example": "機器學習演算法和應用"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}}}}}, "400": {"description": "請求處理錯誤", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Failed to fetch topics"}}}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Failed to fetch topics"}}}}}}}}}, "/api/trending-topics": {"get": {"tags": ["Topics"], "summary": "獲取熱門主題", "description": "獲取熱門主題列表，依據觀點卡和討論串的數量進行排序。返回前10個最活躍的主題。", "responses": {"200": {"description": "成功獲取熱門主題列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "topics": {"type": "array", "description": "熱門主題列表，按活躍度降序排列", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "主題 ID", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "主題名稱", "example": "人工智能"}, "count": {"type": "integer", "description": "相關內容總數（觀點卡 + 討論串）", "example": 42, "minimum": 0}}}}}}}}}, "500": {"description": "伺服器錯誤", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string", "example": "Failed to fetch trending topics"}}}}}}}}}}}