import { NextResponse } from "next/server"
import { getPopularSubtopics } from "@/lib/topic-service"

/**
 * @swagger
 * /api/subtopics/popular:
 *   get:
 *     tags: [Subtopics]
 *     summary: 獲取熱門子主題
 *     description: 獲取最受歡迎的子主題列表，按熱門程度排序
 *     parameters:
 *       - in: query
 *         name: limit
 *         required: false
 *         schema:
 *           type: integer
 *           default: 10
 *           minimum: 1
 *           maximum: 50
 *         description: 限制返回的子主題數量
 *         example: 10
 *     responses:
 *       200:
 *         description: 成功獲取熱門子主題列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         format: uuid
 *                       name:
 *                         type: string
 *                         example: "機器學習"
 *                       description:
 *                         type: string
 *                         example: "人工智能中的機器學習技術"
 *                       popularityScore:
 *                         type: number
 *                         example: 95.5
 *                         description: 熱門度分數
 *                       cardCount:
 *                         type: integer
 *                         example: 142
 *                         description: 相關觀點卡數量
 *                       threadCount:
 *                         type: integer
 *                         example: 38
 *                         description: 相關討論串數量
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Failed to fetch popular subtopics"
 *                 details:
 *                   type: string
 *                   description: 詳細錯誤訊息
 */

export async function GET(request: Request) {
  try {
    const url = new URL(request.url)
    const limit = url.searchParams.get("limit") ? Number.parseInt(url.searchParams.get("limit") as string) : 10

    const response = await getPopularSubtopics(limit)

    // Always return a proper JSON response
    return NextResponse.json(response)
  } catch (error) {
    console.error("Error fetching popular subtopics:", error)
    // Ensure we return a valid JSON response even for errors
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch popular subtopics",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    )
  }
}
