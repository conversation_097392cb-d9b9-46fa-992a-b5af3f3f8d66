/**
 * @swagger
 * /api/subtopics:
 *   get:
 *     tags: [Subtopics]
 *     summary: 獲取子主題列表
 *     description: 獲取子主題列表，可選擇性按特定主題篩選。返回子主題的基本資訊及其所屬主題。
 *     parameters:
 *       - in: query
 *         name: topicId
 *         required: false
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 主題 ID，如果提供則只返回該主題下的子主題
 *         example: "123e4567-e89b-12d3-a456-************"
 *       - in: query
 *         name: limit
 *         required: false
 *         schema:
 *           type: integer
 *           default: 100
 *           minimum: 1
 *           maximum: 500
 *         description: 返回數量限制
 *         example: 50
 *     responses:
 *       200:
 *         description: 成功獲取子主題列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         format: uuid
 *                         description: 子主題 ID
 *                         example: "123e4567-e89b-12d3-a456-************"
 *                       name:
 *                         type: string
 *                         description: 子主題名稱
 *                         example: "深度學習"
 *                       slug:
 *                         type: string
 *                         description: 子主題別名（URL 友善）
 *                         example: "deep-learning"
 *                       description:
 *                         type: string
 *                         nullable: true
 *                         description: 子主題描述
 *                         example: "深度學習相關討論"
 *                       topic_id:
 *                         type: string
 *                         format: uuid
 *                         description: 所屬主題 ID
 *                         example: "456e7890-e89b-12d3-a456-************"
 *                       topics:
 *                         type: object
 *                         nullable: true
 *                         description: 所屬主題資訊
 *                         properties:
 *                           id:
 *                             type: string
 *                             format: uuid
 *                             example: "456e7890-e89b-12d3-a456-************"
 *                           name:
 *                             type: string
 *                             example: "人工智能"
 *                           slug:
 *                             type: string
 *                             example: "artificial-intelligence"
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   enum: 
 *                     - "Failed to fetch subtopics"
 *                     - "Internal server error"
 *                   example: "Failed to fetch subtopics"
 */
import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

export async function GET(request: NextRequest) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)
        const searchParams = request.nextUrl.searchParams

        const topicId = searchParams.get("topicId")
        const limit = Number.parseInt(searchParams.get("limit") || "100", 10)

        let query = supabase
            .from("subtopics")
            .select(`
        id,
        name,
        slug,
        description,
        topic_id,
        topics:topic_id(id, name, slug)
      `)
            .order("name")
            .limit(limit)

        // 如果指定了 topicId，只獲取該主題下的子主題
        if (topicId) {
            query = query.eq("topic_id", topicId)
        }

        const { data: subtopics, error } = await query

        if (error) {
            console.error("Error fetching subtopics:", error)
            return NextResponse.json(
                { success: false, error: "Failed to fetch subtopics" },
                { status: 500 }
            )
        }

        return NextResponse.json({
            success: true,
            data: subtopics || [],
        })
    } catch (error) {
        console.error("Subtopics API error:", error)
        return NextResponse.json(
            { success: false, error: "Internal server error" },
            { status: 500 }
        )
    }
} 