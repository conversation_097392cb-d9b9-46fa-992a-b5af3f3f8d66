import { NextRequest, NextResponse } from "next/server"
import { getCardsByTopic } from "@/lib/topic-card-service"
import { memoryCache, generateCacheKey } from "@/lib/cache-service"

/**
 * @swagger
 * /api/topics/{topicId}/cards:
 *   get:
 *     tags: [Topics]
 *     summary: 獲取主題下的觀點卡
 *     description: 獲取指定主題下的觀點卡列表
 *     parameters:
 *       - in: path
 *         name: topicId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 主題 ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *       - in: query
 *         name: limit
 *         required: false
 *         schema:
 *           type: integer
 *           default: 20
 *           minimum: 1
 *           maximum: 100
 *         description: 限制返回的觀點卡數量
 *         example: 20
 *     responses:
 *       200:
 *         description: 成功獲取觀點卡列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Card'
 *       404:
 *         description: 主題不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ topicId: string }> }
) {
  try {
    const { topicId } = await params
    const searchParams = request.nextUrl.searchParams
    const limit = Number.parseInt(searchParams.get("limit") || "20", 10)

    // 生成快取鍵
    const cacheKey = generateCacheKey(`topic-cards:${topicId}`, { limit })

    // 檢查快取
    const cached = memoryCache.get(cacheKey)
    if (cached) {
      return NextResponse.json(cached)
    }

    // 使用現有服務但添加快取
    const response = await getCardsByTopic(topicId, limit)

    // 快取結果
    if (response.success) {
      memoryCache.set(cacheKey, response, 5 * 60 * 1000) // 5分鐘快取
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error("Error fetching cards by topic:", error)
    return NextResponse.json(
      { success: false, data: null, error: "Failed to fetch cards" },
      { status: 500 }
    )
  }
}
