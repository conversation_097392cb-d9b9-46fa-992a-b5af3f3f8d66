import { NextResponse } from "next/server"
import { getThreadsByTopic } from "@/lib/thread-service"

/**
 * @swagger
 * /api/topics/{topicId}/threads:
 *   get:
 *     tags: [Topics]
 *     summary: 獲取主題下的討論串
 *     description: 獲取指定主題下的討論串列表
 *     parameters:
 *       - in: path
 *         name: topicId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 主題 ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       200:
 *         description: 成功獲取討論串列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Thread'
 *       404:
 *         description: 主題不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

export async function GET(request: Request, { params }: { params: Promise<{ topicId: string }> }) {
  try {
    const { topicId } = await params
    const response = await getThreadsByTopic(topicId)
    return NextResponse.json(response)
  } catch (error) {
    console.error("Error fetching threads by topic:", error)
    return NextResponse.json({ success: false, data: null, error: "Failed to fetch threads" }, { status: 500 })
  }
}
