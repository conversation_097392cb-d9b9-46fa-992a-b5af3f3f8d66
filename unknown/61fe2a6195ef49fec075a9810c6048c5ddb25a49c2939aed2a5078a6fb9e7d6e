import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

/**
 * @swagger
 * /api/debug/cookies:
 *   get:
 *     tags: [Debug]
 *     summary: 偵錯 Cookies
 *     description: 檢查當前請求中的 Cookies 資訊，用於偵錯認證相關問題
 *     responses:
 *       200:
 *         description: 成功獲取 Cookies 資訊
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 totalCookies:
 *                   type: integer
 *                   example: 15
 *                   description: 總 Cookie 數量
 *                 authCookies:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                         example: "sb-access-token"
 *                       valuePreview:
 *                         type: string
 *                         example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *                       hasValue:
 *                         type: boolean
 *                         example: true
 *                   description: 認證相關的 Cookies
 *                 allCookieNames:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["sb-access-token", "sb-refresh-token", "_vercel_jwt"]
 *                   description: 所有 Cookie 名稱
 *                 requestCookies:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                       valuePreview:
 *                         type: string
 *                   description: 請求中的 Cookies
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Cookie debug failed"
 *                 message:
 *                   type: string
 *                   example: "Unknown error"
 */

export async function GET(request: NextRequest) {
    try {
        const cookieStore = await cookies()
        const allCookies = cookieStore.getAll()

        const authCookies = allCookies.filter(cookie =>
            cookie.name.includes('sb-') || cookie.name.includes('supabase')
        )

        return NextResponse.json({
            totalCookies: allCookies.length,
            authCookies: authCookies.map(c => ({
                name: c.name,
                valuePreview: c.value.slice(0, 50) + '...',
                hasValue: !!c.value
            })),
            allCookieNames: allCookies.map(c => c.name),
            requestCookies: request.cookies.getAll().map(c => ({
                name: c.name,
                valuePreview: c.value.slice(0, 50) + '...'
            }))
        })
    } catch (error) {
        console.error('Cookie debug error:', error)
        return NextResponse.json({
            error: 'Cookie debug failed',
            message: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 500 })
    }
} 