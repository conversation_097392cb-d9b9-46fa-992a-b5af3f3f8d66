import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

/**
 * @swagger
 * /api/bookmarks/count:
 *   get:
 *     tags: [Bookmarks]
 *     summary: 獲取收藏數量
 *     description: 獲取指定項目的收藏總數
 *     parameters:
 *       - in: query
 *         name: itemType
 *         required: true
 *         schema:
 *           type: string
 *           enum: [card, thread]
 *         description: 項目類型
 *         example: "card"
 *       - in: query
 *         name: itemId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 項目 ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       200:
 *         description: 成功獲取收藏數量
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     count:
 *                       type: integer
 *                       example: 42
 *                       description: 收藏總數
 *       400:
 *         description: 請求參數錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

export async function GET(request: Request) {
    try {
        const { searchParams } = new URL(request.url)
        const itemType = searchParams.get("itemType")
        const itemId = searchParams.get("itemId")

        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 驗證必要參數
        if (!itemType || !itemId) {
            return NextResponse.json({ success: false, error: "缺少必要參數" }, { status: 400 })
        }

        // 驗證 itemType
        if (itemType !== "card" && itemType !== "thread") {
            return NextResponse.json({ success: false, error: "itemType 必須是 card 或 thread" }, { status: 400 })
        }

        // 計算收藏數量
        const { count, error } = await supabase
            .from("bookmarks")
            .select("*", { count: "exact", head: true })
            .eq("item_type", itemType)
            .eq("item_id", itemId)

        if (error) {
            console.error("計算收藏數量時出錯:", error)
            return NextResponse.json({ success: false, error: "計算收藏數量時出錯" }, { status: 500 })
        }

        return NextResponse.json({
            success: true,
            data: {
                count: count || 0
            }
        })

    } catch (error) {
        console.error("處理計算收藏數量請求時出錯:", error)
        return NextResponse.json({ success: false, error: "內部伺服器錯誤" }, { status: 500 })
    }
} 