const { withSwagger } = require('next-swagger-doc');

const swaggerDefinition = {
    openapi: '3.0.0',
    info: {
        title: 'AILogora API',
        version: '1.0.0',
        description: 'AI 論壇平台的 API 文檔',
        contact: {
            name: 'AILogora Team',
            email: '<EMAIL>',
        },
    },
    servers: [
        {
            url: 'http://localhost:3000',
            description: '開發環境',
        },
        {
            url: 'https://your-production-url.com',
            description: '生產環境',
        },
    ],
    components: {
        schemas: {
            SuccessResponse: {
                type: 'object',
                properties: {
                    success: {
                        type: 'boolean',
                        example: true,
                    },
                    data: {
                        type: 'object',
                    },
                    message: {
                        type: 'string',
                        example: 'Request successful',
                    },
                },
            },
            ErrorResponse: {
                type: 'object',
                properties: {
                    success: {
                        type: 'boolean',
                        example: false,
                    },
                    error: {
                        type: 'string',
                        example: 'Error message',
                    },
                },
            },
            Topic: {
                type: 'object',
                properties: {
                    id: {
                        type: 'string',
                        format: 'uuid',
                        example: '123e4567-e89b-12d3-a456-************',
                    },
                    name: {
                        type: 'string',
                        example: '人工智慧',
                    },
                    slug: {
                        type: 'string',
                        example: 'artificial-intelligence',
                    },
                    description: {
                        type: 'string',
                        example: '討論人工智慧相關的主題',
                    },
                },
            },
            Thread: {
                type: 'object',
                properties: {
                    id: {
                        type: 'string',
                        format: 'uuid',
                    },
                    title: {
                        type: 'string',
                        example: '討論串標題',
                    },
                    content: {
                        type: 'string',
                        example: '討論串內容',
                    },
                    semantic_type: {
                        type: 'string',
                        enum: ['question', 'discussion', 'announcement'],
                        example: 'discussion',
                    },
                    status: {
                        type: 'string',
                        enum: ['draft', 'published', 'archived'],
                        example: 'published',
                    },
                    author_id: {
                        type: 'string',
                        format: 'uuid',
                    },
                    created_at: {
                        type: 'string',
                        format: 'date-time',
                    },
                    updated_at: {
                        type: 'string',
                        format: 'date-time',
                    },
                },
            },
            CreateThreadRequest: {
                type: 'object',
                required: ['title', 'content', 'semanticType'],
                properties: {
                    title: {
                        type: 'string',
                        example: '新討論串標題',
                    },
                    content: {
                        type: 'string',
                        example: '討論串的詳細內容',
                    },
                    semanticType: {
                        type: 'string',
                        enum: ['question', 'discussion', 'announcement'],
                        example: 'discussion',
                    },
                    topicIds: {
                        type: 'array',
                        items: {
                            type: 'string',
                            format: 'uuid',
                        },
                        example: ['123e4567-e89b-12d3-a456-************'],
                    },
                    subtopicIds: {
                        type: 'array',
                        items: {
                            type: 'string',
                            format: 'uuid',
                        },
                    },
                    status: {
                        type: 'string',
                        enum: ['draft', 'published'],
                        default: 'published',
                        example: 'published',
                    },
                },
            },
            Card: {
                type: 'object',
                properties: {
                    id: {
                        type: 'string',
                        format: 'uuid',
                        example: '123e4567-e89b-12d3-a456-************',
                    },
                    title: {
                        type: 'string',
                        example: 'AI 技術的未來發展',
                    },
                    content: {
                        type: 'string',
                        example: '人工智能將在未來十年內改變我們的生活方式...',
                    },
                    author_id: {
                        type: 'string',
                        format: 'uuid',
                    },
                    card_type: {
                        type: 'string',
                        enum: ['internal', 'external'],
                        example: 'internal',
                    },
                    semantic_type: {
                        type: 'string',
                        enum: ['claim', 'evidence', 'reasoning', 'question'],
                        example: 'claim',
                    },
                    contribution_type: {
                        type: 'string',
                        enum: ['original', 'curated', 'translated'],
                        example: 'original',
                    },
                    original_author: {
                        type: 'string',
                        nullable: true,
                        example: '張三',
                    },
                    original_url: {
                        type: 'string',
                        format: 'uri',
                        nullable: true,
                        example: 'https://example.com/article',
                    },
                    status: {
                        type: 'string',
                        enum: ['pending', 'published', 'rejected'],
                        example: 'published',
                    },
                    published_at: {
                        type: 'string',
                        format: 'date-time',
                        nullable: true,
                    },
                    created_at: {
                        type: 'string',
                        format: 'date-time',
                    },
                    updated_at: {
                        type: 'string',
                        format: 'date-time',
                    },
                },
            },
        },
        securitySchemes: {
            BearerAuth: {
                type: 'http',
                scheme: 'bearer',
                bearerFormat: 'JWT',
            },
        },
    },
    security: [
        {
            BearerAuth: [],
        },
    ],
    tags: [
        {
            name: 'Topics',
            description: '主題相關的 API',
        },
        {
            name: 'Threads',
            description: '討論串相關的 API',
        },
        {
            name: 'Auth',
            description: '認證相關的 API',
        },
        {
            name: 'Comments',
            description: '評論相關的 API',
        },
        {
            name: 'Collections',
            description: '收藏相關的 API',
        },
        {
            name: 'Cards',
            description: '觀點卡相關的 API',
        },
    ],
};

module.exports = swaggerDefinition; 