#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

console.log('🔧 修復 API 文檔註解工具')
console.log('='.repeat(50))

// API 端點的具體修復定義
const fixes = {
    'bookmarks': {
        GET: {
            summary: '獲取用戶收藏列表',
            description: '獲取當前用戶的收藏列表，支援分頁和類型篩選',
            parameters: [
                {
                    name: 'page',
                    in: 'query',
                    required: false,
                    schema: { type: 'integer', default: 1, minimum: 1 },
                    description: '頁碼'
                },
                {
                    name: 'limit',
                    in: 'query',
                    required: false,
                    schema: { type: 'integer', default: 10, minimum: 1, maximum: 100 },
                    description: '每頁數量'
                },
                {
                    name: 'itemType',
                    in: 'query',
                    required: false,
                    schema: { type: 'string', enum: ['card', 'thread', 'all'] },
                    description: '收藏類型篩選'
                }
            ]
        },
        POST: {
            summary: '添加收藏',
            description: '將指定的內容添加到用戶收藏列表',
            requestBody: {
                required: true,
                content: {
                    'application/json': {
                        schema: {
                            type: 'object',
                            required: ['itemId', 'itemType'],
                            properties: {
                                itemId: {
                                    type: 'string',
                                    format: 'uuid',
                                    example: '123e4567-e89b-12d3-a456-************',
                                    description: '要收藏的項目 ID'
                                },
                                itemType: {
                                    type: 'string',
                                    enum: ['card', 'thread'],
                                    example: 'card',
                                    description: '收藏項目類型'
                                }
                            }
                        }
                    }
                }
            }
        },
        DELETE: {
            summary: '取消收藏',
            description: '從用戶收藏列表中移除指定內容',
            requestBody: {
                required: true,
                content: {
                    'application/json': {
                        schema: {
                            type: 'object',
                            required: ['itemId', 'itemType'],
                            properties: {
                                itemId: {
                                    type: 'string',
                                    format: 'uuid',
                                    example: '123e4567-e89b-12d3-a456-************',
                                    description: '要取消收藏的項目 ID'
                                },
                                itemType: {
                                    type: 'string',
                                    enum: ['card', 'thread'],
                                    example: 'card',
                                    description: '收藏項目類型'
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    'collections': {
        GET: {
            summary: '獲取收藏集列表',
            description: '獲取用戶的收藏集列表',
            parameters: [
                {
                    name: 'page',
                    in: 'query',
                    required: false,
                    schema: { type: 'integer', default: 1 },
                    description: '頁碼'
                }
            ]
        },
        POST: {
            summary: '創建收藏集',
            description: '創建新的收藏集',
            requestBody: {
                required: true,
                content: {
                    'application/json': {
                        schema: {
                            type: 'object',
                            required: ['name'],
                            properties: {
                                name: {
                                    type: 'string',
                                    example: '我的 AI 收藏',
                                    description: '收藏集名稱'
                                },
                                description: {
                                    type: 'string',
                                    example: '關於人工智能的精選內容',
                                    description: '收藏集描述'
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

// 為每個需要修復的 API 更新文檔
function updateApiDocs() {
    console.log('🔍 開始修復 API 文檔...')

    Object.keys(fixes).forEach(apiName => {
        const routeFile = path.join(process.cwd(), 'app', 'api', apiName, 'route.ts')

        if (!fs.existsSync(routeFile)) {
            console.log(`⚠️  跳過不存在的文件: ${routeFile}`)
            return
        }

        console.log(`📝 修復 ${apiName} API 文檔...`)

        let content = fs.readFileSync(routeFile, 'utf8')
        const apiFixes = fixes[apiName]

        Object.keys(apiFixes).forEach(method => {
            const methodFix = apiFixes[method]

            // 查找並替換現有的 @swagger 註解
            const oldPattern = new RegExp(
                `\\/\\*\\*[\\s\\S]*?@swagger[\\s\\S]*?\\/api\\/${apiName}:[\\s\\S]*?${method.toLowerCase()}:[\\s\\S]*?\\*\\/`,
                'gm'
            )

            // 生成新的註解
            let newDoc = `/**
 * @swagger
 * /api/${apiName}:
 *   ${method.toLowerCase()}:
 *     tags: [${apiName.charAt(0).toUpperCase() + apiName.slice(1)}]
 *     summary: ${methodFix.summary}
 *     description: ${methodFix.description}`

            // 添加安全性要求
            if (method !== 'GET') {
                newDoc += `
 *     security:
 *       - BearerAuth: []`
            }

            // 添加參數
            if (methodFix.parameters) {
                newDoc += `
 *     parameters:`
                methodFix.parameters.forEach(param => {
                    newDoc += `
 *       - in: ${param.in}
 *         name: ${param.name}
 *         required: ${param.required}
 *         schema:
 *           type: ${param.schema.type}`
                    if (param.schema.default !== undefined) {
                        newDoc += `
 *           default: ${param.schema.default}`
                    }
                    if (param.schema.minimum !== undefined) {
                        newDoc += `
 *           minimum: ${param.schema.minimum}`
                    }
                    if (param.schema.maximum !== undefined) {
                        newDoc += `
 *           maximum: ${param.schema.maximum}`
                    }
                    if (param.schema.enum) {
                        newDoc += `
 *           enum: [${param.schema.enum.map(e => `"${e}"`).join(', ')}]`
                    }
                    newDoc += `
 *         description: ${param.description}`
                })
            }

            // 添加請求體
            if (methodFix.requestBody) {
                const rb = methodFix.requestBody
                newDoc += `
 *     requestBody:
 *       required: ${rb.required}
 *       content:
 *         application/json:
 *           schema:
 *             type: object`
                if (rb.content['application/json'].schema.required) {
                    newDoc += `
 *             required: [${rb.content['application/json'].schema.required.map(r => `"${r}"`).join(', ')}]`
                }
                newDoc += `
 *             properties:`
                Object.keys(rb.content['application/json'].schema.properties).forEach(prop => {
                    const propDef = rb.content['application/json'].schema.properties[prop]
                    newDoc += `
 *               ${prop}:
 *                 type: ${propDef.type}`
                    if (propDef.format) {
                        newDoc += `
 *                 format: ${propDef.format}`
                    }
                    if (propDef.enum) {
                        newDoc += `
 *                 enum: [${propDef.enum.map(e => `"${e}"`).join(', ')}]`
                    }
                    if (propDef.example) {
                        newDoc += `
 *                 example: "${propDef.example}"`
                    }
                    if (propDef.description) {
                        newDoc += `
 *                 description: ${propDef.description}`
                    }
                })
            }

            // 添加回應
            newDoc += `
 *     responses:
 *       200:
 *         description: 操作成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'`

            if (method !== 'GET') {
                newDoc += `
 *       400:
 *         description: 請求參數錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: 未授權
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'`
            }

            newDoc += `
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */`

            // 替換舊的註解
            if (oldPattern.test(content)) {
                content = content.replace(oldPattern, newDoc)
                console.log(`  ✅ 已更新 ${method} 方法文檔`)
            } else {
                console.log(`  ⚠️  找不到 ${method} 方法的舊文檔`)
            }
        })

        // 寫回文件
        fs.writeFileSync(routeFile, content)
        console.log(`  ✅ ${apiName} API 文檔修復完成`)
    })
}

updateApiDocs()

console.log('')
console.log('✨ API 文檔修復完成！')
console.log('💡 現在您的 API 文檔應該包含完整的參數和請求體定義了')
console.log('🔄 請運行 npm run docs:generate 重新生成文檔') 