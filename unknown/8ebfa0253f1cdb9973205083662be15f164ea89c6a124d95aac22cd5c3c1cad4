import { NextResponse } from 'next/server'
import { createSwaggerSpec } from 'next-swagger-doc'

/**
 * @swagger
 * /api/docs:
 *   get:
 *     tags: [Documentation]
 *     summary: 獲取 API 文檔規範
 *     description: 返回 OpenAPI 3.0 格式的 API 文檔規範，用於 Swagger UI 顯示
 *     responses:
 *       200:
 *         description: 成功返回 API 文檔規範
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               description: OpenAPI 3.0 規範對象
 *               properties:
 *                 openapi:
 *                   type: string
 *                   example: "3.0.0"
 *                 info:
 *                   type: object
 *                   properties:
 *                     title:
 *                       type: string
 *                       example: "AILogora API"
 *                     version:
 *                       type: string
 *                       example: "1.0.0"
 *                 paths:
 *                   type: object
 *                   description: API 路徑定義
 *                 components:
 *                   type: object
 *                   description: 可重用的組件定義
 *       500:
 *         description: 生成文檔規範時發生錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

const swaggerDefinition = {
    openapi: '3.0.0',
    info: {
        title: 'AILogora API',
        version: '1.0.0',
        description: 'AI 論壇平台的 API 文檔',
        contact: {
            name: 'AILogora Team',
            email: '<EMAIL>',
        },
    },
    servers: [
        {
            url: process.env.NODE_ENV === 'development'
                ? 'http://localhost:3000'
                : 'https://your-production-url.com',
            description: process.env.NODE_ENV === 'development' ? '開發環境' : '生產環境',
        },
    ],
    components: {
        schemas: {
            SuccessResponse: {
                type: 'object',
                properties: {
                    success: {
                        type: 'boolean',
                        example: true,
                    },
                    data: {
                        type: 'object',
                    },
                    message: {
                        type: 'string',
                        example: 'Request successful',
                    },
                },
            },
            ErrorResponse: {
                type: 'object',
                properties: {
                    success: {
                        type: 'boolean',
                        example: false,
                    },
                    error: {
                        type: 'string',
                        example: 'Error message',
                    },
                },
            },
            Topic: {
                type: 'object',
                properties: {
                    id: {
                        type: 'string',
                        format: 'uuid',
                        example: '123e4567-e89b-12d3-a456-************',
                    },
                    name: {
                        type: 'string',
                        example: '人工智慧',
                    },
                    slug: {
                        type: 'string',
                        example: 'artificial-intelligence',
                    },
                    description: {
                        type: 'string',
                        example: '討論人工智慧相關的主題',
                    },
                },
            },
            Thread: {
                type: 'object',
                properties: {
                    id: {
                        type: 'string',
                        format: 'uuid',
                    },
                    title: {
                        type: 'string',
                        example: '討論串標題',
                    },
                    content: {
                        type: 'string',
                        example: '討論串內容',
                    },
                    semantic_type: {
                        type: 'string',
                        enum: ['question', 'discussion', 'announcement'],
                        example: 'discussion',
                    },
                    status: {
                        type: 'string',
                        enum: ['draft', 'published', 'archived'],
                        example: 'published',
                    },
                    author_id: {
                        type: 'string',
                        format: 'uuid',
                    },
                    created_at: {
                        type: 'string',
                        format: 'date-time',
                    },
                    updated_at: {
                        type: 'string',
                        format: 'date-time',
                    },
                },
            },
            CreateThreadRequest: {
                type: 'object',
                required: ['title', 'content', 'semanticType'],
                properties: {
                    title: {
                        type: 'string',
                        example: '新討論串標題',
                    },
                    content: {
                        type: 'string',
                        example: '討論串的詳細內容',
                    },
                    semanticType: {
                        type: 'string',
                        enum: ['question', 'discussion', 'announcement'],
                        example: 'discussion',
                    },
                    topicIds: {
                        type: 'array',
                        items: {
                            type: 'string',
                            format: 'uuid',
                        },
                        example: ['123e4567-e89b-12d3-a456-************'],
                    },
                    subtopicIds: {
                        type: 'array',
                        items: {
                            type: 'string',
                            format: 'uuid',
                        },
                    },
                    status: {
                        type: 'string',
                        enum: ['draft', 'published'],
                        default: 'published',
                        example: 'published',
                    },
                },
            },
        },
        securitySchemes: {
            BearerAuth: {
                type: 'http',
                scheme: 'bearer',
                bearerFormat: 'JWT',
            },
        },
    },
    security: [
        {
            BearerAuth: [],
        },
    ],
    tags: [
        {
            name: 'Topics',
            description: '主題相關的 API',
        },
        {
            name: 'Threads',
            description: '討論串相關的 API',
        },
        {
            name: 'Auth',
            description: '認證相關的 API',
        },
        {
            name: 'Comments',
            description: '評論相關的 API',
        },
        {
            name: 'Collections',
            description: '收藏相關的 API',
        },
    ],
}

export async function GET() {
    try {
        const spec = createSwaggerSpec({
            definition: swaggerDefinition,
            apiFolder: 'app/api',
        })

        return NextResponse.json(spec)
    } catch (error) {
        console.error('生成 API 文檔時出錯:', error)
        return NextResponse.json(
            { success: false, error: '無法生成 API 文檔' },
            { status: 500 }
        )
    }
} 