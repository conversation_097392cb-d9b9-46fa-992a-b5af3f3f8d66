# API 文檔模板

## 如何為 API 添加文檔

### 1. 基本模板

```typescript
/**
 * @swagger
 * /api/your-endpoint:
 *   get:
 *     tags: [YourTag]
 *     summary: 獲取資料
 *     description: 詳細描述這個端點的功能
 *     parameters:
 *       - in: query
 *         name: id
 *         required: false
 *         schema:
 *           type: string
 *         description: 資源 ID
 *     responses:
 *       200:
 *         description: 成功回應
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
export async function GET(request: NextRequest) {
  // 您的 API 邏輯
}
```

### 2. POST 請求模板

```typescript
/**
 * @swagger
 * /api/your-endpoint:
 *   post:
 *     tags: [YourTag]
 *     summary: 創建資源
 *     description: 創建新的資源
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [name]
 *             properties:
 *               name:
 *                 type: string
 *                 example: "範例名稱"
 *     responses:
 *       200:
 *         description: 成功創建
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       400:
 *         description: 請求參數錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: 未授權
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
export async function POST(request: Request) {
  // 您的 API 邏輯
}
```

### 3. 常用參數類型

- **查詢參數**：
  ```yaml
  parameters:
    - in: query
      name: page
      schema:
        type: integer
        default: 1
      description: 頁碼
  ```

- **路徑參數**：
  ```yaml
  parameters:
    - in: path
      name: id
      required: true
      schema:
        type: string
        format: uuid
      description: 資源 ID
  ```

- **請求體**：
  ```yaml
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/YourSchema'
  ```

### 4. 常用回應類型

- **成功回應**：
  ```yaml
  responses:
    200:
      description: 成功
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/SuccessResponse'
  ```

- **錯誤回應**：
  ```yaml
  responses:
    400:
      description: 請求錯誤
    401:
      description: 未授權
    404:
      description: 資源不存在
    500:
      description: 伺服器錯誤
  ```
