'use client'

import { useState, useEffect } from 'react'

interface ApiEndpoint {
    path: string
    method: string
    summary: string
    description: string
    parameters?: any[]
    requestBody?: any
    responses: any
    tags: string[]
}

interface ApiSpec {
    info: {
        title: string
        version: string
        description: string
    }
    paths: Record<string, Record<string, any>>
    components: {
        schemas: Record<string, any>
    }
}

export default function SimpleApiDocsPage() {
    const [spec, setSpec] = useState<ApiSpec | null>(null)
    const [isLoading, setIsLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [selectedEndpoint, setSelectedEndpoint] = useState<string | null>(null)

    useEffect(() => {
        fetch('/api/docs')
            .then(response => {
                if (!response.ok) {
                    throw new Error('無法載入 API 規範')
                }
                return response.json()
            })
            .then(data => {
                setSpec(data)
                setIsLoading(false)
            })
            .catch(err => {
                setError(err.message)
                setIsLoading(false)
            })
    }, [])

    if (isLoading) {
        return (
            <div className="min-h-screen bg-background flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-lg text-muted-foreground">載入 API 文檔中...</p>
                </div>
            </div>
        )
    }

    if (error) {
        return (
            <div className="min-h-screen bg-background flex items-center justify-center">
                <div className="text-center max-w-md mx-auto p-8">
                    <div className="text-red-500 text-6xl mb-4">⚠️</div>
                    <h1 className="text-2xl font-bold text-foreground mb-2">載入錯誤</h1>
                    <p className="text-muted-foreground mb-4">{error}</p>
                    <button
                        onClick={() => window.location.reload()}
                        className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors"
                    >
                        重新載入
                    </button>
                </div>
            </div>
        )
    }

    if (!spec) return null

    const endpoints: ApiEndpoint[] = []

    Object.entries(spec.paths).forEach(([path, methods]) => {
        Object.entries(methods).forEach(([method, details]: [string, any]) => {
            endpoints.push({
                path,
                method: method.toUpperCase(),
                summary: details.summary || '',
                description: details.description || '',
                parameters: details.parameters || [],
                requestBody: details.requestBody,
                responses: details.responses || {},
                tags: details.tags || []
            })
        })
    })

    const getMethodColor = (method: string) => {
        switch (method) {
            case 'GET': return 'bg-blue-100 text-blue-800 border-blue-200'
            case 'POST': return 'bg-green-100 text-green-800 border-green-200'
            case 'PUT': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
            case 'DELETE': return 'bg-red-100 text-red-800 border-red-200'
            case 'PATCH': return 'bg-purple-100 text-purple-800 border-purple-200'
            default: return 'bg-gray-100 text-gray-800 border-gray-200'
        }
    }

    return (
        <div className="min-h-screen bg-background">
            <div className="container mx-auto py-8">
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-foreground mb-2">{spec.info.title}</h1>
                    <p className="text-muted-foreground mb-2">{spec.info.description}</p>
                    <span className="inline-block bg-muted px-2 py-1 rounded text-sm">
                        版本 {spec.info.version}
                    </span>
                </div>

                <div className="grid lg:grid-cols-4 gap-6">
                    {/* 側邊欄 - API 端點列表 */}
                    <div className="lg:col-span-1">
                        <div className="bg-card rounded-lg border p-4 sticky top-4">
                            <h2 className="font-semibold mb-4">API 端點</h2>
                            <div className="space-y-2">
                                {endpoints.map((endpoint, index) => (
                                    <button
                                        key={`${endpoint.path}-${endpoint.method}`}
                                        onClick={() => setSelectedEndpoint(`${endpoint.path}-${endpoint.method}`)}
                                        className={`w-full text-left p-2 rounded-md border transition-colors ${selectedEndpoint === `${endpoint.path}-${endpoint.method}`
                                                ? 'bg-primary/10 border-primary'
                                                : 'hover:bg-muted'
                                            }`}
                                    >
                                        <div className="flex items-center gap-2 mb-1">
                                            <span className={`px-2 py-1 text-xs font-mono rounded border ${getMethodColor(endpoint.method)}`}>
                                                {endpoint.method}
                                            </span>
                                        </div>
                                        <div className="text-sm font-mono text-muted-foreground">
                                            {endpoint.path}
                                        </div>
                                    </button>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* 主要內容區域 */}
                    <div className="lg:col-span-3">
                        {selectedEndpoint ? (
                            (() => {
                                const endpoint = endpoints.find(e => `${e.path}-${e.method}` === selectedEndpoint)
                                if (!endpoint) return null

                                return (
                                    <div className="bg-card rounded-lg border p-6">
                                        <div className="flex items-center gap-3 mb-4">
                                            <span className={`px-3 py-1 font-mono rounded border ${getMethodColor(endpoint.method)}`}>
                                                {endpoint.method}
                                            </span>
                                            <h2 className="text-xl font-mono">{endpoint.path}</h2>
                                        </div>

                                        <div className="mb-6">
                                            <h3 className="font-semibold mb-2">摘要</h3>
                                            <p className="text-muted-foreground">{endpoint.summary}</p>
                                        </div>

                                        {endpoint.description && (
                                            <div className="mb-6">
                                                <h3 className="font-semibold mb-2">描述</h3>
                                                <p className="text-muted-foreground">{endpoint.description}</p>
                                            </div>
                                        )}

                                        {endpoint.parameters && endpoint.parameters.length > 0 && (
                                            <div className="mb-6">
                                                <h3 className="font-semibold mb-3">參數</h3>
                                                <div className="overflow-x-auto">
                                                    <table className="w-full border rounded">
                                                        <thead>
                                                            <tr className="bg-muted">
                                                                <th className="text-left p-3 border-b">名稱</th>
                                                                <th className="text-left p-3 border-b">位置</th>
                                                                <th className="text-left p-3 border-b">類型</th>
                                                                <th className="text-left p-3 border-b">必需</th>
                                                                <th className="text-left p-3 border-b">描述</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {endpoint.parameters.map((param: any, index: number) => (
                                                                <tr key={index} className="border-b">
                                                                    <td className="p-3 font-mono">{param.name}</td>
                                                                    <td className="p-3">{param.in}</td>
                                                                    <td className="p-3">{param.schema?.type || 'string'}</td>
                                                                    <td className="p-3">
                                                                        {param.required ? (
                                                                            <span className="text-red-600 font-semibold">是</span>
                                                                        ) : (
                                                                            <span className="text-muted-foreground">否</span>
                                                                        )}
                                                                    </td>
                                                                    <td className="p-3 text-muted-foreground">{param.description || '-'}</td>
                                                                </tr>
                                                            ))}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        )}

                                        {endpoint.requestBody && (
                                            <div className="mb-6">
                                                <h3 className="font-semibold mb-3">請求體</h3>
                                                <div className="bg-muted p-4 rounded border">
                                                    <pre className="text-sm overflow-x-auto">
                                                        {JSON.stringify(endpoint.requestBody, null, 2)}
                                                    </pre>
                                                </div>
                                            </div>
                                        )}

                                        <div className="mb-6">
                                            <h3 className="font-semibold mb-3">回應</h3>
                                            <div className="space-y-3">
                                                {Object.entries(endpoint.responses).map(([status, response]: [string, any]) => (
                                                    <div key={status} className="border rounded p-4">
                                                        <div className="flex items-center gap-2 mb-2">
                                                            <span className={`px-2 py-1 text-xs rounded ${status.startsWith('2') ? 'bg-green-100 text-green-800' :
                                                                    status.startsWith('4') ? 'bg-red-100 text-red-800' :
                                                                        'bg-gray-100 text-gray-800'
                                                                }`}>
                                                                {status}
                                                            </span>
                                                            <span className="font-medium">{response.description}</span>
                                                        </div>
                                                        {response.content && (
                                                            <div className="mt-2">
                                                                <div className="text-sm text-muted-foreground mb-1">內容類型：</div>
                                                                <div className="bg-muted p-2 rounded text-sm font-mono">
                                                                    {Object.keys(response.content).join(', ')}
                                                                </div>
                                                            </div>
                                                        )}
                                                    </div>
                                                ))}
                                            </div>
                                        </div>

                                        {endpoint.tags.length > 0 && (
                                            <div>
                                                <h3 className="font-semibold mb-2">標籤</h3>
                                                <div className="flex gap-2">
                                                    {endpoint.tags.map((tag: string) => (
                                                        <span key={tag} className="bg-primary/10 text-primary px-2 py-1 rounded text-sm">
                                                            {tag}
                                                        </span>
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                )
                            })()
                        ) : (
                            <div className="bg-card rounded-lg border p-8 text-center">
                                <h2 className="text-xl font-semibold mb-2">選擇一個 API 端點</h2>
                                <p className="text-muted-foreground">
                                    從左側選擇一個 API 端點來查看詳細資訊
                                </p>
                            </div>
                        )}
                    </div>
                </div>

                <div className="mt-8 bg-muted/50 rounded-lg p-6">
                    <h2 className="text-xl font-semibold mb-4">快速開始</h2>
                    <div className="grid md:grid-cols-2 gap-6">
                        <div>
                            <h3 className="font-medium mb-2">基本 URL</h3>
                            <code className="bg-muted px-2 py-1 rounded text-sm">
                                {process.env.NODE_ENV === 'development'
                                    ? 'http://localhost:3000/api'
                                    : 'https://your-domain.com/api'
                                }
                            </code>
                        </div>
                        <div>
                            <h3 className="font-medium mb-2">認證</h3>
                            <p className="text-sm text-muted-foreground">
                                大部分 API 需要 Bearer Token 認證
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
} 