import { NextResponse } from "next/server"
import { getCardsBySubtopic } from "@/lib/topic-card-service"

/**
 * @swagger
 * /api/subtopics/{subtopicId}/cards:
 *   get:
 *     tags: [Subtopics]
 *     summary: 獲取子主題下的觀點卡
 *     description: 獲取指定子主題下的觀點卡列表
 *     parameters:
 *       - in: path
 *         name: subtopicId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 子主題 ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       200:
 *         description: 成功獲取觀點卡列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Card'
 *       404:
 *         description: 子主題不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

export async function GET(request: Request, { params }: { params: Promise<{ subtopicId: string }> }) {
  try {
    const { subtopicId } = await params
    const response = await getCardsBySubtopic(subtopicId)
    return NextResponse.json(response)
  } catch (error) {
    console.error("Error fetching cards by subtopic:", error)
    return NextResponse.json({ success: false, data: null, error: "Failed to fetch cards" }, { status: 500 })
  }
}
