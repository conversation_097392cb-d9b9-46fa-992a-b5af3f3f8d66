import { NextResponse } from "next/server"
import { getThreadViewCount } from "@/lib/thread-service"

/**
 * @swagger
 * /api/threads/{threadId}/views/count:
 *   get:
 *     tags: [Threads]
 *     summary: 獲取討論串瀏覽數量
 *     description: 獲取指定討論串的瀏覽總數
 *     parameters:
 *       - in: path
 *         name: threadId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 討論串 ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       200:
 *         description: 成功獲取瀏覽數量
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     count:
 *                       type: integer
 *                       example: 150
 *                       description: 瀏覽總數
 *       404:
 *         description: 討論串不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

export async function GET(request: Request, { params }: { params: Promise<{ threadId: string }> }) {
  try {
    const { threadId } = await params
    const response = await getThreadViewCount(threadId)
    return NextResponse.json(response)
  } catch (error) {
    console.error("Error fetching thread view count:", error)
    return NextResponse.json({ success: false, data: null, error: "Failed to fetch view count" }, { status: 500 })
  }
}
