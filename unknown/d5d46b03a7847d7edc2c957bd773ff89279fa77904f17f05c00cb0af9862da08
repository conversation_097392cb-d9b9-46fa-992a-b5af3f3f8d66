const fs = require('fs')
const path = require('path')

// API 端點模板
const templates = {
  auth: {
    tag: 'Auth',
    description: '認證相關的 API',
  },
  comments: {
    tag: 'Comments',
    description: '評論相關的 API',
  },
  collections: {
    tag: 'Collections',
    description: '收藏相關的 API',
  },
  bookmarks: {
    tag: 'Bookmarks',
    description: '書籤相關的 API',
  },
  reactions: {
    tag: 'Reactions',
    description: '反應相關的 API',
  },
  library: {
    tag: 'Library',
    description: '圖書館相關的 API',
  },
  explore: {
    tag: 'Explore',
    description: '探索相關的 API',
  },
  cards: {
    tag: 'Cards',
    description: '卡片相關的 API',
  },
  subtopics: {
    tag: 'Subtopics',
    description: '子主題相關的 API',
  },
}

// 生成基本的 Swagger 註解
function generateSwaggerComment(endpoint, method, template) {
  const methodUpper = method.toUpperCase()
  const methodLower = method.toLowerCase()

  return `/**
 * @swagger
 * ${endpoint}:
 *   ${methodLower}:
 *     tags: [${template.tag}]
 *     summary: ${methodUpper} ${endpoint}
 *     description: ${template.description}
 *     responses:
 *       200:
 *         description: 成功回應
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */`
}

// 遞歸掃描 API 目錄
function scanApiDirectory(dir, basePath = '') {
  const apiDir = path.join(process.cwd(), 'app', 'api', dir)

  if (!fs.existsSync(apiDir)) {
    console.log('🔍 掃描 API 目錄...')
    return scanApiDirectory('', '')
  }

  if (basePath === '') {
    console.log('🔍 掃描 API 目錄...')
  }

  const items = fs.readdirSync(apiDir)

  items.forEach(item => {
    const itemPath = path.join(apiDir, item)
    const stat = fs.statSync(itemPath)

    if (stat.isDirectory() && item !== 'docs') {
      const currentPath = basePath ? `${basePath}/${item}` : item
      const routeFile = path.join(itemPath, 'route.ts')

      if (fs.existsSync(routeFile)) {
        console.log(`📁 發現 API 端點: /api/${currentPath}`)

        // 檢查是否已有文檔註解
        const content = fs.readFileSync(routeFile, 'utf8')

        if (!content.includes('@swagger')) {
          console.log(`  ⚠️  缺少文檔註解: ${routeFile}`)

          // 建議添加文檔的模板
          const templateKey = currentPath.split('/')[0] // 使用頂層目錄作為模板鍵
          const template = templates[templateKey] || {
            tag: currentPath.charAt(0).toUpperCase() + currentPath.slice(1).replace(/[-/]/g, ' '),
            description: `${currentPath} 相關的 API`,
          }

          console.log(`  💡 建議添加以下註解到 ${currentPath}/route.ts:`)
          console.log('')

          // 檢測可能的 HTTP 方法
          const methods = []
          if (content.includes('export async function GET')) methods.push('get')
          if (content.includes('export async function POST')) methods.push('post')
          if (content.includes('export async function PUT')) methods.push('put')
          if (content.includes('export async function DELETE')) methods.push('delete')
          if (content.includes('export async function PATCH')) methods.push('patch')

          methods.forEach(method => {
            console.log(generateSwaggerComment(`/api/${currentPath}`, method, template))
            console.log('')
          })

          console.log('─'.repeat(80))
        } else {
          console.log(`  ✅ 已有文檔註解: ${routeFile}`)
        }
      } else {
        // 遞歸掃描子目錄
        scanApiDirectory(path.join(dir, item), currentPath)
      }
    }
  })
}

// 生成文檔模板檔案
function generateDocTemplate() {
  const templateContent = `# API 文檔模板

## 如何為 API 添加文檔

### 1. 基本模板

\`\`\`typescript
/**
 * @swagger
 * /api/your-endpoint:
 *   get:
 *     tags: [YourTag]
 *     summary: 獲取資料
 *     description: 詳細描述這個端點的功能
 *     parameters:
 *       - in: query
 *         name: id
 *         required: false
 *         schema:
 *           type: string
 *         description: 資源 ID
 *     responses:
 *       200:
 *         description: 成功回應
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
export async function GET(request: NextRequest) {
  // 您的 API 邏輯
}
\`\`\`

### 2. POST 請求模板

\`\`\`typescript
/**
 * @swagger
 * /api/your-endpoint:
 *   post:
 *     tags: [YourTag]
 *     summary: 創建資源
 *     description: 創建新的資源
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [name]
 *             properties:
 *               name:
 *                 type: string
 *                 example: "範例名稱"
 *     responses:
 *       200:
 *         description: 成功創建
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       400:
 *         description: 請求參數錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: 未授權
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
export async function POST(request: Request) {
  // 您的 API 邏輯
}
\`\`\`

### 3. 常用參數類型

- **查詢參數**：
  \`\`\`yaml
  parameters:
    - in: query
      name: page
      schema:
        type: integer
        default: 1
      description: 頁碼
  \`\`\`

- **路徑參數**：
  \`\`\`yaml
  parameters:
    - in: path
      name: id
      required: true
      schema:
        type: string
        format: uuid
      description: 資源 ID
  \`\`\`

- **請求體**：
  \`\`\`yaml
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/YourSchema'
  \`\`\`

### 4. 常用回應類型

- **成功回應**：
  \`\`\`yaml
  responses:
    200:
      description: 成功
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/SuccessResponse'
  \`\`\`

- **錯誤回應**：
  \`\`\`yaml
  responses:
    400:
      description: 請求錯誤
    401:
      description: 未授權
    404:
      description: 資源不存在
    500:
      description: 伺服器錯誤
  \`\`\`
`

  const templatePath = path.join(process.cwd(), 'docs', 'api-doc-template.md')
  fs.writeFileSync(templatePath, templateContent)
  console.log(`📝 已生成文檔模板: ${templatePath}`)
}

console.log('🚀 API 文檔分析工具')
console.log('='.repeat(50))

scanApiDirectory('')
generateDocTemplate()

console.log('')
console.log('✨ 分析完成！')
console.log('💡 請參考上面的建議為您的 API 端點添加文檔註解')
console.log('📖 詳細模板請查看: docs/api-doc-template.md') 