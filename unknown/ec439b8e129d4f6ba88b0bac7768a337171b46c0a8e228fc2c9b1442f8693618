import { NextResponse } from "next/server"
import { getAllTopicsWithSubtopics } from "@/lib/topic-service"

/**
 * @swagger
 * /api/topics/with-subtopics:
 *   get:
 *     tags: [Topics]
 *     summary: 獲取主題及其子主題
 *     description: 獲取所有主題及其關聯的子主題列表，以樹狀結構返回
 *     responses:
 *       200:
 *         description: 成功獲取主題及子主題列表
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     format: uuid
 *                   name:
 *                     type: string
 *                     example: "人工智能"
 *                   description:
 *                     type: string
 *                     example: "關於人工智能技術的討論"
 *                   icon:
 *                     type: string
 *                     example: "🤖"
 *                   color:
 *                     type: string
 *                     example: "#3B82F6"
 *                   subtopics:
 *                     type: array
 *                     items:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                           format: uuid
 *                         name:
 *                           type: string
 *                           example: "機器學習"
 *                         description:
 *                           type: string
 *                           example: "機器學習演算法和應用"
 *                         createdAt:
 *                           type: string
 *                           format: date-time
 *                         updatedAt:
 *                           type: string
 *                           format: date-time
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 *                   updatedAt:
 *                     type: string
 *                     format: date-time
 *       400:
 *         description: 請求處理錯誤
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Failed to fetch topics"
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Failed to fetch topics"
 */

export async function GET() {
  try {
    const response = await getAllTopicsWithSubtopics()

    if (!response.success) {
      return NextResponse.json({ error: response.error }, { status: 400 })
    }

    return NextResponse.json(response.data)
  } catch (error) {
    console.error("Error fetching topics with subtopics:", error)
    return NextResponse.json({ error: "Failed to fetch topics" }, { status: 500 })
  }
}
