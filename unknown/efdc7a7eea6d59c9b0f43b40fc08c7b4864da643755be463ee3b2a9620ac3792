import { NextResponse } from "next/server"
import { getSupabase } from "@/lib/api-utils"

/**
 * @swagger
 * /api/debug/topics:
 *   get:
 *     tags: [Debug]
 *     summary: 偵錯主題資料
 *     description: 獲取所有主題資料用於偵錯，檢查主題表是否正常
 *     responses:
 *       200:
 *         description: 成功獲取主題資料
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 count:
 *                   type: integer
 *                   example: 25
 *                   description: 主題總數
 *                 topics:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         format: uuid
 *                       name:
 *                         type: string
 *                         example: "人工智能"
 *                       slug:
 *                         type: string
 *                         example: "artificial-intelligence"
 *                       description:
 *                         type: string
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                       updated_at:
 *                         type: string
 *                         format: date-time
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */

export async function GET() {
  try {
    const supabase = getSupabase()

    // 獲取所有主題
    const { data: topics, error } = await supabase.from("topics").select("*")

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({
      count: topics?.length || 0,
      topics: topics || [],
    })
  } catch (error) {
    console.error("Error in debug topics API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
