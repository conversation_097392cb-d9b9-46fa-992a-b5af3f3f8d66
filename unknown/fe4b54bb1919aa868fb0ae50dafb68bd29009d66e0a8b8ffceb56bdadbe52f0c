# AILogora API 文檔

這個專案使用自動化工具來生成 OpenAPI/Swagger 規範的 API 文檔。

## 🚀 快速開始

### 查看 API 文檔

1. **啟動開發伺服器**：
   ```bash
   bun node_modules/next/dist/bin/next dev
   ```

2. **訪問 API 文檔頁面**：
   打開瀏覽器並訪問 [http://localhost:3000/api-docs](http://localhost:3000/api-docs)

3. **直接獲取 OpenAPI 規範**：
   訪問 [http://localhost:3000/api/docs](http://localhost:3000/api/docs) 獲取 JSON 格式的 API 規範

### 生成靜態文檔檔案

```bash
# 生成 API 規範 JSON 檔案
bun run docs:generate

# 生成文檔並啟動開發伺服器
bun run docs:dev

# 生成文檔並啟動生產伺服器
bun run docs:serve
```

## 📝 如何為 API 添加文檔

### 1. 在 API 路由中添加 JSDoc 註解

在您的 API 路由檔案中添加 `@swagger` 註解：

```typescript
/**
 * @swagger
 * /api/your-endpoint:
 *   get:
 *     tags: [YourTag]
 *     summary: 端點摘要
 *     description: 詳細描述
 *     parameters:
 *       - in: query
 *         name: paramName
 *         required: false
 *         schema:
 *           type: string
 *         description: 參數描述
 *     responses:
 *       200:
 *         description: 成功回應
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 */
export async function GET(request: NextRequest) {
  // 您的 API 邏輯
}
```

### 2. 定義資料模型

在 `swagger.config.js` 或 `app/api/docs/route.ts` 中的 `components.schemas` 部分定義您的資料模型：

```javascript
schemas: {
  YourModel: {
    type: 'object',
    properties: {
      id: {
        type: 'string',
        format: 'uuid',
        example: '123e4567-e89b-12d3-a456-************',
      },
      name: {
        type: 'string',
        example: '範例名稱',
      },
    },
  },
}
```

### 3. 重新生成文檔

```bash
bun run docs:generate
```

## 🛠️ 配置

### 修改 API 基本資訊

編輯 `swagger.config.js` 或 `app/api/docs/route.ts` 中的 `swaggerDefinition`：

```javascript
const swaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: '您的 API 標題',
    version: '1.0.0',
    description: '您的 API 描述',
    contact: {
      name: '您的團隊',
      email: '<EMAIL>',
    },
  },
  servers: [
    {
      url: 'http://localhost:3000',
      description: '開發環境',
    },
    {
      url: 'https://your-production-url.com',
      description: '生產環境',
    },
  ],
  // ... 其他配置
}
```

### 添加新的標籤

在 `tags` 陣列中添加新的標籤：

```javascript
tags: [
  {
    name: 'YourNewTag',
    description: '您的新標籤描述',
  },
  // ... 其他標籤
],
```

## 📁 檔案結構

```
├── app/
│   ├── api/
│   │   ├── docs/
│   │   │   └── route.ts          # API 文檔端點
│   │   ├── topics/
│   │   │   └── route.ts          # 主題 API（已添加文檔）
│   │   └── threads/
│   │       └── route.ts          # 討論串 API（已添加文檔）
│   └── api-docs/
│       └── page.tsx              # API 文檔 UI 頁面
├── docs/
│   ├── api-spec.json             # 生成的 OpenAPI 規範
│   └── README.md                 # 本檔案
├── scripts/
│   └── generate-api-docs.js      # 文檔生成腳本
└── swagger.config.js             # Swagger 配置檔案
```

## 🎯 功能特色

- ✅ **自動發現 API 端點**：自動掃描 `app/api` 目錄中的路由
- ✅ **互動式文檔**：使用 Swagger UI 提供可測試的 API 文檔
- ✅ **TypeScript 支援**：完整的 TypeScript 類型定義
- ✅ **繁體中文介面**：本地化的使用者介面
- ✅ **即時更新**：開發模式下自動重新生成文檔
- ✅ **多環境支援**：支援開發和生產環境配置

## 🔧 故障排除

### 文檔沒有顯示新的 API

1. 確保您的 API 路由檔案包含正確的 `@swagger` 註解
2. 執行 `bun run docs:generate` 重新生成文檔
3. 重新啟動開發伺服器

### Swagger UI 載入失敗

1. 檢查瀏覽器控制台是否有錯誤訊息
2. 確保 `/api/docs` 端點正常回應
3. 檢查 OpenAPI 規範的 JSON 格式是否正確

### 生成腳本執行失敗

1. 確保所有依賴都已正確安裝：`bun install`
2. 檢查 `scripts/generate-api-docs.js` 檔案是否存在
3. 確保您有寫入 `docs/` 目錄的權限

## 📚 相關資源

- [OpenAPI 規範](https://swagger.io/specification/)
- [Swagger UI 文檔](https://swagger.io/tools/swagger-ui/)
- [next-swagger-doc](https://github.com/jellydn/next-swagger-doc)
- [JSDoc 語法](https://jsdoc.app/)

## 🤝 貢獻

歡迎提交 Issue 和 Pull Request 來改善這個 API 文檔系統！ 