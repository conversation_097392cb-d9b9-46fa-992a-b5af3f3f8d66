const { createSwaggerSpec } = require('next-swagger-doc')
const fs = require('fs')
const path = require('path')

const swaggerDefinition = {
    openapi: '3.0.0',
    info: {
        title: 'AILogora API',
        version: '1.0.0',
        description: 'AI 論壇平台的 API 文檔',
        contact: {
            name: 'AILogora Team',
            email: '<EMAIL>',
        },
    },
    servers: [
        {
            url: 'http://localhost:3000',
            description: '開發環境',
        },
        {
            url: 'https://your-production-url.com',
            description: '生產環境',
        },
    ],
    components: {
        schemas: {
            SuccessResponse: {
                type: 'object',
                properties: {
                    success: {
                        type: 'boolean',
                        example: true,
                    },
                    data: {
                        type: 'object',
                    },
                    message: {
                        type: 'string',
                        example: 'Request successful',
                    },
                },
            },
            ErrorResponse: {
                type: 'object',
                properties: {
                    success: {
                        type: 'boolean',
                        example: false,
                    },
                    error: {
                        type: 'string',
                        example: 'Error message',
                    },
                },
            },
            Topic: {
                type: 'object',
                properties: {
                    id: {
                        type: 'string',
                        format: 'uuid',
                        example: '123e4567-e89b-12d3-a456-************',
                    },
                    name: {
                        type: 'string',
                        example: '人工智慧',
                    },
                    slug: {
                        type: 'string',
                        example: 'artificial-intelligence',
                    },
                    description: {
                        type: 'string',
                        example: '討論人工智慧相關的主題',
                    },
                },
            },
            Thread: {
                type: 'object',
                properties: {
                    id: {
                        type: 'string',
                        format: 'uuid',
                    },
                    title: {
                        type: 'string',
                        example: '討論串標題',
                    },
                    content: {
                        type: 'string',
                        example: '討論串內容',
                    },
                    semantic_type: {
                        type: 'string',
                        enum: ['question', 'discussion', 'announcement'],
                        example: 'discussion',
                    },
                    status: {
                        type: 'string',
                        enum: ['draft', 'published', 'archived'],
                        example: 'published',
                    },
                    author_id: {
                        type: 'string',
                        format: 'uuid',
                    },
                    created_at: {
                        type: 'string',
                        format: 'date-time',
                    },
                    updated_at: {
                        type: 'string',
                        format: 'date-time',
                    },
                },
            },
            CreateThreadRequest: {
                type: 'object',
                required: ['title', 'content', 'semanticType'],
                properties: {
                    title: {
                        type: 'string',
                        example: '新討論串標題',
                    },
                    content: {
                        type: 'string',
                        example: '討論串的詳細內容',
                    },
                    semanticType: {
                        type: 'string',
                        enum: ['question', 'discussion', 'announcement'],
                        example: 'discussion',
                    },
                    topicIds: {
                        type: 'array',
                        items: {
                            type: 'string',
                            format: 'uuid',
                        },
                        example: ['123e4567-e89b-12d3-a456-************'],
                    },
                    subtopicIds: {
                        type: 'array',
                        items: {
                            type: 'string',
                            format: 'uuid',
                        },
                    },
                    status: {
                        type: 'string',
                        enum: ['draft', 'published'],
                        default: 'published',
                        example: 'published',
                    },
                },
            },
        },
        securitySchemes: {
            BearerAuth: {
                type: 'http',
                scheme: 'bearer',
                bearerFormat: 'JWT',
            },
        },
    },
    security: [
        {
            BearerAuth: [],
        },
    ],
    tags: [
        {
            name: 'Topics',
            description: '主題相關的 API',
        },
        {
            name: 'Threads',
            description: '討論串相關的 API',
        },
        {
            name: 'Auth',
            description: '認證相關的 API',
        },
        {
            name: 'Comments',
            description: '評論相關的 API',
        },
        {
            name: 'Collections',
            description: '收藏相關的 API',
        },
    ],
}

try {
    // 生成 Swagger 規範
    const spec = createSwaggerSpec({
        definition: swaggerDefinition,
        apiFolder: 'app/api',
    })

    // 確保 docs 資料夾存在
    const docsDir = path.join(process.cwd(), 'docs')
    if (!fs.existsSync(docsDir)) {
        fs.mkdirSync(docsDir, { recursive: true })
    }

    // 寫入 JSON 檔案
    const outputPath = path.join(docsDir, 'api-spec.json')
    fs.writeFileSync(outputPath, JSON.stringify(spec, null, 2))

    console.log('✅ API 文檔已生成:', outputPath)
    console.log('📊 發現的 API 端點數量:', Object.keys(spec.paths || {}).length)

    // 顯示發現的端點
    if (spec.paths) {
        console.log('\n📋 發現的 API 端點:')
        Object.keys(spec.paths).forEach(path => {
            const methods = Object.keys(spec.paths[path])
            console.log(`  ${path} - ${methods.join(', ').toUpperCase()}`)
        })
    }

} catch (error) {
    console.error('❌ 生成 API 文檔時發生錯誤:', error)
    process.exit(1)
} 