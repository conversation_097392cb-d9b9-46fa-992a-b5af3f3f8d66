'use client'

import { useEffect, useRef } from 'react'
import dynamic from 'next/dynamic'

// 動態載入 Swagger UI
const SwaggerUI = dynamic(() => import('swagger-ui-react'), {
    ssr: false,
    loading: () => (
        <div className="flex items-center justify-center min-h-[400px]">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
    )
})

interface SwaggerWrapperProps {
    spec: any
}

export default function SwaggerWrapper({ spec }: SwaggerWrapperProps) {
    const wrapperRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
        // 暫時抑制 React Strict Mode 警告
        const originalConsoleError = console.error

        console.error = (...args: any[]) => {
            // 忽略 Swagger UI 相關的生命週期警告
            if (
                args[0]?.includes?.('UNSAFE_componentWillReceiveProps') ||
                args[0]?.includes?.('ModelCollapse') ||
                args[0]?.includes?.('OperationContainer')
            ) {
                return
            }
            originalConsoleError(...args)
        }

        return () => {
            console.error = originalConsoleError
        }
    }, [])

    if (!spec) {
        return (
            <div className="flex items-center justify-center min-h-[400px]">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-lg text-muted-foreground">載入 API 規範中...</p>
                </div>
            </div>
        )
    }

    return (
        <div ref={wrapperRef} className="swagger-wrapper">
            <SwaggerUI
                spec={spec}
                tryItOutEnabled={true}
                displayRequestDuration={true}
                docExpansion="list"
                defaultModelsExpandDepth={2}
                displayOperationId={false}
                filter={true}
                showExtensions={true}
                showCommonExtensions={true}
                deepLinking={true}
                persistAuthorization={true}
                supportedSubmitMethods={['get', 'post', 'put', 'delete', 'patch']}
                requestInterceptor={(req: any) => {
                    // 可以在這裡添加全局請求攔截器
                    console.log('API Request:', req)
                    return req
                }}
                responseInterceptor={(res: any) => {
                    // 可以在這裡添加全域回應攔截器
                    console.log('API Response:', res)
                    return res
                }}
            />

            <style jsx global>{`
        .swagger-wrapper .swagger-ui {
          font-family: inherit;
        }
        
        .swagger-wrapper .swagger-ui .topbar {
          display: none;
        }
        
        .swagger-wrapper .swagger-ui .info {
          margin: 20px 0;
        }
        
        .swagger-wrapper .swagger-ui .scheme-container {
          background: transparent;
          box-shadow: none;
          padding: 0;
        }
        
        .swagger-wrapper .swagger-ui .opblock.opblock-post {
          border-color: #10b981;
          background: rgba(16, 185, 129, 0.1);
        }
        
        .swagger-wrapper .swagger-ui .opblock.opblock-get {
          border-color: #3b82f6;
          background: rgba(59, 130, 246, 0.1);
        }
        
        .swagger-wrapper .swagger-ui .opblock.opblock-put {
          border-color: #f59e0b;
          background: rgba(245, 158, 11, 0.1);
        }
        
        .swagger-wrapper .swagger-ui .opblock.opblock-delete {
          border-color: #ef4444;
          background: rgba(239, 68, 68, 0.1);
        }
      `}</style>
        </div>
    )
} 